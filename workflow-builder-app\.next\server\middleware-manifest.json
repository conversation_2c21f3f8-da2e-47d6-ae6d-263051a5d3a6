{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "3a835874723e9faa53cc33004c894e83", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "83bbe04fca8243d6744fe2633bf5970a64a201112c4e9c9848eafde0e85031fd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0ceaf4f6d60cc235450c874256c61d49d6da1c3fc371edb4e5c6b9c8fa668641"}}}, "sortedMiddleware": ["/"], "functions": {}}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Creates a debounced function that delays invoking func until after wait milliseconds\r\n * have elapsed since the last time the debounced function was invoked.\r\n *\r\n * @param func The function to debounce\r\n * @param wait The number of milliseconds to delay\r\n * @returns A debounced function\r\n */\r\nexport function debounce<T extends (...args: any[]) => any>(\r\n  func: T,\r\n  wait: number\r\n): (...args: Parameters<T>) => void {\r\n  let timeout: NodeJS.Timeout | null = null;\r\n\r\n  return function(...args: Parameters<T>) {\r\n    const later = () => {\r\n      timeout = null;\r\n      func(...args);\r\n    };\r\n\r\n    if (timeout !== null) {\r\n      clearTimeout(timeout);\r\n    }\r\n\r\n    timeout = setTimeout(later, wait);\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,SAAS,GAAG,IAAmB;QACpC,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,aAAa;QACf;QAEA,UAAU,WAAW,OAAO;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/types.ts"], "sourcesContent": ["import { Node, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\n\r\n/**\r\n * Validation error codes for all validation rules\r\n */\r\nexport enum ValidationErrorCode {\r\n  // Workflow structure errors\r\n  WORKFLOW_INVALID_JSON = \"WF001\",\r\n  WORKFLOW_MISSING_NODES = \"WF002\",\r\n  WORKFLOW_MISSING_EDGES = \"WF003\",\r\n  WORKFLOW_MISSING_START_NODE = \"WF004\",\r\n  WORKFLOW_DISCONNECTED_NODES = \"WF005\",\r\n  WORKFLOW_CYCLE_DETECTED = \"WF006\",\r\n  WORKFLOW_INVALID_NAME = \"WF007\",\r\n  WORKFLOW_EMPTY = \"WF008\",\r\n  WORKFLOW_USING_FALLBACK_START_NODE = \"WF009\",\r\n\r\n  // Node validation errors\r\n  NODE_MISSING_ID = \"ND001\",\r\n  NODE_MISSING_TYPE = \"ND002\",\r\n  NODE_MISSING_POSITION = \"ND003\",\r\n  NODE_MISSING_DATA = \"ND004\",\r\n  NODE_MISSING_DATA_TYPE = \"ND005\",\r\n  NODE_MISSING_DATA_LABEL = \"ND006\",\r\n  NODE_MISSING_DATA_DEFINITION = \"ND007\",\r\n  NODE_DUPLICATE_ID = \"ND008\",\r\n  NODE_INVALID_POSITION = \"ND009\",\r\n\r\n  // Edge validation errors\r\n  EDGE_MISSING_ID = \"ED001\",\r\n  EDGE_MISSING_SOURCE = \"ED002\",\r\n  EDGE_MISSING_TARGET = \"ED003\",\r\n  EDGE_SOURCE_NOT_FOUND = \"ED004\",\r\n  EDGE_TARGET_NOT_FOUND = \"ED005\",\r\n  EDGE_DUPLICATE_ID = \"ED006\",\r\n  EDGE_SELF_REFERENCE = \"ED007\",\r\n\r\n  // Field validation errors\r\n  FIELD_REQUIRED = \"FD001\",\r\n  FIELD_STRING_LENGTH = \"FD002\",\r\n  FIELD_NUMBER_RANGE = \"FD003\",\r\n  FIELD_PATTERN_MISMATCH = \"FD004\",\r\n  FIELD_MISSING_REQUIRED_KEYS = \"FD005\",\r\n  FIELD_CONNECTED_INPUT = \"FD006\",\r\n}\r\n\r\n/**\r\n * Validation error severity levels\r\n */\r\nexport type ValidationSeverity = \"error\" | \"warning\" | \"info\";\r\n\r\n/**\r\n * Validation error interface\r\n */\r\nexport interface ValidationError {\r\n  code: ValidationErrorCode;\r\n  message: string;\r\n  severity: ValidationSeverity;\r\n  nodeId?: string;\r\n  fieldId?: string;\r\n}\r\n\r\n/**\r\n * Missing field interface\r\n */\r\nexport interface MissingField {\r\n  nodeId: string;\r\n  nodeName: string;\r\n  name: string;\r\n  displayName: string;\r\n  info?: string;\r\n  inputType: string;\r\n  connected_to_start?: boolean;\r\n  directly_connected_to_start?: boolean;\r\n  // Additional properties for all fields collection\r\n  required?: boolean;\r\n  isEmpty?: boolean;\r\n  currentValue?: any;\r\n  options?: Array<string | { value: string; label: string }> | null;\r\n  schema?: {\r\n    type?: string;\r\n    properties?: Record<string, any>;\r\n    required?: string[];\r\n  };\r\n  // Handle connection properties\r\n  is_handle?: boolean;\r\n  is_connected?: boolean;\r\n}\r\n\r\n/**\r\n * Validation result interface\r\n */\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: ValidationError[];\r\n  warnings: ValidationError[];\r\n  infos: ValidationError[];\r\n  missingFields?: MissingField[];\r\n  startNodeId?: string;\r\n  connectedNodes?: Set<string>;\r\n}\r\n\r\n/**\r\n * Workflow validation options\r\n */\r\nexport interface WorkflowValidationOptions {\r\n  validateConnectivity?: boolean;\r\n  collectMissingFields?: boolean;\r\n  validateFieldTypes?: boolean;\r\n  validateCycles?: boolean;\r\n}\r\n\r\n/**\r\n * Validation state for Zustand store\r\n */\r\nexport interface ValidationState {\r\n  isValid: boolean;\r\n  errors: ValidationError[];\r\n  warnings: ValidationError[];\r\n  infos: ValidationError[];\r\n  missingFields: MissingField[];\r\n  startNodeId?: string;\r\n  connectedNodes?: Set<string>;\r\n  isValidating: boolean;\r\n  hasValidated: boolean;\r\n\r\n  // Actions\r\n  validateWorkflow: (\r\n    nodes: Node<WorkflowNodeData>[],\r\n    edges: Edge[],\r\n    options?: WorkflowValidationOptions\r\n  ) => ValidationResult;\r\n  clearValidation: () => void;\r\n}\r\n"], "names": [], "mappings": ";;;AAMO,IAAA,AAAK,6CAAA;IACV,4BAA4B;;;;;;;;;;IAW5B,yBAAyB;;;;;;;;;;IAWzB,yBAAyB;;;;;;;;IASzB,0BAA0B;;;;;;;WAhChB", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/errors.ts"], "sourcesContent": ["import { ValidationError, ValidationErrorCode, ValidationSeverity } from \"./types\";\r\n\r\n/**\r\n * Creates a validation error object\r\n * \r\n * @param code The error code\r\n * @param message The error message\r\n * @param severity The error severity (default: \"error\")\r\n * @param nodeId Optional node ID for node-specific errors\r\n * @param fieldId Optional field ID for field-specific errors\r\n * @returns A ValidationError object\r\n */\r\nexport function createValidationError(\r\n  code: ValidationErrorCode,\r\n  message: string,\r\n  severity: ValidationSeverity = \"error\",\r\n  nodeId?: string,\r\n  fieldId?: string\r\n): ValidationError {\r\n  return {\r\n    code,\r\n    message,\r\n    severity,\r\n    nodeId,\r\n    fieldId,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAYO,SAAS,sBACd,IAAyB,EACzB,OAAe,EACf,WAA+B,OAAO,EACtC,MAAe,EACf,OAAgB;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/nodeValidation.ts"], "sourcesContent": ["import { Node } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { ValidationError, ValidationErrorCode, ValidationResult } from \"./types\";\r\nimport { createValidationError } from \"./errors\";\r\n\r\n/**\r\n * Validates a single node\r\n * \r\n * @param node The node to validate\r\n * @param index The index of the node in the array\r\n * @returns An array of validation errors\r\n */\r\nexport function validateNode(\r\n  node: Node<WorkflowNodeData>,\r\n  index: number\r\n): ValidationError[] {\r\n  const errors: ValidationError[] = [];\r\n\r\n  // Check for required properties\r\n  if (!node.id) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_ID,\r\n        `Node at index ${index} is missing an ID`\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!node.type) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_TYPE,\r\n        `Node ${node.id || `at index ${index}`} is missing a type`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!node.position) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_POSITION,\r\n        `Node ${node.id || `at index ${index}`} is missing a position`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n  } else if (\r\n    typeof node.position !== \"object\" ||\r\n    typeof node.position.x !== \"number\" ||\r\n    typeof node.position.y !== \"number\"\r\n  ) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_INVALID_POSITION,\r\n        `Node ${node.id || `at index ${index}`} has an invalid position`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!node.data) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_DATA,\r\n        `Node ${node.id || `at index ${index}`} is missing data`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n    return errors; // Stop validation if data is missing\r\n  }\r\n\r\n  // Validate data properties\r\n  if (!node.data.type) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_DATA_TYPE,\r\n        `Node ${node.id || `at index ${index}`} is missing data.type`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!node.data.label) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_DATA_LABEL,\r\n        `Node ${node.id || `at index ${index}`} is missing data.label`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!node.data.definition) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_MISSING_DATA_DEFINITION,\r\n        `Node ${node.id || `at index ${index}`} is missing data.definition`,\r\n        \"error\",\r\n        node.id\r\n      )\r\n    );\r\n  }\r\n\r\n  return errors;\r\n}\r\n\r\n/**\r\n * Validates that all nodes have unique IDs\r\n * \r\n * @param nodes The array of nodes to validate\r\n * @returns An array of validation errors\r\n */\r\nexport function validateNodeUniqueness(\r\n  nodes: Node<WorkflowNodeData>[]\r\n): ValidationError[] {\r\n  const errors: ValidationError[] = [];\r\n  const nodeIds = new Set<string>();\r\n  const duplicateIds = new Set<string>();\r\n\r\n  // Find duplicate IDs\r\n  nodes.forEach(node => {\r\n    if (node.id) {\r\n      if (nodeIds.has(node.id)) {\r\n        duplicateIds.add(node.id);\r\n      } else {\r\n        nodeIds.add(node.id);\r\n      }\r\n    }\r\n  });\r\n\r\n  // Create errors for duplicate IDs\r\n  duplicateIds.forEach(id => {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.NODE_DUPLICATE_ID,\r\n        `Duplicate node ID: ${id}`,\r\n        \"error\",\r\n        id\r\n      )\r\n    );\r\n  });\r\n\r\n  return errors;\r\n}\r\n\r\n/**\r\n * Validates all nodes in a workflow\r\n * \r\n * @param nodes The array of nodes to validate\r\n * @returns A validation result\r\n */\r\nexport function validateNodes(\r\n  nodes: Node<WorkflowNodeData>[]\r\n): ValidationResult {\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  // Check if nodes is an array\r\n  if (!Array.isArray(nodes)) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_MISSING_NODES,\r\n        'Workflow must contain a \"nodes\" array'\r\n      )\r\n    );\r\n    return { isValid: false, errors, warnings, infos };\r\n  }\r\n\r\n  // Validate each node\r\n  nodes.forEach((node, index) => {\r\n    const nodeErrors = validateNode(node, index);\r\n    errors.push(...nodeErrors);\r\n  });\r\n\r\n  // Validate node uniqueness\r\n  const uniquenessErrors = validateNodeUniqueness(nodes);\r\n  errors.push(...uniquenessErrors);\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AASO,SAAS,aACd,IAA4B,EAC5B,KAAa;IAEb,MAAM,SAA4B,EAAE;IAEpC,gCAAgC;IAChC,IAAI,CAAC,KAAK,EAAE,EAAE;QACZ,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,eAAe,EACnC,CAAC,cAAc,EAAE,MAAM,iBAAiB,CAAC;IAG/C;IAEA,IAAI,CAAC,KAAK,IAAI,EAAE;QACd,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,iBAAiB,EACrC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,kBAAkB,CAAC,EAC1D,SACA,KAAK,EAAE;IAGb;IAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;QAClB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,qBAAqB,EACzC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAC9D,SACA,KAAK,EAAE;IAGb,OAAO,IACL,OAAO,KAAK,QAAQ,KAAK,YACzB,OAAO,KAAK,QAAQ,CAAC,CAAC,KAAK,YAC3B,OAAO,KAAK,QAAQ,CAAC,CAAC,KAAK,UAC3B;QACA,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,qBAAqB,EACzC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,wBAAwB,CAAC,EAChE,SACA,KAAK,EAAE;IAGb;IAEA,IAAI,CAAC,KAAK,IAAI,EAAE;QACd,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,iBAAiB,EACrC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,gBAAgB,CAAC,EACxD,SACA,KAAK,EAAE;QAGX,OAAO,QAAQ,qCAAqC;IACtD;IAEA,2BAA2B;IAC3B,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;QACnB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,qBAAqB,CAAC,EAC7D,SACA,KAAK,EAAE;IAGb;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;QACpB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,uBAAuB,EAC3C,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAC9D,SACA,KAAK,EAAE;IAGb;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE;QACzB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,4BAA4B,EAChD,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,2BAA2B,CAAC,EACnE,SACA,KAAK,EAAE;IAGb;IAEA,OAAO;AACT;AAQO,SAAS,uBACd,KAA+B;IAE/B,MAAM,SAA4B,EAAE;IACpC,MAAM,UAAU,IAAI;IACpB,MAAM,eAAe,IAAI;IAEzB,qBAAqB;IACrB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,EAAE,EAAE;YACX,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG;gBACxB,aAAa,GAAG,CAAC,KAAK,EAAE;YAC1B,OAAO;gBACL,QAAQ,GAAG,CAAC,KAAK,EAAE;YACrB;QACF;IACF;IAEA,kCAAkC;IAClC,aAAa,OAAO,CAAC,CAAA;QACnB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,iBAAiB,EACrC,CAAC,mBAAmB,EAAE,IAAI,EAC1B,SACA;IAGN;IAEA,OAAO;AACT;AAQO,SAAS,cACd,KAA+B;IAE/B,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,6BAA6B;IAC7B,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;QACzB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C;QAGJ,OAAO;YAAE,SAAS;YAAO;YAAQ;YAAU;QAAM;IACnD;IAEA,qBAAqB;IACrB,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,OAAO,IAAI,IAAI;IACjB;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,uBAAuB;IAChD,OAAO,IAAI,IAAI;IAEf,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/edgeValidation.ts"], "sourcesContent": ["import { Node, Edge } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { ValidationError, ValidationErrorCode, ValidationResult } from \"./types\";\r\nimport { createValidationError } from \"./errors\";\r\n\r\n/**\r\n * Validates a single edge\r\n * \r\n * @param edge The edge to validate\r\n * @param nodes The array of nodes to check against\r\n * @param index The index of the edge in the array\r\n * @returns An array of validation errors\r\n */\r\nexport function validateEdge(\r\n  edge: Edge,\r\n  nodes: Node<WorkflowNodeData>[],\r\n  index: number\r\n): ValidationError[] {\r\n  const errors: ValidationError[] = [];\r\n\r\n  // Check for required properties\r\n  if (!edge.id) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_MISSING_ID,\r\n        `Edge at index ${index} is missing an ID`\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!edge.source) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_MISSING_SOURCE,\r\n        `Edge ${edge.id || `at index ${index}`} is missing a source`,\r\n        \"error\",\r\n        undefined,\r\n        edge.id\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!edge.target) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_MISSING_TARGET,\r\n        `Edge ${edge.id || `at index ${index}`} is missing a target`,\r\n        \"error\",\r\n        undefined,\r\n        edge.id\r\n      )\r\n    );\r\n  }\r\n\r\n  // Skip further validation if any required properties are missing\r\n  if (!edge.source || !edge.target) {\r\n    return errors;\r\n  }\r\n\r\n  // Check if source and target nodes exist\r\n  const sourceNode = nodes.find(node => node.id === edge.source);\r\n  if (!sourceNode) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_SOURCE_NOT_FOUND,\r\n        `Edge ${edge.id || `at index ${index}`} has a non-existent source node: ${edge.source}`,\r\n        \"error\",\r\n        undefined,\r\n        edge.id\r\n      )\r\n    );\r\n  }\r\n\r\n  const targetNode = nodes.find(node => node.id === edge.target);\r\n  if (!targetNode) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_TARGET_NOT_FOUND,\r\n        `Edge ${edge.id || `at index ${index}`} has a non-existent target node: ${edge.target}`,\r\n        \"error\",\r\n        undefined,\r\n        edge.id\r\n      )\r\n    );\r\n  }\r\n\r\n  // Check for self-referencing edges\r\n  if (edge.source === edge.target) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_SELF_REFERENCE,\r\n        `Edge ${edge.id || `at index ${index}`} is self-referencing: ${edge.source} -> ${edge.target}`,\r\n        \"error\",\r\n        undefined,\r\n        edge.id\r\n      )\r\n    );\r\n  }\r\n\r\n  return errors;\r\n}\r\n\r\n/**\r\n * Validates that all edges have unique IDs\r\n * \r\n * @param edges The array of edges to validate\r\n * @returns An array of validation errors\r\n */\r\nexport function validateEdgeUniqueness(edges: Edge[]): ValidationError[] {\r\n  const errors: ValidationError[] = [];\r\n  const edgeIds = new Set<string>();\r\n  const duplicateIds = new Set<string>();\r\n\r\n  // Find duplicate IDs\r\n  edges.forEach(edge => {\r\n    if (edge.id) {\r\n      if (edgeIds.has(edge.id)) {\r\n        duplicateIds.add(edge.id);\r\n      } else {\r\n        edgeIds.add(edge.id);\r\n      }\r\n    }\r\n  });\r\n\r\n  // Create errors for duplicate IDs\r\n  duplicateIds.forEach(id => {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.EDGE_DUPLICATE_ID,\r\n        `Duplicate edge ID: ${id}`,\r\n        \"error\",\r\n        undefined,\r\n        id\r\n      )\r\n    );\r\n  });\r\n\r\n  return errors;\r\n}\r\n\r\n/**\r\n * Validates all edges in a workflow\r\n * \r\n * @param edges The array of edges to validate\r\n * @param nodes The array of nodes to check against\r\n * @returns A validation result\r\n */\r\nexport function validateEdges(\r\n  edges: Edge[],\r\n  nodes: Node<WorkflowNodeData>[]\r\n): ValidationResult {\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  // Check if edges is an array\r\n  if (!Array.isArray(edges)) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_MISSING_EDGES,\r\n        'Workflow must contain an \"edges\" array'\r\n      )\r\n    );\r\n    return { isValid: false, errors, warnings, infos };\r\n  }\r\n\r\n  // Validate each edge\r\n  edges.forEach((edge, index) => {\r\n    const edgeErrors = validateEdge(edge, nodes, index);\r\n    errors.push(...edgeErrors);\r\n  });\r\n\r\n  // Validate edge uniqueness\r\n  const uniquenessErrors = validateEdgeUniqueness(edges);\r\n  errors.push(...uniquenessErrors);\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAUO,SAAS,aACd,IAAU,EACV,KAA+B,EAC/B,KAAa;IAEb,MAAM,SAA4B,EAAE;IAEpC,gCAAgC;IAChC,IAAI,CAAC,KAAK,EAAE,EAAE;QACZ,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,eAAe,EACnC,CAAC,cAAc,EAAE,MAAM,iBAAiB,CAAC;IAG/C;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,EACvC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,oBAAoB,CAAC,EAC5D,SACA,WACA,KAAK,EAAE;IAGb;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,EACvC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,oBAAoB,CAAC,EAC5D,SACA,WACA,KAAK,EAAE;IAGb;IAEA,iEAAiE;IACjE,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,EAAE;QAChC,OAAO;IACT;IAEA,yCAAyC;IACzC,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,MAAM;IAC7D,IAAI,CAAC,YAAY;QACf,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,qBAAqB,EACzC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,iCAAiC,EAAE,KAAK,MAAM,EAAE,EACvF,SACA,WACA,KAAK,EAAE;IAGb;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,MAAM;IAC7D,IAAI,CAAC,YAAY;QACf,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,qBAAqB,EACzC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,iCAAiC,EAAE,KAAK,MAAM,EAAE,EACvF,SACA,WACA,KAAK,EAAE;IAGb;IAEA,mCAAmC;IACnC,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;QAC/B,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,EACvC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,sBAAsB,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE,EAC9F,SACA,WACA,KAAK,EAAE;IAGb;IAEA,OAAO;AACT;AAQO,SAAS,uBAAuB,KAAa;IAClD,MAAM,SAA4B,EAAE;IACpC,MAAM,UAAU,IAAI;IACpB,MAAM,eAAe,IAAI;IAEzB,qBAAqB;IACrB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,EAAE,EAAE;YACX,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG;gBACxB,aAAa,GAAG,CAAC,KAAK,EAAE;YAC1B,OAAO;gBACL,QAAQ,GAAG,CAAC,KAAK,EAAE;YACrB;QACF;IACF;IAEA,kCAAkC;IAClC,aAAa,OAAO,CAAC,CAAA;QACnB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,iBAAiB,EACrC,CAAC,mBAAmB,EAAE,IAAI,EAC1B,SACA,WACA;IAGN;IAEA,OAAO;AACT;AASO,SAAS,cACd,KAAa,EACb,KAA+B;IAE/B,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,6BAA6B;IAC7B,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;QACzB,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C;QAGJ,OAAO;YAAE,SAAS;YAAO;YAAQ;YAAU;QAAM;IACnD;IAEA,qBAAqB;IACrB,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM,OAAO;QAC7C,OAAO,IAAI,IAAI;IACjB;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,uBAAuB;IAChD,OAAO,IAAI,IAAI;IAEf,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/utils.ts"], "sourcesContent": ["import { No<PERSON>, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\n\r\n/**\r\n * Checks if a node is a StartNode using multiple detection methods\r\n *\r\n * @param node The node to check\r\n * @returns True if the node is a StartNode, false otherwise\r\n */\r\nexport function isStartNode(node: Node<WorkflowNodeData>): boolean {\r\n  // Safety check for null/undefined node or data\r\n  if (!node || !node.data) return false;\r\n\r\n  // Log detailed node information for debugging\r\n  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);\r\n  console.log(`[${timestamp}] [isStartNode] Checking if node is a StartNode:`, {\r\n    id: node.id,\r\n    type: node.type,\r\n    dataType: node.data.type,\r\n    originalType: node.data.originalType,\r\n    definitionName: node.data.definition?.name,\r\n    label: node.data.label\r\n  });\r\n\r\n  // Method 1: Check by originalType (primary method)\r\n  if (node.data.originalType === \"StartNode\") {\r\n    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by originalType`);\r\n    return true;\r\n  }\r\n\r\n  // Method 2: Check by definition name\r\n  if (node.data.definition?.name === \"StartNode\") {\r\n    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by definition name`);\r\n    return true;\r\n  }\r\n\r\n  // Method 3: Check by node type\r\n  if (node.type === \"StartNode\" || node.data.type === \"StartNode\") {\r\n    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by node type`);\r\n    return true;\r\n  }\r\n\r\n  // Method 4: Check by label (as a fallback)\r\n  if (node.data.label === \"Start\" || node.data.label === \"StartNode\") {\r\n    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by label`);\r\n    return true;\r\n  }\r\n\r\n  // Method 5: Check by component type and name pattern\r\n  if (node.data.type === \"component\" &&\r\n      node.data.definition?.name &&\r\n      node.data.definition.name.toLowerCase().includes(\"start\")) {\r\n    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by component name pattern`);\r\n    return true;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Finds the StartNode in an array of nodes with detailed logging\r\n *\r\n * @param nodes The array of nodes to search\r\n * @returns The StartNode if found, undefined otherwise\r\n */\r\nexport function findStartNode(nodes: Node<WorkflowNodeData>[]): Node<WorkflowNodeData> | undefined {\r\n  // Safety check for null/undefined nodes array\r\n  if (!nodes || !Array.isArray(nodes)) {\r\n    console.warn('[findStartNode] Nodes array is null, undefined, or not an array');\r\n    return undefined;\r\n  }\r\n\r\n  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);\r\n  console.log(`[${timestamp}] [findStartNode] Searching for StartNode in ${nodes.length} nodes`);\r\n\r\n  // Log all node types for debugging\r\n  console.log(`[${timestamp}] [findStartNode] Node types in workflow:`,\r\n    nodes.map(node => ({\r\n      id: node.id,\r\n      type: node.type,\r\n      dataType: node.data?.type,\r\n      originalType: node.data?.originalType,\r\n      definitionName: node.data?.definition?.name,\r\n      label: node.data?.label\r\n    }))\r\n  );\r\n\r\n  // Find the first node that passes the isStartNode check\r\n  const startNode = nodes.find(isStartNode);\r\n\r\n  if (startNode) {\r\n    console.log(`[${timestamp}] [findStartNode] Found StartNode with ID: ${startNode.id}`);\r\n  } else {\r\n    console.warn(`[${timestamp}] [findStartNode] No StartNode found in workflow with ${nodes.length} nodes`);\r\n  }\r\n\r\n  return startNode;\r\n}\r\n\r\n/**\r\n * Checks if a node is connected to the start node\r\n *\r\n * @param nodeId The ID of the node to check\r\n * @param connectedNodes Set of node IDs connected to the start node\r\n * @returns True if the node is connected, false otherwise\r\n */\r\nexport function isNodeConnected(nodeId: string, connectedNodes: Set<string>): boolean {\r\n  return connectedNodes.has(nodeId);\r\n}\r\n\r\n/**\r\n * Gets nodes directly connected to the start node\r\n *\r\n * @param nodes The array of nodes\r\n * @param edges The array of edges\r\n * @param startNodeId The ID of the start node\r\n * @returns A set of node IDs directly connected to the start node\r\n */\r\nexport function getDirectlyConnectedNodes(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  startNodeId: string\r\n): Set<string> {\r\n  const directlyConnectedNodes = new Set<string>();\r\n\r\n  // Find all edges where the start node is the source\r\n  edges.forEach(edge => {\r\n    if (edge.source === startNodeId) {\r\n      directlyConnectedNodes.add(edge.target);\r\n    }\r\n  });\r\n\r\n  return directlyConnectedNodes;\r\n}\r\n\r\n/**\r\n * Gets fields directly connected to the start node\r\n *\r\n * @param nodes The array of nodes\r\n * @param edges The array of edges\r\n * @param startNodeId The ID of the start node\r\n * @returns A map of node IDs to sets of field names that are directly connected to the start node\r\n */\r\nexport function getDirectlyConnectedFields(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  startNodeId: string\r\n): Map<string, Set<string>> {\r\n  const directlyConnectedFields = new Map<string, Set<string>>();\r\n\r\n  // Find all edges where the start node is the source\r\n  edges.forEach(edge => {\r\n    if (edge.source === startNodeId && edge.target && edge.targetHandle) {\r\n      // Initialize the set if it doesn't exist\r\n      if (!directlyConnectedFields.has(edge.target)) {\r\n        directlyConnectedFields.set(edge.target, new Set<string>());\r\n      }\r\n\r\n      // Add the field name to the set\r\n      const fieldName = edge.targetHandle;\r\n      directlyConnectedFields.get(edge.target)?.add(fieldName);\r\n    }\r\n  });\r\n\r\n  return directlyConnectedFields;\r\n}\r\n\r\n/**\r\n * Gets all nodes connected to the start node using BFS\r\n *\r\n * @param nodes The array of nodes\r\n * @param edges The array of edges\r\n * @param startNodeId The ID of the start node\r\n * @returns A set of node IDs connected to the start node\r\n */\r\nexport function getConnectedNodes(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  startNodeId: string\r\n): Set<string> {\r\n  const connectedNodes = new Set<string>([startNodeId]);\r\n  const queue: string[] = [startNodeId];\r\n\r\n  // Create an adjacency list for faster lookups\r\n  const adjacencyList = new Map<string, string[]>();\r\n\r\n  // Initialize the adjacency list\r\n  nodes.forEach(node => {\r\n    adjacencyList.set(node.id, []);\r\n  });\r\n\r\n  // Populate the adjacency list\r\n  edges.forEach(edge => {\r\n    const sourceNeighbors = adjacencyList.get(edge.source) || [];\r\n    sourceNeighbors.push(edge.target);\r\n    adjacencyList.set(edge.source, sourceNeighbors);\r\n  });\r\n\r\n  // BFS to find all connected nodes\r\n  while (queue.length > 0) {\r\n    const currentNodeId = queue.shift()!;\r\n    const neighbors = adjacencyList.get(currentNodeId) || [];\r\n\r\n    for (const neighborId of neighbors) {\r\n      if (!connectedNodes.has(neighborId)) {\r\n        connectedNodes.add(neighborId);\r\n        queue.push(neighborId);\r\n      }\r\n    }\r\n  }\r\n\r\n  return connectedNodes;\r\n}\r\n\r\n/**\r\n * Detects cycles in the workflow graph using DFS\r\n *\r\n * @param nodes The array of nodes\r\n * @param edges The array of edges\r\n * @returns An array of node IDs involved in cycles\r\n */\r\nexport function detectCycles(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[]\r\n): string[] {\r\n  const adjacencyList = new Map<string, string[]>();\r\n\r\n  // Initialize the adjacency list\r\n  nodes.forEach(node => {\r\n    adjacencyList.set(node.id, []);\r\n  });\r\n\r\n  // Populate the adjacency list\r\n  edges.forEach(edge => {\r\n    const sourceNeighbors = adjacencyList.get(edge.source) || [];\r\n    sourceNeighbors.push(edge.target);\r\n    adjacencyList.set(edge.source, sourceNeighbors);\r\n  });\r\n\r\n  const visited = new Set<string>();\r\n  const recursionStack = new Set<string>();\r\n  const nodesInCycle = new Set<string>();\r\n\r\n  // DFS function to detect cycles\r\n  function dfs(nodeId: string): boolean {\r\n    if (recursionStack.has(nodeId)) {\r\n      nodesInCycle.add(nodeId);\r\n      return true;\r\n    }\r\n\r\n    if (visited.has(nodeId)) {\r\n      return false;\r\n    }\r\n\r\n    visited.add(nodeId);\r\n    recursionStack.add(nodeId);\r\n\r\n    const neighbors = adjacencyList.get(nodeId) || [];\r\n    for (const neighborId of neighbors) {\r\n      if (dfs(neighborId)) {\r\n        nodesInCycle.add(nodeId);\r\n        return true;\r\n      }\r\n    }\r\n\r\n    recursionStack.delete(nodeId);\r\n    return false;\r\n  }\r\n\r\n  // Run DFS from each node\r\n  nodes.forEach(node => {\r\n    if (!visited.has(node.id)) {\r\n      dfs(node.id);\r\n    }\r\n  });\r\n\r\n  return Array.from(nodesInCycle);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AASO,SAAS,YAAY,IAA4B;IACtD,+CAA+C;IAC/C,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,OAAO;IAEhC,8CAA8C;IAC9C,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;IAC1E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,gDAAgD,CAAC,EAAE;QAC3E,IAAI,KAAK,EAAE;QACX,MAAM,KAAK,IAAI;QACf,UAAU,KAAK,IAAI,CAAC,IAAI;QACxB,cAAc,KAAK,IAAI,CAAC,YAAY;QACpC,gBAAgB,KAAK,IAAI,CAAC,UAAU,EAAE;QACtC,OAAO,KAAK,IAAI,CAAC,KAAK;IACxB;IAEA,mDAAmD;IACnD,IAAI,KAAK,IAAI,CAAC,YAAY,KAAK,aAAa;QAC1C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,EAAE,KAAK,EAAE,CAAC,wCAAwC,CAAC;QAClG,OAAO;IACT;IAEA,qCAAqC;IACrC,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,SAAS,aAAa;QAC9C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,EAAE,KAAK,EAAE,CAAC,2CAA2C,CAAC;QACrG,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,CAAC,IAAI,KAAK,aAAa;QAC/D,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,EAAE,KAAK,EAAE,CAAC,qCAAqC,CAAC;QAC/F,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,aAAa;QAClE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,EAAE,KAAK,EAAE,CAAC,iCAAiC,CAAC;QAC3F,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,eACnB,KAAK,IAAI,CAAC,UAAU,EAAE,QACtB,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU;QAC7D,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,EAAE,KAAK,EAAE,CAAC,kDAAkD,CAAC;QAC5G,OAAO;IACT;IAEA,OAAO;AACT;AAQO,SAAS,cAAc,KAA+B;IAC3D,8CAA8C;IAC9C,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,QAAQ;QACnC,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;IAC1E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,6CAA6C,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;IAE7F,mCAAmC;IACnC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,yCAAyC,CAAC,EAClE,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YACjB,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,IAAI,EAAE;YACrB,cAAc,KAAK,IAAI,EAAE;YACzB,gBAAgB,KAAK,IAAI,EAAE,YAAY;YACvC,OAAO,KAAK,IAAI,EAAE;QACpB,CAAC;IAGH,wDAAwD;IACxD,MAAM,YAAY,MAAM,IAAI,CAAC;IAE7B,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,2CAA2C,EAAE,UAAU,EAAE,EAAE;IACvF,OAAO;QACL,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,sDAAsD,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;IACzG;IAEA,OAAO;AACT;AASO,SAAS,gBAAgB,MAAc,EAAE,cAA2B;IACzE,OAAO,eAAe,GAAG,CAAC;AAC5B;AAUO,SAAS,0BACd,KAA+B,EAC/B,KAAa,EACb,WAAmB;IAEnB,MAAM,yBAAyB,IAAI;IAEnC,oDAAoD;IACpD,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,MAAM,KAAK,aAAa;YAC/B,uBAAuB,GAAG,CAAC,KAAK,MAAM;QACxC;IACF;IAEA,OAAO;AACT;AAUO,SAAS,2BACd,KAA+B,EAC/B,KAAa,EACb,WAAmB;IAEnB,MAAM,0BAA0B,IAAI;IAEpC,oDAAoD;IACpD,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,KAAK,MAAM,KAAK,eAAe,KAAK,MAAM,IAAI,KAAK,YAAY,EAAE;YACnE,yCAAyC;YACzC,IAAI,CAAC,wBAAwB,GAAG,CAAC,KAAK,MAAM,GAAG;gBAC7C,wBAAwB,GAAG,CAAC,KAAK,MAAM,EAAE,IAAI;YAC/C;YAEA,gCAAgC;YAChC,MAAM,YAAY,KAAK,YAAY;YACnC,wBAAwB,GAAG,CAAC,KAAK,MAAM,GAAG,IAAI;QAChD;IACF;IAEA,OAAO;AACT;AAUO,SAAS,kBACd,KAA+B,EAC/B,KAAa,EACb,WAAmB;IAEnB,MAAM,iBAAiB,IAAI,IAAY;QAAC;KAAY;IACpD,MAAM,QAAkB;QAAC;KAAY;IAErC,8CAA8C;IAC9C,MAAM,gBAAgB,IAAI;IAE1B,gCAAgC;IAChC,MAAM,OAAO,CAAC,CAAA;QACZ,cAAc,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE;IAC/B;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,kBAAkB,cAAc,GAAG,CAAC,KAAK,MAAM,KAAK,EAAE;QAC5D,gBAAgB,IAAI,CAAC,KAAK,MAAM;QAChC,cAAc,GAAG,CAAC,KAAK,MAAM,EAAE;IACjC;IAEA,kCAAkC;IAClC,MAAO,MAAM,MAAM,GAAG,EAAG;QACvB,MAAM,gBAAgB,MAAM,KAAK;QACjC,MAAM,YAAY,cAAc,GAAG,CAAC,kBAAkB,EAAE;QAExD,KAAK,MAAM,cAAc,UAAW;YAClC,IAAI,CAAC,eAAe,GAAG,CAAC,aAAa;gBACnC,eAAe,GAAG,CAAC;gBACnB,MAAM,IAAI,CAAC;YACb;QACF;IACF;IAEA,OAAO;AACT;AASO,SAAS,aACd,KAA+B,EAC/B,KAAa;IAEb,MAAM,gBAAgB,IAAI;IAE1B,gCAAgC;IAChC,MAAM,OAAO,CAAC,CAAA;QACZ,cAAc,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE;IAC/B;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,kBAAkB,cAAc,GAAG,CAAC,KAAK,MAAM,KAAK,EAAE;QAC5D,gBAAgB,IAAI,CAAC,KAAK,MAAM;QAChC,cAAc,GAAG,CAAC,KAAK,MAAM,EAAE;IACjC;IAEA,MAAM,UAAU,IAAI;IACpB,MAAM,iBAAiB,IAAI;IAC3B,MAAM,eAAe,IAAI;IAEzB,gCAAgC;IAChC,SAAS,IAAI,MAAc;QACzB,IAAI,eAAe,GAAG,CAAC,SAAS;YAC9B,aAAa,GAAG,CAAC;YACjB,OAAO;QACT;QAEA,IAAI,QAAQ,GAAG,CAAC,SAAS;YACvB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,eAAe,GAAG,CAAC;QAEnB,MAAM,YAAY,cAAc,GAAG,CAAC,WAAW,EAAE;QACjD,KAAK,MAAM,cAAc,UAAW;YAClC,IAAI,IAAI,aAAa;gBACnB,aAAa,GAAG,CAAC;gBACjB,OAAO;YACT;QACF;QAEA,eAAe,MAAM,CAAC;QACtB,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG;YACzB,IAAI,KAAK,EAAE;QACb;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/connectivityValidation.ts"], "sourcesContent": ["import { No<PERSON>, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { ValidationError, ValidationErrorCode, ValidationResult } from \"./types\";\r\nimport { createValidationError } from \"./errors\";\r\nimport { findStartNode, getConnectedNodes, detectCycles } from \"./utils\";\r\n\r\n/**\r\n * Validates that the workflow has a StartNode with enhanced logging and fallback detection\r\n *\r\n * @param nodes The array of nodes to validate\r\n * @returns A validation result with the StartNode ID if found\r\n */\r\nexport function validateStartNode(\r\n  nodes: Node<WorkflowNodeData>[]\r\n): ValidationResult & { startNodeId?: string } {\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);\r\n  console.log(`[${timestamp}] [validateStartNode] Starting StartNode validation with ${nodes.length} nodes`);\r\n\r\n  // Check if nodes array is valid\r\n  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {\r\n    console.warn(`[${timestamp}] [validateStartNode] Nodes array is empty or invalid`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_EMPTY,\r\n        \"Workflow is empty or not properly initialized\"\r\n      )\r\n    );\r\n    return { isValid: false, errors, warnings, infos };\r\n  }\r\n\r\n  // Find the StartNode using our enhanced detection\r\n  const startNode = findStartNode(nodes);\r\n\r\n  if (!startNode) {\r\n    console.warn(`[${timestamp}] [validateStartNode] No StartNode found using standard detection methods`);\r\n\r\n    // Try to find a node that might be a Start node based on additional heuristics\r\n    const potentialStartNodes = nodes.filter(node => {\r\n      // Check for any node with \"start\" in its label or type (case insensitive)\r\n      const label = (node.data?.label || \"\").toLowerCase();\r\n      const type = (node.data?.type || \"\").toLowerCase();\r\n      const originalType = (node.data?.originalType || \"\").toLowerCase();\r\n\r\n      return label.includes(\"start\") || type.includes(\"start\") || originalType.includes(\"start\");\r\n    });\r\n\r\n    if (potentialStartNodes.length > 0) {\r\n      const fallbackNode = potentialStartNodes[0];\r\n      console.log(`[${timestamp}] [validateStartNode] Found potential StartNode using fallback detection: ${fallbackNode.id}`);\r\n\r\n      // Add a warning but don't fail validation\r\n      warnings.push(\r\n        createValidationError(\r\n          ValidationErrorCode.WORKFLOW_USING_FALLBACK_START_NODE,\r\n          `Using ${fallbackNode.data?.label || fallbackNode.id} as a fallback Start node`,\r\n          \"warning\"\r\n        )\r\n      );\r\n\r\n      return {\r\n        isValid: true,\r\n        errors,\r\n        warnings,\r\n        infos,\r\n        startNodeId: fallbackNode.id,\r\n      };\r\n    }\r\n\r\n    // No StartNode found, even with fallback detection\r\n    console.error(`[${timestamp}] [validateStartNode] No StartNode found, validation failed`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_MISSING_START_NODE,\r\n        \"Workflow must have a Start node\"\r\n      )\r\n    );\r\n    return { isValid: false, errors, warnings, infos };\r\n  }\r\n\r\n  console.log(`[${timestamp}] [validateStartNode] StartNode validation successful, found node: ${startNode.id}`);\r\n  return {\r\n    isValid: true,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n    startNodeId: startNode.id,\r\n  };\r\n}\r\n\r\n/**\r\n * Validates that all nodes are connected to the StartNode\r\n *\r\n * Instead of failing validation, it now adds warnings for disconnected nodes\r\n * and allows the workflow to be saved or executed with only the connected nodes.\r\n *\r\n * @param nodes The array of nodes to validate\r\n * @param edges The array of edges to validate\r\n * @param startNodeId The ID of the StartNode\r\n * @returns A validation result with the set of connected nodes\r\n */\r\nexport function validateConnectivity(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  startNodeId: string\r\n): ValidationResult & { connectedNodes?: Set<string> } {\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  // Get all nodes connected to the StartNode\r\n  const connectedNodes = getConnectedNodes(nodes, edges, startNodeId);\r\n\r\n  // Find disconnected nodes\r\n  const disconnectedNodes = nodes.filter(node => !connectedNodes.has(node.id));\r\n\r\n  if (disconnectedNodes.length > 0) {\r\n    const disconnectedNodeIds = disconnectedNodes.map(node => node.id).join(\", \");\r\n    const disconnectedNodeLabels = disconnectedNodes\r\n      .map(node => node.data?.label || node.id)\r\n      .join(\", \");\r\n\r\n    warnings.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_DISCONNECTED_NODES,\r\n        `The following nodes are not connected to the Start node and will not be saved or executed: ${disconnectedNodeLabels}`,\r\n        \"warning\"\r\n      )\r\n    );\r\n\r\n    // Add info about what will happen with these nodes\r\n    infos.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_INFO,\r\n        `Only nodes connected to the Start node will be included in the workflow execution and saving.`,\r\n        \"info\"\r\n      )\r\n    );\r\n  }\r\n\r\n  // Always return isValid=true since disconnected nodes are now just warnings\r\n  return {\r\n    isValid: true,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n    connectedNodes,\r\n  };\r\n}\r\n\r\n/**\r\n * Detects cycles in the workflow\r\n *\r\n * @param nodes The array of nodes to validate\r\n * @param edges The array of edges to validate\r\n * @returns A validation result with warnings if cycles are detected\r\n */\r\nexport function detectCyclesInWorkflow(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[]\r\n): ValidationResult {\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  // Detect cycles\r\n  const nodesInCycle = detectCycles(nodes, edges);\r\n\r\n  if (nodesInCycle.length > 0) {\r\n    warnings.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_CYCLE_DETECTED,\r\n        `Workflow contains cycles involving the following nodes: ${nodesInCycle.join(\", \")}`,\r\n        \"warning\"\r\n      )\r\n    );\r\n  }\r\n\r\n  return {\r\n    isValid: nodesInCycle.length === 0,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;;AAQO,SAAS,kBACd,KAA+B;IAE/B,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;IAC1E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,yDAAyD,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;IAEzG,gCAAgC;IAChC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;QACzD,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,qDAAqD,CAAC;QACjF,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,cAAc,EAClC;QAGJ,OAAO;YAAE,SAAS;YAAO;YAAQ;YAAU;QAAM;IACnD;IAEA,kDAAkD;IAClD,MAAM,YAAY,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;IAEhC,IAAI,CAAC,WAAW;QACd,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,yEAAyE,CAAC;QAErG,+EAA+E;QAC/E,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA;YACvC,0EAA0E;YAC1E,MAAM,QAAQ,CAAC,KAAK,IAAI,EAAE,SAAS,EAAE,EAAE,WAAW;YAClD,MAAM,OAAO,CAAC,KAAK,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW;YAChD,MAAM,eAAe,CAAC,KAAK,IAAI,EAAE,gBAAgB,EAAE,EAAE,WAAW;YAEhE,OAAO,MAAM,QAAQ,CAAC,YAAY,KAAK,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC;QACpF;QAEA,IAAI,oBAAoB,MAAM,GAAG,GAAG;YAClC,MAAM,eAAe,mBAAmB,CAAC,EAAE;YAC3C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,0EAA0E,EAAE,aAAa,EAAE,EAAE;YAEvH,0CAA0C;YAC1C,SAAS,IAAI,CACX,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,kCAAkC,EACtD,CAAC,MAAM,EAAE,aAAa,IAAI,EAAE,SAAS,aAAa,EAAE,CAAC,yBAAyB,CAAC,EAC/E;YAIJ,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;gBACA,aAAa,aAAa,EAAE;YAC9B;QACF;QAEA,mDAAmD;QACnD,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,2DAA2D,CAAC;QACxF,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,2BAA2B,EAC/C;QAGJ,OAAO;YAAE,SAAS;YAAO;YAAQ;YAAU;QAAM;IACnD;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,mEAAmE,EAAE,UAAU,EAAE,EAAE;IAC7G,OAAO;QACL,SAAS;QACT;QACA;QACA;QACA,aAAa,UAAU,EAAE;IAC3B;AACF;AAaO,SAAS,qBACd,KAA+B,EAC/B,KAAa,EACb,WAAmB;IAEnB,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,2CAA2C;IAC3C,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,OAAO;IAEvD,0BAA0B;IAC1B,MAAM,oBAAoB,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE;IAE1E,IAAI,kBAAkB,MAAM,GAAG,GAAG;QAChC,MAAM,sBAAsB,kBAAkB,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE,EAAE,IAAI,CAAC;QACxE,MAAM,yBAAyB,kBAC5B,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,SAAS,KAAK,EAAE,EACvC,IAAI,CAAC;QAER,SAAS,IAAI,CACX,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,2BAA2B,EAC/C,CAAC,2FAA2F,EAAE,wBAAwB,EACtH;QAIJ,mDAAmD;QACnD,MAAM,IAAI,CACR,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,aAAa,EACjC,CAAC,6FAA6F,CAAC,EAC/F;IAGN;IAEA,4EAA4E;IAC5E,OAAO;QACL,SAAS;QACT;QACA;QACA;QACA;IACF;AACF;AASO,SAAS,uBACd,KAA+B,EAC/B,KAAa;IAEb,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;IAEzC,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,SAAS,IAAI,CACX,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,uBAAuB,EAC3C,CAAC,wDAAwD,EAAE,aAAa,IAAI,CAAC,OAAO,EACpF;IAGN;IAEA,OAAO;QACL,SAAS,aAAa,MAAM,KAAK;QACjC;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/fieldValidation.ts"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { InputDefinition, InputRequirementRule } from \"@/types\";\r\nimport { ValidationError, ValidationErrorCode, ValidationResult, MissingField } from \"./types\";\r\nimport { createValidationError } from \"./errors\";\r\n\r\n/**\r\n * Unwraps dual-purpose input values that may be wrapped in {value: ...} objects\r\n * @param value The value to unwrap\r\n * @returns The unwrapped value\r\n */\r\nfunction unwrapDualPurposeValue(value: any): any {\r\n  console.log(`[unwrapDualPurposeValue] Input value:`, value, `Type: ${typeof value}`);\r\n\r\n  // Check if the value is wrapped in a dual-purpose input structure\r\n  if (typeof value === 'object' && value !== null && 'value' in value) {\r\n    // Check if this looks like a dual-purpose input wrapper\r\n    // It should have a 'value' property and optionally other metadata properties\r\n    const keys = Object.keys(value);\r\n    const hasValueProperty = keys.includes('value');\r\n    const hasOnlyValueProperty = keys.length === 1 && hasValueProperty;\r\n    const hasValueAndMetadata = hasValueProperty && keys.every(key =>\r\n      key === 'value' || key === 'transition_id' || key === 'metadata' || key === 'type'\r\n    );\r\n\r\n    console.log(`[unwrapDualPurposeValue] Analysis:`, {\r\n      hasValueProperty,\r\n      hasOnlyValueProperty,\r\n      hasValueAndMetadata,\r\n      keys,\r\n      valueType: typeof value.value\r\n    });\r\n\r\n    if (hasOnlyValueProperty || hasValueAndMetadata) {\r\n      console.log(`[unwrapDualPurposeValue] Detected dual-purpose wrapper, unwrapping:`, value);\r\n      console.log(`[unwrapDualPurposeValue] Wrapper keys:`, keys);\r\n      console.log(`[unwrapDualPurposeValue] Extracted value:`, value.value);\r\n\r\n      // If the extracted value is a JSON string for object types, parse it\r\n      if (typeof value.value === 'string' && value.value.trim().startsWith('{')) {\r\n        try {\r\n          const parsed = JSON.parse(value.value);\r\n          console.log(`[unwrapDualPurposeValue] Parsed JSON string to object:`, parsed);\r\n          return parsed;\r\n        } catch (e) {\r\n          console.log(`[unwrapDualPurposeValue] Failed to parse JSON string, returning as-is:`, e);\r\n          return value.value;\r\n        }\r\n      }\r\n\r\n      return value.value;\r\n    }\r\n  }\r\n\r\n  // If it's a JSON string that looks like an object, try to parse it\r\n  if (typeof value === 'string' && value.trim().startsWith('{')) {\r\n    try {\r\n      const parsed = JSON.parse(value);\r\n      console.log(`[unwrapDualPurposeValue] Parsed standalone JSON string to object:`, parsed);\r\n      return parsed;\r\n    } catch (e) {\r\n      console.log(`[unwrapDualPurposeValue] Failed to parse standalone JSON string, returning as-is:`, e);\r\n    }\r\n  }\r\n\r\n  console.log(`[unwrapDualPurposeValue] No unwrapping needed, returning as-is:`, value);\r\n  return value;\r\n}\r\n\r\n/**\r\n * Validates a single field based on its definition\r\n *\r\n * @param inputDef The input definition\r\n * @param value The field value\r\n * @param nodeId The ID of the node containing the field\r\n * @param node The node containing the field (optional)\r\n * @returns An array of validation errors\r\n */\r\nexport function validateField(\r\n  inputDef: InputDefinition,\r\n  value: any,\r\n  nodeId: string,\r\n  node?: Node<WorkflowNodeData>\r\n): ValidationError[] {\r\n  console.log(`[${getTimestamp()}] [validateField] ========== VALIDATING FIELD ==========`);\r\n  console.log(`[${getTimestamp()}] [validateField] Field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);\r\n  console.log(`[${getTimestamp()}] [validateField] Node ID: ${nodeId}`);\r\n  console.log(`[${getTimestamp()}] [validateField] Node label: ${node?.data?.label || \"Unknown\"}`);\r\n  console.log(`[${getTimestamp()}] [validateField] Input type: ${inputDef.input_type}`);\r\n  console.log(`[${getTimestamp()}] [validateField] Value: ${JSON.stringify(value)}`);\r\n\r\n  const errors: ValidationError[] = [];\r\n\r\n  // Skip validation for handle inputs\r\n  if (inputDef.is_handle) {\r\n    console.log(`[${getTimestamp()}] [validateField] Skipping validation for handle input: ${inputDef.name}`);\r\n    return errors;\r\n  }\r\n\r\n  // Determine if field is required\r\n  const isRequired = node ? isFieldRequired(node, inputDef) : !!inputDef.required;\r\n  console.log(`[${getTimestamp()}] [validateField] Is required: ${isRequired ? \"YES\" : \"NO\"} (node provided: ${node ? \"YES\" : \"NO\"})`);\r\n\r\n  // Check if field is empty\r\n  const isEmpty = isValueEmpty(value, inputDef.input_type);\r\n  console.log(`[${getTimestamp()}] [validateField] Is empty: ${isEmpty ? \"YES\" : \"NO\"}`);\r\n\r\n  // Check if required field is missing\r\n  if (isRequired && isEmpty) {\r\n    console.log(`[${getTimestamp()}] [validateField] VALIDATION ERROR: Required field \"${inputDef.display_name || inputDef.name}\" is missing`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_REQUIRED,\r\n        `Field \"${inputDef.display_name}\" is required`,\r\n        \"error\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n    console.log(`[${getTimestamp()}] [validateField] Stopping validation for this field due to missing required value`);\r\n    return errors; // Stop validation if required field is missing\r\n  }\r\n\r\n  // Skip validation for empty optional fields\r\n  if (!isRequired && isEmpty) {\r\n    console.log(`[${getTimestamp()}] [validateField] Skipping validation for empty optional field: ${inputDef.name}`);\r\n    return errors;\r\n  }\r\n\r\n  // Validate based on input type\r\n  switch (inputDef.input_type) {\r\n    case \"string\":\r\n      validateStringField(inputDef, value, nodeId, errors);\r\n      break;\r\n    case \"int\":\r\n    case \"float\":\r\n    case \"number\":\r\n      validateNumberField(inputDef, value, nodeId, errors);\r\n      break;\r\n    case \"dict\":\r\n    case \"json\":\r\n    case \"object\":\r\n      validateObjectField(inputDef, value, nodeId, errors);\r\n      break;\r\n  }\r\n\r\n  console.log(`[${getTimestamp()}] [validateField] Field validation complete. Errors found: ${errors.length}`);\r\n  return errors;\r\n}\r\n\r\n/**\r\n * Validates a string field\r\n *\r\n * @param inputDef The input definition\r\n * @param value The field value\r\n * @param nodeId The ID of the node containing the field\r\n * @param errors The array of errors to append to\r\n */\r\nfunction validateStringField(\r\n  inputDef: InputDefinition,\r\n  value: any,\r\n  nodeId: string,\r\n  errors: ValidationError[]\r\n): void {\r\n  console.log(`[${getTimestamp()}] [validateStringField] Validating string field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);\r\n  console.log(`[${getTimestamp()}] [validateStringField] Value type: ${typeof value}, Value: ${JSON.stringify(value)}`);\r\n\r\n  // Check type\r\n  if (typeof value !== \"string\") {\r\n    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION ERROR: Value is not a string: ${typeof value}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_STRING_LENGTH,\r\n        `Field \"${inputDef.display_name}\" must be a string`,\r\n        \"error\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n    return;\r\n  }\r\n\r\n  // Check min length\r\n  if (inputDef.min_length !== undefined && value.length < inputDef.min_length) {\r\n    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String length ${value.length} is less than minimum ${inputDef.min_length}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_STRING_LENGTH,\r\n        `Field \"${inputDef.display_name}\" must be at least ${inputDef.min_length} characters`,\r\n        \"warning\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n  }\r\n\r\n  // Check max length\r\n  if (inputDef.max_length !== undefined && value.length > inputDef.max_length) {\r\n    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String length ${value.length} exceeds maximum ${inputDef.max_length}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_STRING_LENGTH,\r\n        `Field \"${inputDef.display_name}\" must be at most ${inputDef.max_length} characters`,\r\n        \"warning\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n  }\r\n\r\n  // Check pattern\r\n  if (inputDef.pattern && !new RegExp(inputDef.pattern).test(value)) {\r\n    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String does not match pattern: ${inputDef.pattern}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_PATTERN_MISMATCH,\r\n        inputDef.pattern_error || `Field \"${inputDef.display_name}\" does not match the required pattern`,\r\n        \"warning\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n  }\r\n\r\n  console.log(`[${getTimestamp()}] [validateStringField] String validation complete. Errors found: ${errors.length}`);\r\n}\r\n\r\n/**\r\n * Validates a number field\r\n *\r\n * @param inputDef The input definition\r\n * @param value The field value\r\n * @param nodeId The ID of the node containing the field\r\n * @param errors The array of errors to append to\r\n */\r\nfunction validateNumberField(\r\n  inputDef: InputDefinition,\r\n  value: any,\r\n  nodeId: string,\r\n  errors: ValidationError[]\r\n): void {\r\n  console.log(`[${getTimestamp()}] [validateNumberField] Validating number field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);\r\n  console.log(`[${getTimestamp()}] [validateNumberField] Original value type: ${typeof value}, Value: ${JSON.stringify(value)}`);\r\n\r\n  // Convert to number if string\r\n  const numValue = typeof value === \"string\" ? Number(value) : value;\r\n  console.log(`[${getTimestamp()}] [validateNumberField] Converted value: ${numValue}, isNaN: ${isNaN(numValue)}`);\r\n\r\n  // Check type\r\n  if (typeof numValue !== \"number\" || isNaN(numValue)) {\r\n    console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION ERROR: Value is not a valid number: ${value}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_NUMBER_RANGE,\r\n        `Field \"${inputDef.display_name}\" must be a number`,\r\n        \"error\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n    return;\r\n  }\r\n\r\n  // Check min value\r\n  if (inputDef.min_value !== undefined && numValue < Number(inputDef.min_value)) {\r\n    console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION WARNING: Number ${numValue} is less than minimum ${inputDef.min_value}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_NUMBER_RANGE,\r\n        `Field \"${inputDef.display_name}\" must be at least ${inputDef.min_value}`,\r\n        \"warning\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n  }\r\n\r\n  // Check max value\r\n  if (inputDef.max_value !== undefined && numValue > Number(inputDef.max_value)) {\r\n    console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION WARNING: Number ${numValue} exceeds maximum ${inputDef.max_value}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_NUMBER_RANGE,\r\n        `Field \"${inputDef.display_name}\" must be at most ${inputDef.max_value}`,\r\n        \"warning\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n  }\r\n\r\n  console.log(`[${getTimestamp()}] [validateNumberField] Number validation complete. Errors found: ${errors.length}`);\r\n}\r\n\r\n/**\r\n * Validates an object field\r\n *\r\n * @param inputDef The input definition\r\n * @param value The field value\r\n * @param nodeId The ID of the node containing the field\r\n * @param errors The array of errors to append to\r\n */\r\nfunction validateObjectField(\r\n  inputDef: InputDefinition,\r\n  value: any,\r\n  nodeId: string,\r\n  errors: ValidationError[]\r\n): void {\r\n  console.log(`[${getTimestamp()}] [validateObjectField] Validating object field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);\r\n  console.log(`[${getTimestamp()}] [validateObjectField] Value type: ${typeof value}, Value: ${JSON.stringify(value)}`);\r\n\r\n  // Handle string representation of objects\r\n  if (typeof value === \"string\") {\r\n    console.log(`[${getTimestamp()}] [validateObjectField] Value is a string, attempting to parse as JSON`);\r\n    try {\r\n      value = JSON.parse(value);\r\n      console.log(`[${getTimestamp()}] [validateObjectField] Successfully parsed string as JSON: ${JSON.stringify(value)}`);\r\n    } catch (e) {\r\n      const errorMessage = e instanceof Error ? e.message : String(e);\r\n      console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Failed to parse string as JSON: ${errorMessage}`);\r\n      errors.push(\r\n        createValidationError(\r\n          \"INVALID_JSON\" as any, // Type assertion as a temporary fix\r\n          `Field \"${inputDef.display_name}\" must be valid JSON`,\r\n          \"error\",\r\n          nodeId,\r\n          inputDef.name\r\n        )\r\n      );\r\n      return;\r\n    }\r\n  }\r\n\r\n  // Check type\r\n  if (typeof value !== \"object\" || value === null) {\r\n    console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Value is not an object: ${typeof value}, null: ${value === null}`);\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.FIELD_MISSING_REQUIRED_KEYS,\r\n        `Field \"${inputDef.display_name}\" must be an object`,\r\n        \"error\",\r\n        nodeId,\r\n        inputDef.name\r\n      )\r\n    );\r\n    return;\r\n  }\r\n\r\n  // Check required keys\r\n  if (inputDef.required_keys && inputDef.required_keys.length > 0) {\r\n    console.log(`[${getTimestamp()}] [validateObjectField] Checking for required keys: ${inputDef.required_keys.join(', ')}`);\r\n    const missingKeys = inputDef.required_keys.filter(key => !(key in value));\r\n\r\n    if (missingKeys.length > 0) {\r\n      console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Missing required keys: ${missingKeys.join(', ')}`);\r\n      errors.push(\r\n        createValidationError(\r\n          ValidationErrorCode.FIELD_MISSING_REQUIRED_KEYS,\r\n          `Field \"${inputDef.display_name}\" is missing required keys: ${missingKeys.join(\", \")}`,\r\n          \"error\",\r\n          nodeId,\r\n          inputDef.name\r\n        )\r\n      );\r\n    } else {\r\n      console.log(`[${getTimestamp()}] [validateObjectField] All required keys are present`);\r\n    }\r\n  } else {\r\n    console.log(`[${getTimestamp()}] [validateObjectField] No required keys specified for this object`);\r\n  }\r\n\r\n  console.log(`[${getTimestamp()}] [validateObjectField] Object validation complete. Errors found: ${errors.length}`);\r\n}\r\n\r\n/**\r\n * Validates all fields in a node\r\n *\r\n * @param node The node to validate\r\n * @returns A validation result\r\n */\r\nexport function validateFields(node: Node<WorkflowNodeData>): ValidationResult {\r\n  console.log(`[${getTimestamp()}] [validateFields] ========== STARTING FIELD VALIDATION FOR NODE ==========`);\r\n  console.log(`[${getTimestamp()}] [validateFields] Node: ${node.id} (${node.data.label || \"Unnamed\"})`);\r\n  console.log(`[${getTimestamp()}] [validateFields] Type: ${node.data.type}, Original Type: ${node.data.originalType}`);\r\n\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  // Skip validation if node has no definition or inputs\r\n  if (!node.data?.definition?.inputs) {\r\n    console.log(`[${getTimestamp()}] [validateFields] Skipping validation - node has no definition or inputs`);\r\n    return { isValid: true, errors, warnings, infos };\r\n  }\r\n\r\n  const inputs = node.data.definition.inputs;\r\n  const config = node.data.config || {};\r\n\r\n  // Log detailed node information\r\n  console.log(`[${getTimestamp()}] [validateFields] Node details:\r\n    - ID: ${node.id}\r\n    - Label: ${node.data.label || \"Unnamed\"}\r\n    - Type: ${node.data.type}\r\n    - Original Type: ${node.data.originalType}\r\n    - Position: (${node.position.x}, ${node.position.y})\r\n    - Total inputs: ${inputs.length}\r\n    - Config keys: ${Object.keys(config).join(', ') || \"none\"}\r\n    - Is MCP component: ${isMCPMarketplaceComponent(node) ? \"YES\" : \"NO\"}`);\r\n\r\n  // Validate each input\r\n  inputs.forEach(input => {\r\n    console.log(`[${getTimestamp()}] [validateFields] Validating input: ${input.name} (${input.display_name || input.name})`);\r\n\r\n    // Pass the node to validateField so it can determine if the field is required\r\n    const fieldErrors = validateField(input, config[input.name], node.id, node);\r\n\r\n    // Log validation results\r\n    if (fieldErrors.length > 0) {\r\n      console.log(`[${getTimestamp()}] [validateFields] Found ${fieldErrors.length} validation issues for field ${input.name}:`);\r\n      fieldErrors.forEach((error, index) => {\r\n        console.log(`[${getTimestamp()}] [validateFields]   ${index + 1}. ${error.severity.toUpperCase()}: ${error.message}`);\r\n      });\r\n    } else {\r\n      console.log(`[${getTimestamp()}] [validateFields] Field ${input.name} passed validation`);\r\n    }\r\n\r\n    // Separate errors by severity\r\n    fieldErrors.forEach(error => {\r\n      if (error.severity === \"error\") {\r\n        errors.push(error);\r\n      } else if (error.severity === \"warning\") {\r\n        warnings.push(error);\r\n      } else if (error.severity === \"info\") {\r\n        infos.push(error);\r\n      }\r\n    });\r\n  });\r\n\r\n  // Log validation summary\r\n  console.log(`[${getTimestamp()}] [validateFields] ========== FIELD VALIDATION COMPLETE ==========`);\r\n  console.log(`[${getTimestamp()}] [validateFields] Validation results for node ${node.id} (${node.data.label || \"Unnamed\"}):`);\r\n  console.log(`[${getTimestamp()}] [validateFields] - Errors: ${errors.length}`);\r\n  console.log(`[${getTimestamp()}] [validateFields] - Warnings: ${warnings.length}`);\r\n  console.log(`[${getTimestamp()}] [validateFields] - Infos: ${infos.length}`);\r\n  console.log(`[${getTimestamp()}] [validateFields] - Is valid: ${errors.length === 0 ? \"YES\" : \"NO\"}`);\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get a timestamp for logging\r\n * @returns Formatted timestamp string\r\n */\r\nfunction getTimestamp(): string {\r\n  return new Date().toISOString().replace('T', ' ').substring(0, 19);\r\n}\r\n\r\n/**\r\n * Evaluates requirement rules to determine if a field should be required\r\n *\r\n * @param requirementRules The requirement rules to evaluate\r\n * @param requirementLogic The logic to use when combining rules (OR, AND)\r\n * @param config The current node configuration\r\n * @returns True if the requirement rules are satisfied\r\n */\r\nfunction evaluateRequirementRules(\r\n  requirementRules: InputRequirementRule[] | null | undefined,\r\n  requirementLogic: string | undefined,\r\n  config: Record<string, any>\r\n): boolean {\r\n  if (!requirementRules || requirementRules.length === 0) {\r\n    return false;\r\n  }\r\n\r\n  console.log(`[${getTimestamp()}] [evaluateRequirementRules] Evaluating ${requirementRules.length} requirement rules with logic: ${requirementLogic || \"OR\"}`);\r\n\r\n  const logic = requirementLogic || \"OR\";\r\n  const results = requirementRules.map(rule => {\r\n    const targetValue = config[rule.field_name];\r\n    const operator = rule.operator || \"equals\";\r\n\r\n    let ruleMatches = false;\r\n    switch (operator) {\r\n      case \"equals\":\r\n        ruleMatches = targetValue === rule.field_value;\r\n        break;\r\n      case \"not_equals\":\r\n        ruleMatches = targetValue !== rule.field_value;\r\n        break;\r\n      case \"contains\":\r\n        ruleMatches = typeof targetValue === \"string\" && targetValue.includes(rule.field_value);\r\n        break;\r\n      case \"exists\":\r\n        ruleMatches = targetValue !== undefined && targetValue !== null;\r\n        break;\r\n      case \"not_exists\":\r\n        ruleMatches = targetValue === undefined || targetValue === null;\r\n        break;\r\n      default:\r\n        ruleMatches = targetValue === rule.field_value;\r\n    }\r\n\r\n    console.log(`[${getTimestamp()}] [evaluateRequirementRules] Rule: ${rule.field_name} ${operator} ${rule.field_value} | Target: ${targetValue} | Result: ${ruleMatches}`);\r\n    return ruleMatches;\r\n  });\r\n\r\n  const finalResult = logic === \"AND\" ? results.every(r => r) : results.some(r => r);\r\n  console.log(`[${getTimestamp()}] [evaluateRequirementRules] Final result with ${logic} logic: ${finalResult}`);\r\n\r\n  return finalResult;\r\n}\r\n\r\n/**\r\n * Determines if a node is an MCP Marketplace component\r\n *\r\n * @param node The node to check\r\n * @returns True if the node is an MCP Marketplace component\r\n */\r\nfunction isMCPMarketplaceComponent(node: Node<WorkflowNodeData>): boolean {\r\n  if (!node || !node.data) return false;\r\n\r\n  // Check various indicators that this is an MCP component\r\n  const isMCP = (\r\n    node.data.type === \"mcp\" ||\r\n    node.data.originalType === \"MCPMarketplaceComponent\" ||\r\n    node.data.type === \"MCPMarketplaceComponent\" ||\r\n    (node.data.definition && node.data.definition.type === \"MCPMarketplaceComponent\") ||\r\n    (node.data.definition && node.data.definition.mcp_info) ||\r\n    (node.data.definition && node.data.definition.path &&\r\n     (node.data.definition.path.includes(\"mcp_marketplace\") ||\r\n      node.data.definition.path.includes(\"components.mcp\")))\r\n  );\r\n\r\n  console.log(`[${getTimestamp()}] [isMCPMarketplaceComponent] Node ${node.id} (${node.data.label || \"Unnamed\"}):\r\n    - type: ${node.data.type}\r\n    - originalType: ${node.data.originalType}\r\n    - definition.type: ${node.data?.definition?.type}\r\n    - has mcp_info: ${!!node.data?.definition?.mcp_info}\r\n    - path: ${node.data?.definition?.path}\r\n    - RESULT: ${isMCP ? \"IS MCP COMPONENT\" : \"NOT MCP COMPONENT\"}`);\r\n\r\n  return !!isMCP; // Convert to boolean\r\n}\r\n\r\n/**\r\n * Determines if a field should be considered required based on component type and field properties\r\n *\r\n * @param node The node containing the field\r\n * @param input The input definition\r\n * @param isHandleConnected Optional flag indicating if this handle input is connected to another node\r\n * @returns True if the field should be considered required\r\n */\r\nexport function isFieldRequired(\r\n  node: Node<WorkflowNodeData>,\r\n  input: InputDefinition,\r\n  isHandleConnected: boolean = false\r\n): boolean {\r\n  console.log(`[${getTimestamp()}] [isFieldRequired] Checking if field is required:\r\n    - Node: ${node.id} (${node.data.label || \"Unnamed\"})\r\n    - Field: ${input.name} (${input.display_name || input.name})\r\n    - Input type: ${input.input_type}\r\n    - Explicitly required: ${input.required === true ? \"YES\" : \"NO\"}\r\n    - Is handle: ${input.is_handle ? \"YES\" : \"NO\"}\r\n    - Is handle connected: ${isHandleConnected ? \"YES\" : \"NO\"}\r\n    - Ends with _handle: ${input.name.endsWith(\"_handle\") ? \"YES\" : \"NO\"}\r\n    - Has requirement rules: ${input.requirement_rules && input.requirement_rules.length > 0 ? \"YES\" : \"NO\"}`);\r\n\r\n  // Check requirement rules first - if they exist and are satisfied, the field is required\r\n  if (input.requirement_rules && input.requirement_rules.length > 0) {\r\n    const config = node.data.config || {};\r\n    const isRequiredByRules = evaluateRequirementRules(\r\n      input.requirement_rules,\r\n      input.requirement_logic,\r\n      config\r\n    );\r\n\r\n    if (isRequiredByRules) {\r\n      console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is required by requirement rules`);\r\n\r\n      // For handle inputs, they're only required if not connected\r\n      if (input.is_handle || input.input_type === \"handle\" || input.name.endsWith(\"_handle\")) {\r\n        if (isHandleConnected) {\r\n          console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and is connected, not required for direct input despite requirement rules`);\r\n          return false;\r\n        } else {\r\n          console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input but NOT connected, required for direct input due to requirement rules`);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }\r\n  }\r\n\r\n  // If explicitly marked as required, it's required\r\n  if (input.required === true) {\r\n    // For handle inputs, they're only required if not connected\r\n    if (input.is_handle || input.input_type === \"handle\" || input.name.endsWith(\"_handle\")) {\r\n      if (isHandleConnected) {\r\n        console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and is connected, not required for direct input`);\r\n        return false;\r\n      } else {\r\n        console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input but NOT connected, required for direct input`);\r\n        return true;\r\n      }\r\n    }\r\n\r\n    console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is explicitly marked as required`);\r\n    return true;\r\n  }\r\n\r\n  // For handle inputs that aren't explicitly required, they're never required for direct input\r\n  if (input.is_handle || input.input_type === \"handle\" || input.name.endsWith(\"_handle\")) {\r\n    console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and not explicitly required, not required for direct input`);\r\n    return false;\r\n  }\r\n\r\n  // For MCP components, we need special handling\r\n  const isMCP = isMCPMarketplaceComponent(node);\r\n  if (isMCP) {\r\n    console.log(`[${getTimestamp()}] [isFieldRequired] Node is an MCP component, applying special rules for field ${input.name}`);\r\n\r\n    // Common optional fields in MCP components\r\n    const commonOptionalFields = [\"link\", \"api_key\", \"base_url\"];\r\n    if (commonOptionalFields.includes(input.name)) {\r\n      console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is in the common optional fields list for MCP components`);\r\n      return false;\r\n    }\r\n\r\n    // For MCP components, consider fields required if they are explicitly marked as required: true\r\n    // OR if they are not explicitly marked as optional (required: false)\r\n    const isRequired = input.required !== false;\r\n    console.log(`[${getTimestamp()}] [isFieldRequired] MCP component field ${input.name} required status: ${isRequired ? \"REQUIRED\" : \"OPTIONAL\"} (required !== false: ${input.required !== false})`);\r\n    return isRequired;\r\n  }\r\n\r\n  // For standard components, consider fields required if they are explicitly marked as required: true\r\n  // OR if they are not explicitly marked as optional (required: false)\r\n  const isRequired = input.required !== false;\r\n  console.log(`[${getTimestamp()}] [isFieldRequired] Standard component field ${input.name} required status: ${isRequired ? \"REQUIRED\" : \"OPTIONAL\"} (required !== false: ${input.required !== false})`);\r\n  return isRequired;\r\n}\r\n\r\n/**\r\n * Determines if a field value should be considered empty\r\n *\r\n * @param value The field value\r\n * @param inputType The type of the input\r\n * @returns True if the value should be considered empty\r\n */\r\nfunction isValueEmpty(value: any, inputType: string): boolean {\r\n  console.log(`[${getTimestamp()}] [isValueEmpty] Checking if value is empty:\r\n    - Input type: ${inputType}\r\n    - Value type: ${typeof value}\r\n    - Value: ${JSON.stringify(value)}\r\n    - Is undefined: ${value === undefined ? \"YES\" : \"NO\"}\r\n    - Is null: ${value === null ? \"YES\" : \"NO\"}`);\r\n\r\n  // Undefined or null values are always empty\r\n  if (value === undefined || value === null) {\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] Value is undefined or null, considered EMPTY`);\r\n    return true;\r\n  }\r\n\r\n  // For boolean values, they're never empty (false is a valid value)\r\n  if (inputType === \"boolean\" || inputType === \"bool\") {\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] Boolean value ${value}, considered NOT EMPTY (false is a valid value)`);\r\n    return false;\r\n  }\r\n\r\n  // For numeric values, 0 is a valid value\r\n  if (inputType === \"number\" || inputType === \"int\" || inputType === \"float\") {\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] Numeric value ${value}, considered NOT EMPTY (0 is a valid value)`);\r\n    return false;\r\n  }\r\n\r\n  // For strings, empty string is empty\r\n  if (inputType === \"string\" || inputType === \"text\") {\r\n    const isEmpty = value === \"\";\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] String value \"${value}\", considered ${isEmpty ? \"EMPTY\" : \"NOT EMPTY\"}`);\r\n    return isEmpty;\r\n  }\r\n\r\n  // For objects and arrays, empty objects/arrays are considered empty\r\n  if (\r\n    (inputType === \"object\" || inputType === \"dict\" || inputType === \"json\") &&\r\n    typeof value === \"object\"\r\n  ) {\r\n    const isEmpty = Object.keys(value).length === 0;\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] Object value with ${Object.keys(value).length} keys, considered ${isEmpty ? \"EMPTY\" : \"NOT EMPTY\"}`);\r\n    return isEmpty;\r\n  }\r\n\r\n  if ((inputType === \"array\" || inputType === \"list\") && Array.isArray(value)) {\r\n    const isEmpty = value.length === 0;\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] Array value with ${value.length} items, considered ${isEmpty ? \"EMPTY\" : \"NOT EMPTY\"}`);\r\n    return isEmpty;\r\n  }\r\n\r\n  // For string representations of objects/arrays, check if they're empty\r\n  if (\r\n    (inputType === \"object\" || inputType === \"dict\" || inputType === \"json\" ||\r\n     inputType === \"array\" || inputType === \"list\") &&\r\n    typeof value === \"string\"\r\n  ) {\r\n    const trimmed = value.trim();\r\n    const isEmpty = trimmed === \"\" || trimmed === \"{}\" || trimmed === \"[]\";\r\n    console.log(`[${getTimestamp()}] [isValueEmpty] String representation of object/array: \"${trimmed}\", considered ${isEmpty ? \"EMPTY\" : \"NOT EMPTY\"}`);\r\n    return isEmpty;\r\n  }\r\n\r\n  // Default case - empty string is empty\r\n  const isEmpty = value === \"\";\r\n  console.log(`[${getTimestamp()}] [isValueEmpty] Default case, value: \"${value}\", considered ${isEmpty ? \"EMPTY\" : \"NOT EMPTY\"}`);\r\n  return isEmpty;\r\n}\r\n\r\n/**\r\n * Checks if a handle input is connected to another node\r\n *\r\n * @param nodeId The ID of the node containing the handle\r\n * @param inputName The name of the handle input\r\n * @param edges The array of edges in the workflow\r\n * @returns True if the handle is connected to another node\r\n */\r\nfunction isHandleConnected(nodeId: string, inputName: string, edges: Edge[]): boolean {\r\n  // For target handles, the connection would be to a target handle with the input name\r\n  const isConnected = edges.some(edge =>\r\n    edge.target === nodeId && edge.targetHandle === inputName\r\n  );\r\n\r\n  console.log(`[${getTimestamp()}] [isHandleConnected] Checking if handle ${nodeId}.${inputName} is connected: ${isConnected ? \"YES\" : \"NO\"}`);\r\n\r\n  return isConnected;\r\n}\r\n\r\n/**\r\n * Collects all fields (both required and optional) from connected nodes\r\n *\r\n * @param nodes The array of nodes to check\r\n * @param connectedNodes Set of node IDs connected to the start node\r\n * @param edges Optional array of edges in the workflow\r\n * @returns An array of field objects (both required and optional)\r\n */\r\nexport function collectAllFields(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  connectedNodes: Set<string>,\r\n  edges: Edge[] = []\r\n): MissingField[] {\r\n  console.log(`[${getTimestamp()}] [collectAllFields] ========== STARTING ALL FIELDS COLLECTION ==========`);\r\n  console.log(`[${getTimestamp()}] [collectAllFields] Total nodes: ${nodes.length}, Connected nodes: ${connectedNodes.size}, Edges: ${edges.length}`);\r\n  console.log(`[${getTimestamp()}] [collectAllFields] Connected node IDs: ${Array.from(connectedNodes).join(', ')}`);\r\n\r\n  // Find the StartNode\r\n  const startNode = nodes.find(node => node.data.originalType === \"StartNode\");\r\n\r\n  // Get directly connected nodes and fields if StartNode exists\r\n  let directlyConnectedNodes = new Set<string>();\r\n  let directlyConnectedFields = new Map<string, Set<string>>();\r\n  if (startNode) {\r\n    // Import the functions from utils\r\n    const { getDirectlyConnectedNodes, getDirectlyConnectedFields } = require(\"./utils\");\r\n    directlyConnectedNodes = getDirectlyConnectedNodes(nodes, edges, startNode.id);\r\n    directlyConnectedFields = getDirectlyConnectedFields(nodes, edges, startNode.id);\r\n    console.log(`[${getTimestamp()}] [collectAllFields] Directly connected node IDs: ${Array.from(directlyConnectedNodes).join(', ')}`);\r\n\r\n    // Log directly connected fields\r\n    directlyConnectedFields.forEach((fieldNames, nodeId) => {\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Node ${nodeId} has directly connected fields: ${Array.from(fieldNames).join(', ')}`);\r\n    });\r\n  }\r\n\r\n  const allFields: MissingField[] = [];\r\n\r\n  // Check each node\r\n  nodes.forEach(node => {\r\n    console.log(`[${getTimestamp()}] [collectAllFields] Examining node: ${node.id} (${node.data.label || \"Unnamed\"})`);\r\n\r\n    // Skip nodes that are not connected to the start node\r\n    if (!connectedNodes.has(node.id)) {\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - not connected to start node`);\r\n      return;\r\n    }\r\n\r\n    // Skip nodes with no definition or inputs\r\n    if (!node.data?.definition?.inputs) {\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - no definition or inputs`);\r\n      return;\r\n    }\r\n\r\n    const inputs = node.data.definition.inputs;\r\n    const config = node.data.config || {};\r\n\r\n    // Log detailed node information\r\n    console.log(`[${getTimestamp()}] [collectAllFields] Node details:\r\n      - ID: ${node.id}\r\n      - Label: ${node.data.label || \"Unnamed\"}\r\n      - Type: ${node.data.type}\r\n      - Original Type: ${node.data.originalType}\r\n      - Position: (${node.position.x}, ${node.position.y})\r\n      - Total inputs: ${inputs.length}\r\n      - Config keys: ${Object.keys(config).join(', ') || \"none\"}\r\n      - Is MCP component: ${isMCPMarketplaceComponent(node) ? \"YES\" : \"NO\"}`);\r\n\r\n    // Check each input\r\n    inputs.forEach(input => {\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Examining input: ${input.name} (${input.display_name || input.name})`);\r\n\r\n      // For handle inputs, check if they're connected to other nodes\r\n      let isConnected = false;\r\n      if (input.is_handle) {\r\n        isConnected = isHandleConnected(node.id, input.name, edges);\r\n\r\n        // We no longer skip connected handles - we need to include them in the field list\r\n        // but mark them as connected so they don't show up in the execution dialog\r\n        if (isConnected) {\r\n          console.log(`[${getTimestamp()}] [collectAllFields] Handle input ${input.name} is connected, including in field list`);\r\n        } else {\r\n          console.log(`[${getTimestamp()}] [collectAllFields] Handle input ${input.name} is not connected, including in field list`);\r\n        }\r\n      }\r\n\r\n      // Log detailed input information\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Input details:\r\n        - Name: ${input.name}\r\n        - Display Name: ${input.display_name || input.name}\r\n        - Type: ${input.input_type}\r\n        - Required flag: ${input.required === undefined ? \"undefined\" : input.required}\r\n        - Is handle: ${input.is_handle ? \"YES\" : \"NO\"}\r\n        - Is connected: ${isConnected ? \"YES\" : \"NO\"}\r\n        - Has info: ${input.info ? \"YES\" : \"NO\"}\r\n        - Current config value: ${JSON.stringify(config[input.name])}`);\r\n\r\n      // Determine if this field is required\r\n      const required = isFieldRequired(node, input, isConnected);\r\n\r\n      // Check if the field has a value\r\n      const isEmpty = isValueEmpty(config[input.name], input.input_type);\r\n\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Field ${input.name} details:\r\n        - Required: ${required ? \"YES\" : \"NO\"}\r\n        - Is Empty: ${isEmpty ? \"YES\" : \"NO\"}\r\n        - Value: ${JSON.stringify(config[input.name])}`);\r\n\r\n      // Check if this specific field is directly connected to the Start node\r\n      const isNodeDirectlyConnected = directlyConnectedNodes.has(node.id);\r\n      const isFieldDirectlyConnected = directlyConnectedFields.get(node.id)?.has(input.name) || false;\r\n      const isRequired = input.required !== false; // Consider required unless explicitly marked as optional\r\n\r\n      // Log detailed connection information for debugging\r\n      console.log(`[${getTimestamp()}] [collectAllFields] Field ${node.data.label || \"\"}.${input.name} direct connection check:\r\n        - Node directly connected to Start: ${isNodeDirectlyConnected ? \"YES\" : \"NO\"}\r\n        - Field handle: ${input.name}\r\n        - Field directly connected fields map has node: ${directlyConnectedFields.has(node.id) ? \"YES\" : \"NO\"}\r\n        - Field directly connected to Start: ${isFieldDirectlyConnected ? \"YES\" : \"NO\"}\r\n        - All directly connected fields for this node: ${directlyConnectedFields.get(node.id) ? Array.from(directlyConnectedFields.get(node.id) || []).join(', ') : \"none\"}`);\r\n\r\n      // FIXED: Always include directly connected fields, regardless of whether they're required\r\n      // For other fields, include them if they're required\r\n      if (isRequired || isFieldDirectlyConnected) {\r\n        // Ensure we have valid values for all fields\r\n        const nodeName = node.data.label || \"Unknown Node\";\r\n        const fieldName = input.name || \"unnamed_field\";\r\n        const displayName = input.display_name || input.name || \"Unnamed Field\";\r\n        const inputType = input.input_type || \"string\";\r\n\r\n        console.log(`[${getTimestamp()}] [collectAllFields] Adding field with validated properties:\r\n          - Node Name: ${nodeName}\r\n          - Field Name: ${fieldName}\r\n          - Display Name: ${displayName}\r\n          - Input Type: ${inputType}\r\n          - Required: ${isRequired ? \"YES\" : \"NO\"}\r\n          - Directly connected to Start: ${isFieldDirectlyConnected ? \"YES\" : \"NO\"}\r\n          - Is Empty: ${isEmpty ? \"YES\" : \"NO\"}`);\r\n\r\n        // Extract schema information for JSON objects\r\n        let schema = undefined;\r\n        if (input.input_type === 'json' || input.input_type === 'object' || input.input_type === 'dict') {\r\n          // Check if this is an MCP component with schema information\r\n          if (node.data.definition?.mcp_info?.input_schema?.properties?.[input.name]) {\r\n            // Get schema from MCP input schema\r\n            const mcpSchema = node.data.definition.mcp_info.input_schema.properties[input.name];\r\n            schema = {\r\n              type: mcpSchema.type,\r\n              properties: mcpSchema.properties || {},\r\n              required: mcpSchema.required || []\r\n            };\r\n            console.log(`[${getTimestamp()}] [collectAllFields] Found MCP schema for field ${input.name}:`, schema);\r\n          } else if (input.properties) {\r\n            // Get schema from input properties\r\n            schema = {\r\n              type: 'object',\r\n              properties: input.properties,\r\n              required: input.required_keys || []\r\n            };\r\n            console.log(`[${getTimestamp()}] [collectAllFields] Using input properties as schema for field ${input.name}:`, schema);\r\n          } else if (input.name === 'keywords') {\r\n            // Special case for keywords field\r\n            schema = {\r\n              type: 'object',\r\n              properties: {\r\n                time: { type: 'string', description: 'Time for the script' },\r\n                objective: { type: 'string', description: 'Objective of the script' },\r\n                audience: { type: 'string', description: 'Audience for the script' },\r\n                gender: { type: 'string', description: 'Gender for the script' },\r\n                tone: { type: 'string', description: 'Tone of the script' },\r\n                speakers: { type: 'string', description: 'Speaker in the script' }\r\n              },\r\n              required: []\r\n            };\r\n            console.log(`[${getTimestamp()}] [collectAllFields] Using predefined schema for keywords field:`, schema);\r\n          }\r\n        }\r\n\r\n        console.log(`[${getTimestamp()}] [collectAllFields] Field ${nodeName}.${fieldName} connection status:\r\n          - Node connected to Start: ${connectedNodes.has(node.id) ? \"YES\" : \"NO\"}\r\n          - Node directly connected to Start: ${isNodeDirectlyConnected ? \"YES\" : \"NO\"}\r\n          - Field directly connected to Start: ${isFieldDirectlyConnected ? \"YES\" : \"NO\"}`);\r\n\r\n        allFields.push({\r\n          nodeId: node.id,\r\n          nodeName: nodeName,\r\n          name: fieldName,\r\n          displayName: displayName,\r\n          info: input.info || undefined,\r\n          inputType: inputType,\r\n          connected_to_start: connectedNodes.has(node.id),\r\n          directly_connected_to_start: isFieldDirectlyConnected,\r\n          required: isRequired || isFieldDirectlyConnected, // Set required to true if directly connected\r\n          isEmpty: isEmpty,\r\n          currentValue: isEmpty ? undefined : unwrapDualPurposeValue(config[input.name]), // Include current value if not empty, unwrapped\r\n          options: input.options,\r\n          schema: schema,\r\n          // Add handle connection properties\r\n          is_handle: input.is_handle || false,\r\n          is_connected: isConnected\r\n        });\r\n\r\n        console.log(`[${getTimestamp()}] [collectAllFields] Added field ${node.data.label || \"\"}.${input.name} to all fields list`);\r\n      } else {\r\n        console.log(`[${getTimestamp()}] [collectAllFields] Skipping field ${input.name} - not required and not directly connected to Start node`);\r\n      }\r\n    });\r\n  });\r\n\r\n  console.log(`[${getTimestamp()}] [collectAllFields] ========== ALL FIELDS COLLECTION COMPLETE ==========`);\r\n  console.log(`[${getTimestamp()}] [collectAllFields] Total fields found: ${allFields.length}`);\r\n\r\n  return allFields;\r\n}\r\n\r\n/**\r\n * Collects missing required fields from all nodes\r\n *\r\n * @param nodes The array of nodes to check\r\n * @param connectedNodes Set of node IDs connected to the start node\r\n * @param edges Optional array of edges in the workflow (needed to check if handles are connected)\r\n * @returns An array of missing field objects\r\n */\r\nexport function collectMissingRequiredFields(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  connectedNodes: Set<string>,\r\n  edges: Edge[] = []\r\n): MissingField[] {\r\n  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] ========== STARTING MISSING FIELDS COLLECTION ==========`);\r\n  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Total nodes: ${nodes.length}, Connected nodes: ${connectedNodes.size}, Edges: ${edges.length}`);\r\n  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Connected node IDs: ${Array.from(connectedNodes).join(', ')}`);\r\n\r\n  // Find the StartNode\r\n  const startNode = nodes.find(node => node.data.originalType === \"StartNode\");\r\n\r\n  // Get directly connected nodes and fields if StartNode exists\r\n  let directlyConnectedNodes = new Set<string>();\r\n  let directlyConnectedFields = new Map<string, Set<string>>();\r\n  if (startNode) {\r\n    // Import the functions from utils\r\n    const { getDirectlyConnectedNodes, getDirectlyConnectedFields } = require(\"./utils\");\r\n    directlyConnectedNodes = getDirectlyConnectedNodes(nodes, edges, startNode.id);\r\n    directlyConnectedFields = getDirectlyConnectedFields(nodes, edges, startNode.id);\r\n    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Directly connected node IDs: ${Array.from(directlyConnectedNodes).join(', ')}`);\r\n\r\n    // Log directly connected fields\r\n    directlyConnectedFields.forEach((fieldNames, nodeId) => {\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Node ${nodeId} has directly connected fields: ${Array.from(fieldNames).join(', ')}`);\r\n    });\r\n  }\r\n\r\n  const missingFields: MissingField[] = [];\r\n\r\n  // Check each node\r\n  nodes.forEach(node => {\r\n    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Examining node: ${node.id} (${node.data.label || \"Unnamed\"})`);\r\n\r\n    // Skip nodes that are not connected to the start node\r\n    if (!connectedNodes.has(node.id)) {\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - not connected to start node`);\r\n      return;\r\n    }\r\n\r\n    // Skip nodes with no definition or inputs\r\n    if (!node.data?.definition?.inputs) {\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - no definition or inputs`);\r\n      return;\r\n    }\r\n\r\n    const inputs = node.data.definition.inputs;\r\n    const config = node.data.config || {};\r\n\r\n    // Log detailed node information\r\n    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Node details:\r\n      - ID: ${node.id}\r\n      - Label: ${node.data.label || \"Unnamed\"}\r\n      - Type: ${node.data.type}\r\n      - Original Type: ${node.data.originalType}\r\n      - Position: (${node.position.x}, ${node.position.y})\r\n      - Total inputs: ${inputs.length}\r\n      - Config keys: ${Object.keys(config).join(', ') || \"none\"}\r\n      - Is MCP component: ${isMCPMarketplaceComponent(node) ? \"YES\" : \"NO\"}`);\r\n\r\n    // Check each input\r\n    inputs.forEach(input => {\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Examining input: ${input.name} (${input.display_name || input.name})`);\r\n\r\n      // Check if this is a handle input and if it's connected\r\n      let isConnected = false;\r\n      if (input.is_handle) {\r\n        isConnected = isHandleConnected(node.id, input.name, edges);\r\n\r\n        if (isConnected) {\r\n          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Handle input ${input.name} is connected`);\r\n        } else {\r\n          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Handle input ${input.name} is not connected, checking if it needs a value`);\r\n        }\r\n      }\r\n\r\n      // Log detailed input information\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Input details:\r\n        - Name: ${input.name}\r\n        - Display Name: ${input.display_name || input.name}\r\n        - Type: ${input.input_type}\r\n        - Required flag: ${input.required === undefined ? \"undefined\" : input.required}\r\n        - Is handle: ${input.is_handle ? \"YES\" : \"NO\"}\r\n        - Is connected: ${isConnected ? \"YES\" : \"NO\"}\r\n        - Has info: ${input.info ? \"YES\" : \"NO\"}\r\n        - Current config value: ${JSON.stringify(config[input.name])}`);\r\n\r\n      // Determine if this field is required, passing the connection status for handle inputs\r\n      const required = isFieldRequired(node, input, isConnected);\r\n\r\n      // Check if the field has a value\r\n      const isEmpty = isValueEmpty(config[input.name], input.input_type);\r\n\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} validation result:\r\n        - Required: ${required ? \"YES\" : \"NO\"}\r\n        - Is Empty: ${isEmpty ? \"YES\" : \"NO\"}\r\n        - Value: ${JSON.stringify(config[input.name])}`);\r\n\r\n      // Check if this specific field is directly connected to the Start node\r\n      const isNodeDirectlyConnected = directlyConnectedNodes.has(node.id);\r\n      const isFieldDirectlyConnected = directlyConnectedFields.get(node.id)?.has(input.name) || false;\r\n\r\n      // Log detailed connection information for debugging\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${node.data.label || \"\"}.${input.name} direct connection check:\r\n        - Node directly connected to Start: ${isNodeDirectlyConnected ? \"YES\" : \"NO\"}\r\n        - Field handle: ${input.name}\r\n        - Field directly connected fields map has node: ${directlyConnectedFields.has(node.id) ? \"YES\" : \"NO\"}\r\n        - Field directly connected to Start: ${isFieldDirectlyConnected ? \"YES\" : \"NO\"}\r\n        - All directly connected fields for this node: ${directlyConnectedFields.get(node.id) ? Array.from(directlyConnectedFields.get(node.id) || []).join(', ') : \"none\"}`);\r\n\r\n      // FIXED: Add fields that are either (required AND empty) OR directly connected to the Start node\r\n      // This ensures that fields with direct connections from Start node are always included\r\n      if ((required && isEmpty) || isFieldDirectlyConnected) {\r\n        // Ensure we have valid values for all fields\r\n        const nodeName = node.data.label || \"Unknown Node\";\r\n        const fieldName = input.name || \"unnamed_field\";\r\n        const displayName = input.display_name || input.name || \"Unnamed Field\";\r\n        const inputType = input.input_type || \"string\";\r\n\r\n        if (required && isEmpty) {\r\n          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] FOUND MISSING REQUIRED FIELD: ${node.data.label || \"\"}.${input.name}`);\r\n        } else if (isFieldDirectlyConnected) {\r\n          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] FOUND DIRECTLY CONNECTED FIELD: ${node.data.label || \"\"}.${input.name}`);\r\n        }\r\n\r\n        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Adding field with validated properties:\r\n          - Node Name: ${nodeName}\r\n          - Field Name: ${fieldName}\r\n          - Display Name: ${displayName}\r\n          - Input Type: ${inputType}\r\n          - Is Empty: ${isEmpty ? \"YES\" : \"NO\"}\r\n          - Current Value: ${JSON.stringify(config[input.name])}\r\n          - Directly connected to Start: ${isFieldDirectlyConnected ? \"YES\" : \"NO\"}`);\r\n\r\n        // Extract schema information for JSON objects\r\n        let schema = undefined;\r\n        if (input.input_type === 'json' || input.input_type === 'object' || input.input_type === 'dict') {\r\n          // Check if this is an MCP component with schema information\r\n          if (node.data.definition?.mcp_info?.input_schema?.properties?.[input.name]) {\r\n            // Get schema from MCP input schema\r\n            const mcpSchema = node.data.definition.mcp_info.input_schema.properties[input.name];\r\n            schema = {\r\n              type: mcpSchema.type,\r\n              properties: mcpSchema.properties || {},\r\n              required: mcpSchema.required || []\r\n            };\r\n            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Found MCP schema for field ${input.name}:`, schema);\r\n          } else if (input.properties) {\r\n            // Get schema from input properties\r\n            schema = {\r\n              type: 'object',\r\n              properties: input.properties,\r\n              required: input.required_keys || []\r\n            };\r\n            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Using input properties as schema for field ${input.name}:`, schema);\r\n          } else if (input.name === 'keywords') {\r\n            // Special case for keywords field\r\n            schema = {\r\n              type: 'object',\r\n              properties: {\r\n                time: { type: 'string', description: 'Time for the script' },\r\n                objective: { type: 'string', description: 'Objective of the script' },\r\n                audience: { type: 'string', description: 'Audience for the script' },\r\n                gender: { type: 'string', description: 'Gender for the script' },\r\n                tone: { type: 'string', description: 'Tone of the script' },\r\n                speakers: { type: 'string', description: 'Speaker in the script' }\r\n              },\r\n              required: []\r\n            };\r\n            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Using predefined schema for keywords field:`, schema);\r\n          }\r\n        }\r\n\r\n        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${nodeName}.${fieldName} connection status:\r\n          - Node connected to Start: ${connectedNodes.has(node.id) ? \"YES\" : \"NO\"}\r\n          - Node directly connected to Start: ${isNodeDirectlyConnected ? \"YES\" : \"NO\"}\r\n          - Field directly connected to Start: ${isFieldDirectlyConnected ? \"YES\" : \"NO\"}`);\r\n\r\n        missingFields.push({\r\n          nodeId: node.id,\r\n          nodeName: nodeName,\r\n          name: fieldName,\r\n          displayName: displayName,\r\n          info: input.info || undefined,\r\n          inputType: inputType,\r\n          // Add handle connection properties\r\n          is_handle: input.is_handle || false,\r\n          is_connected: isConnected,\r\n          connected_to_start: connectedNodes.has(node.id),\r\n          directly_connected_to_start: isFieldDirectlyConnected,\r\n          required: required || isFieldDirectlyConnected, // Set required to true if directly connected\r\n          isEmpty: isEmpty, // Include whether the field is empty\r\n          currentValue: isEmpty ? undefined : unwrapDualPurposeValue(config[input.name]), // Include current value if not empty, unwrapped\r\n          schema: schema,\r\n          options: input.options\r\n        });\r\n      } else if (required && !isEmpty) {\r\n        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} is required but already has a value, skipping`);\r\n      } else {\r\n        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} is not required, skipping`);\r\n      }\r\n    });\r\n  });\r\n\r\n  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] ========== MISSING FIELDS COLLECTION COMPLETE ==========`);\r\n  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Total missing fields found: ${missingFields.length}`);\r\n  if (missingFields.length > 0) {\r\n    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Missing fields summary:`);\r\n    missingFields.forEach((field, index) => {\r\n      console.log(`[${getTimestamp()}] [collectMissingRequiredFields]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);\r\n    });\r\n  }\r\n\r\n  return missingFields;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;;;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,KAAU;IACxC,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,OAAO;IAEnF,kEAAkE;IAClE,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,WAAW,OAAO;QACnE,wDAAwD;QACxD,6EAA6E;QAC7E,MAAM,OAAO,OAAO,IAAI,CAAC;QACzB,MAAM,mBAAmB,KAAK,QAAQ,CAAC;QACvC,MAAM,uBAAuB,KAAK,MAAM,KAAK,KAAK;QAClD,MAAM,sBAAsB,oBAAoB,KAAK,KAAK,CAAC,CAAA,MACzD,QAAQ,WAAW,QAAQ,mBAAmB,QAAQ,cAAc,QAAQ;QAG9E,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE;YAChD;YACA;YACA;YACA;YACA,WAAW,OAAO,MAAM,KAAK;QAC/B;QAEA,IAAI,wBAAwB,qBAAqB;YAC/C,QAAQ,GAAG,CAAC,CAAC,mEAAmE,CAAC,EAAE;YACnF,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE;YACtD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,CAAC,EAAE,MAAM,KAAK;YAEpE,qEAAqE;YACrE,IAAI,OAAO,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM;gBACzE,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,KAAK;oBACrC,QAAQ,GAAG,CAAC,CAAC,sDAAsD,CAAC,EAAE;oBACtE,OAAO;gBACT,EAAE,OAAO,GAAG;oBACV,QAAQ,GAAG,CAAC,CAAC,sEAAsE,CAAC,EAAE;oBACtF,OAAO,MAAM,KAAK;gBACpB;YACF;YAEA,OAAO,MAAM,KAAK;QACpB;IACF;IAEA,mEAAmE;IACnE,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM;QAC7D,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC,EAAE;YACjF,OAAO;QACT,EAAE,OAAO,GAAG;YACV,QAAQ,GAAG,CAAC,CAAC,iFAAiF,CAAC,EAAE;QACnG;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,+DAA+D,CAAC,EAAE;IAC/E,OAAO;AACT;AAWO,SAAS,cACd,QAAyB,EACzB,KAAU,EACV,MAAc,EACd,IAA6B;IAE7B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,wDAAwD,CAAC;IACxF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yBAAyB,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,YAAY,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC;IACrH,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2BAA2B,EAAE,QAAQ;IACpE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,8BAA8B,EAAE,MAAM,MAAM,SAAS,WAAW;IAC/F,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,8BAA8B,EAAE,SAAS,UAAU,EAAE;IACpF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yBAAyB,EAAE,KAAK,SAAS,CAAC,QAAQ;IAEjF,MAAM,SAA4B,EAAE;IAEpC,oCAAoC;IACpC,IAAI,SAAS,SAAS,EAAE;QACtB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,wDAAwD,EAAE,SAAS,IAAI,EAAE;QACxG,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,aAAa,OAAO,gBAAgB,MAAM,YAAY,CAAC,CAAC,SAAS,QAAQ;IAC/E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+BAA+B,EAAE,aAAa,QAAQ,KAAK,iBAAiB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC;IAEnI,0BAA0B;IAC1B,MAAM,UAAU,aAAa,OAAO,SAAS,UAAU;IACvD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,4BAA4B,EAAE,UAAU,QAAQ,MAAM;IAErF,qCAAqC;IACrC,IAAI,cAAc,SAAS;QACzB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,oDAAoD,EAAE,SAAS,YAAY,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC;QACzI,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,cAAc,EAClC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,aAAa,CAAC,EAC9C,SACA,QACA,SAAS,IAAI;QAGjB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kFAAkF,CAAC;QAClH,OAAO,QAAQ,+CAA+C;IAChE;IAEA,4CAA4C;IAC5C,IAAI,CAAC,cAAc,SAAS;QAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,gEAAgE,EAAE,SAAS,IAAI,EAAE;QAChH,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAQ,SAAS,UAAU;QACzB,KAAK;YACH,oBAAoB,UAAU,OAAO,QAAQ;YAC7C;QACF,KAAK;QACL,KAAK;QACL,KAAK;YACH,oBAAoB,UAAU,OAAO,QAAQ;YAC7C;QACF,KAAK;QACL,KAAK;QACL,KAAK;YACH,oBAAoB,UAAU,OAAO,QAAQ;YAC7C;IACJ;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2DAA2D,EAAE,OAAO,MAAM,EAAE;IAC3G,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,oBACP,QAAyB,EACzB,KAAU,EACV,MAAc,EACd,MAAyB;IAEzB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iDAAiD,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,YAAY,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC;IAC7I,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,oCAAoC,EAAE,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,CAAC,QAAQ;IAEpH,aAAa;IACb,IAAI,OAAO,UAAU,UAAU;QAC7B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iEAAiE,EAAE,OAAO,OAAO;QAChH,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,EACvC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,kBAAkB,CAAC,EACnD,SACA,QACA,SAAS,IAAI;QAGjB;IACF;IAEA,mBAAmB;IACnB,IAAI,SAAS,UAAU,KAAK,aAAa,MAAM,MAAM,GAAG,SAAS,UAAU,EAAE;QAC3E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0DAA0D,EAAE,MAAM,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QACrJ,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,EACvC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,mBAAmB,EAAE,SAAS,UAAU,CAAC,WAAW,CAAC,EACrF,WACA,QACA,SAAS,IAAI;IAGnB;IAEA,mBAAmB;IACnB,IAAI,SAAS,UAAU,KAAK,aAAa,MAAM,MAAM,GAAG,SAAS,UAAU,EAAE;QAC3E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0DAA0D,EAAE,MAAM,MAAM,CAAC,iBAAiB,EAAE,SAAS,UAAU,EAAE;QAChJ,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,mBAAmB,EACvC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,SAAS,UAAU,CAAC,WAAW,CAAC,EACpF,WACA,QACA,SAAS,IAAI;IAGnB;IAEA,gBAAgB;IAChB,IAAI,SAAS,OAAO,IAAI,CAAC,IAAI,OAAO,SAAS,OAAO,EAAE,IAAI,CAAC,QAAQ;QACjE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2EAA2E,EAAE,SAAS,OAAO,EAAE;QAC9H,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C,SAAS,aAAa,IAAI,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,qCAAqC,CAAC,EAChG,WACA,QACA,SAAS,IAAI;IAGnB;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kEAAkE,EAAE,OAAO,MAAM,EAAE;AACpH;AAEA;;;;;;;CAOC,GACD,SAAS,oBACP,QAAyB,EACzB,KAAU,EACV,MAAc,EACd,MAAyB;IAEzB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iDAAiD,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,YAAY,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC;IAC7I,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,6CAA6C,EAAE,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,CAAC,QAAQ;IAE7H,8BAA8B;IAC9B,MAAM,WAAW,OAAO,UAAU,WAAW,OAAO,SAAS;IAC7D,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yCAAyC,EAAE,SAAS,SAAS,EAAE,MAAM,WAAW;IAE/G,aAAa;IACb,IAAI,OAAO,aAAa,YAAY,MAAM,WAAW;QACnD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uEAAuE,EAAE,OAAO;QAC/G,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,kBAAkB,EACtC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,kBAAkB,CAAC,EACnD,SACA,QACA,SAAS,IAAI;QAGjB;IACF;IAEA,kBAAkB;IAClB,IAAI,SAAS,SAAS,KAAK,aAAa,WAAW,OAAO,SAAS,SAAS,GAAG;QAC7E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mDAAmD,EAAE,SAAS,sBAAsB,EAAE,SAAS,SAAS,EAAE;QACzI,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,kBAAkB,EACtC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,mBAAmB,EAAE,SAAS,SAAS,EAAE,EACzE,WACA,QACA,SAAS,IAAI;IAGnB;IAEA,kBAAkB;IAClB,IAAI,SAAS,SAAS,KAAK,aAAa,WAAW,OAAO,SAAS,SAAS,GAAG;QAC7E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mDAAmD,EAAE,SAAS,iBAAiB,EAAE,SAAS,SAAS,EAAE;QACpI,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,kBAAkB,EACtC,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,kBAAkB,EAAE,SAAS,SAAS,EAAE,EACxE,WACA,QACA,SAAS,IAAI;IAGnB;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kEAAkE,EAAE,OAAO,MAAM,EAAE;AACpH;AAEA;;;;;;;CAOC,GACD,SAAS,oBACP,QAAyB,EACzB,KAAU,EACV,MAAc,EACd,MAAyB;IAEzB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iDAAiD,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,YAAY,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC;IAC7I,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,oCAAoC,EAAE,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,CAAC,QAAQ;IAEpH,0CAA0C;IAC1C,IAAI,OAAO,UAAU,UAAU;QAC7B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,sEAAsE,CAAC;QACtG,IAAI;YACF,QAAQ,KAAK,KAAK,CAAC;YACnB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,4DAA4D,EAAE,KAAK,SAAS,CAAC,QAAQ;QACtH,EAAE,OAAO,GAAG;YACV,MAAM,eAAe,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;YAC7D,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0EAA0E,EAAE,cAAc;YACzH,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,gBACA,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,oBAAoB,CAAC,EACrD,SACA,QACA,SAAS,IAAI;YAGjB;QACF;IACF;IAEA,aAAa;IACb,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;QAC/C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kEAAkE,EAAE,OAAO,MAAM,QAAQ,EAAE,UAAU,MAAM;QAC1I,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,2BAA2B,EAC/C,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,mBAAmB,CAAC,EACpD,SACA,QACA,SAAS,IAAI;QAGjB;IACF;IAEA,sBAAsB;IACtB,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;QAC/D,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,oDAAoD,EAAE,SAAS,aAAa,CAAC,IAAI,CAAC,OAAO;QACxH,MAAM,cAAc,SAAS,aAAa,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,CAAC,OAAO,KAAK;QAEvE,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iEAAiE,EAAE,YAAY,IAAI,CAAC,OAAO;YAC1H,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,2BAA2B,EAC/C,CAAC,OAAO,EAAE,SAAS,YAAY,CAAC,4BAA4B,EAAE,YAAY,IAAI,CAAC,OAAO,EACtF,SACA,QACA,SAAS,IAAI;QAGnB,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,qDAAqD,CAAC;QACvF;IACF,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kEAAkE,CAAC;IACpG;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kEAAkE,EAAE,OAAO,MAAM,EAAE;AACpH;AAQO,SAAS,eAAe,IAA4B;IACzD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2EAA2E,CAAC;IAC3G,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yBAAyB,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,CAAC;IACrG,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yBAAyB,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,YAAY,EAAE;IAEpH,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,sDAAsD;IACtD,IAAI,CAAC,KAAK,IAAI,EAAE,YAAY,QAAQ;QAClC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yEAAyE,CAAC;QACzG,OAAO;YAAE,SAAS;YAAM;YAAQ;YAAU;QAAM;IAClD;IAEA,MAAM,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM;IAC1C,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;IAEpC,gCAAgC;IAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;UACvB,EAAE,KAAK,EAAE,CAAC;aACP,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU;YAChC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;qBACR,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;iBAC7B,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACnC,EAAE,OAAO,MAAM,CAAC;mBACjB,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,OAAO;wBACtC,EAAE,0BAA0B,QAAQ,QAAQ,MAAM;IAExE,sBAAsB;IACtB,OAAO,OAAO,CAAC,CAAA;QACb,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,qCAAqC,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;QAExH,8EAA8E;QAC9E,MAAM,cAAc,cAAc,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;QAEtE,yBAAyB;QACzB,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yBAAyB,EAAE,YAAY,MAAM,CAAC,6BAA6B,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YACzH,YAAY,OAAO,CAAC,CAAC,OAAO;gBAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,qBAAqB,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC,WAAW,GAAG,EAAE,EAAE,MAAM,OAAO,EAAE;YACtH;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yBAAyB,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC;QAC1F;QAEA,8BAA8B;QAC9B,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,MAAM,QAAQ,KAAK,SAAS;gBAC9B,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,MAAM,QAAQ,KAAK,WAAW;gBACvC,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,MAAM,QAAQ,KAAK,QAAQ;gBACpC,MAAM,IAAI,CAAC;YACb;QACF;IACF;IAEA,yBAAyB;IACzB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kEAAkE,CAAC;IAClG,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU,EAAE,CAAC;IAC5H,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,6BAA6B,EAAE,OAAO,MAAM,EAAE;IAC7E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+BAA+B,EAAE,SAAS,MAAM,EAAE;IACjF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,4BAA4B,EAAE,MAAM,MAAM,EAAE;IAC3E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+BAA+B,EAAE,OAAO,MAAM,KAAK,IAAI,QAAQ,MAAM;IAEpG,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;IACF;AACF;AAEA;;;CAGC,GACD,SAAS;IACP,OAAO,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;AACjE;AAEA;;;;;;;CAOC,GACD,SAAS,yBACP,gBAA2D,EAC3D,gBAAoC,EACpC,MAA2B;IAE3B,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;QACtD,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,wCAAwC,EAAE,iBAAiB,MAAM,CAAC,+BAA+B,EAAE,oBAAoB,MAAM;IAE5J,MAAM,QAAQ,oBAAoB;IAClC,MAAM,UAAU,iBAAiB,GAAG,CAAC,CAAA;QACnC,MAAM,cAAc,MAAM,CAAC,KAAK,UAAU,CAAC;QAC3C,MAAM,WAAW,KAAK,QAAQ,IAAI;QAElC,IAAI,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,cAAc,gBAAgB,KAAK,WAAW;gBAC9C;YACF,KAAK;gBACH,cAAc,gBAAgB,KAAK,WAAW;gBAC9C;YACF,KAAK;gBACH,cAAc,OAAO,gBAAgB,YAAY,YAAY,QAAQ,CAAC,KAAK,WAAW;gBACtF;YACF,KAAK;gBACH,cAAc,gBAAgB,aAAa,gBAAgB;gBAC3D;YACF,KAAK;gBACH,cAAc,gBAAgB,aAAa,gBAAgB;gBAC3D;YACF;gBACE,cAAc,gBAAgB,KAAK,WAAW;QAClD;QAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mCAAmC,EAAE,KAAK,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE,YAAY,WAAW,EAAE,aAAa;QACvK,OAAO;IACT;IAEA,MAAM,cAAc,UAAU,QAAQ,QAAQ,KAAK,CAAC,CAAA,IAAK,KAAK,QAAQ,IAAI,CAAC,CAAA,IAAK;IAChF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+CAA+C,EAAE,MAAM,QAAQ,EAAE,aAAa;IAE7G,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,0BAA0B,IAA4B;IAC7D,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,OAAO;IAEhC,yDAAyD;IACzD,MAAM,QACJ,KAAK,IAAI,CAAC,IAAI,KAAK,SACnB,KAAK,IAAI,CAAC,YAAY,KAAK,6BAC3B,KAAK,IAAI,CAAC,IAAI,KAAK,6BAClB,KAAK,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,6BACtD,KAAK,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,IACrD,KAAK,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,IACjD,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,sBACnC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB;IAGxD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mCAAmC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU;YACnG,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;oBACT,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;uBACtB,EAAE,KAAK,IAAI,EAAE,YAAY,KAAK;oBACjC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE,YAAY,SAAS;YAC5C,EAAE,KAAK,IAAI,EAAE,YAAY,KAAK;cAC5B,EAAE,QAAQ,qBAAqB,qBAAqB;IAEhE,OAAO,CAAC,CAAC,OAAO,qBAAqB;AACvC;AAUO,SAAS,gBACd,IAA4B,EAC5B,KAAsB,EACtB,oBAA6B,KAAK;IAElC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;YACrB,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU;aAC1C,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,CAAC;kBAC7C,EAAE,MAAM,UAAU,CAAC;2BACV,EAAE,MAAM,QAAQ,KAAK,OAAO,QAAQ,KAAK;iBACnD,EAAE,MAAM,SAAS,GAAG,QAAQ,KAAK;2BACvB,EAAE,oBAAoB,QAAQ,KAAK;yBACrC,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,QAAQ,KAAK;6BAC5C,EAAE,MAAM,iBAAiB,IAAI,MAAM,iBAAiB,CAAC,MAAM,GAAG,IAAI,QAAQ,MAAM;IAE3G,yFAAyF;IACzF,IAAI,MAAM,iBAAiB,IAAI,MAAM,iBAAiB,CAAC,MAAM,GAAG,GAAG;QACjE,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QACpC,MAAM,oBAAoB,yBACxB,MAAM,iBAAiB,EACvB,MAAM,iBAAiB,EACvB;QAGF,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,iCAAiC,CAAC;YAExG,4DAA4D;YAC5D,IAAI,MAAM,SAAS,IAAI,MAAM,UAAU,KAAK,YAAY,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY;gBACtF,IAAI,mBAAmB;oBACrB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,4FAA4F,CAAC;oBACnK,OAAO;gBACT,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,wFAAwF,CAAC;oBAC/J,OAAO;gBACT;YACF;YAEA,OAAO;QACT;IACF;IAEA,kDAAkD;IAClD,IAAI,MAAM,QAAQ,KAAK,MAAM;QAC3B,4DAA4D;QAC5D,IAAI,MAAM,SAAS,IAAI,MAAM,UAAU,KAAK,YAAY,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY;YACtF,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,kEAAkE,CAAC;gBACzI,OAAO;YACT,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,+DAA+D,CAAC;gBACtI,OAAO;YACT;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,iCAAiC,CAAC;QACxG,OAAO;IACT;IAEA,6FAA6F;IAC7F,IAAI,MAAM,SAAS,IAAI,MAAM,UAAU,KAAK,YAAY,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY;QACtF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,6EAA6E,CAAC;QACpJ,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,QAAQ,0BAA0B;IACxC,IAAI,OAAO;QACT,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+EAA+E,EAAE,MAAM,IAAI,EAAE;QAE5H,2CAA2C;QAC3C,MAAM,uBAAuB;YAAC;YAAQ;YAAW;SAAW;QAC5D,IAAI,qBAAqB,QAAQ,CAAC,MAAM,IAAI,GAAG;YAC7C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,MAAM,IAAI,CAAC,yDAAyD,CAAC;YAChI,OAAO;QACT;QAEA,+FAA+F;QAC/F,qEAAqE;QACrE,MAAM,aAAa,MAAM,QAAQ,KAAK;QACtC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,wCAAwC,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,aAAa,aAAa,WAAW,sBAAsB,EAAE,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC;QAChM,OAAO;IACT;IAEA,oGAAoG;IACpG,qEAAqE;IACrE,MAAM,aAAa,MAAM,QAAQ,KAAK;IACtC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,6CAA6C,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,aAAa,aAAa,WAAW,sBAAsB,EAAE,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC;IACrM,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,KAAU,EAAE,SAAiB;IACjD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;kBACf,EAAE,UAAU;kBACZ,EAAE,OAAO,MAAM;aACpB,EAAE,KAAK,SAAS,CAAC,OAAO;oBACjB,EAAE,UAAU,YAAY,QAAQ,KAAK;eAC1C,EAAE,UAAU,OAAO,QAAQ,MAAM;IAE9C,4CAA4C;IAC5C,IAAI,UAAU,aAAa,UAAU,MAAM;QACzC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,6DAA6D,CAAC;QAC7F,OAAO;IACT;IAEA,mEAAmE;IACnE,IAAI,cAAc,aAAa,cAAc,QAAQ;QACnD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+BAA+B,EAAE,MAAM,+CAA+C,CAAC;QACtH,OAAO;IACT;IAEA,yCAAyC;IACzC,IAAI,cAAc,YAAY,cAAc,SAAS,cAAc,SAAS;QAC1E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+BAA+B,EAAE,MAAM,2CAA2C,CAAC;QAClH,OAAO;IACT;IAEA,qCAAqC;IACrC,IAAI,cAAc,YAAY,cAAc,QAAQ;QAClD,MAAM,UAAU,UAAU;QAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+BAA+B,EAAE,MAAM,cAAc,EAAE,UAAU,UAAU,aAAa;QACvH,OAAO;IACT;IAEA,oEAAoE;IACpE,IACE,CAAC,cAAc,YAAY,cAAc,UAAU,cAAc,MAAM,KACvE,OAAO,UAAU,UACjB;QACA,MAAM,UAAU,OAAO,IAAI,CAAC,OAAO,MAAM,KAAK;QAC9C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mCAAmC,EAAE,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB,EAAE,UAAU,UAAU,aAAa;QACnJ,OAAO;IACT;IAEA,IAAI,CAAC,cAAc,WAAW,cAAc,MAAM,KAAK,MAAM,OAAO,CAAC,QAAQ;QAC3E,MAAM,UAAU,MAAM,MAAM,KAAK;QACjC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kCAAkC,EAAE,MAAM,MAAM,CAAC,mBAAmB,EAAE,UAAU,UAAU,aAAa;QACtI,OAAO;IACT;IAEA,uEAAuE;IACvE,IACE,CAAC,cAAc,YAAY,cAAc,UAAU,cAAc,UAChE,cAAc,WAAW,cAAc,MAAM,KAC9C,OAAO,UAAU,UACjB;QACA,MAAM,UAAU,MAAM,IAAI;QAC1B,MAAM,UAAU,YAAY,MAAM,YAAY,QAAQ,YAAY;QAClE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yDAAyD,EAAE,QAAQ,cAAc,EAAE,UAAU,UAAU,aAAa;QACnJ,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,UAAU,UAAU;IAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uCAAuC,EAAE,MAAM,cAAc,EAAE,UAAU,UAAU,aAAa;IAC/H,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,kBAAkB,MAAc,EAAE,SAAiB,EAAE,KAAa;IACzE,qFAAqF;IACrF,MAAM,cAAc,MAAM,IAAI,CAAC,CAAA,OAC7B,KAAK,MAAM,KAAK,UAAU,KAAK,YAAY,KAAK;IAGlD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yCAAyC,EAAE,OAAO,CAAC,EAAE,UAAU,eAAe,EAAE,cAAc,QAAQ,MAAM;IAE3I,OAAO;AACT;AAUO,SAAS,iBACd,KAA+B,EAC/B,cAA2B,EAC3B,QAAgB,EAAE;IAElB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yEAAyE,CAAC;IACzG,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kCAAkC,EAAE,MAAM,MAAM,CAAC,mBAAmB,EAAE,eAAe,IAAI,CAAC,SAAS,EAAE,MAAM,MAAM,EAAE;IAClJ,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yCAAyC,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;IAEjH,qBAAqB;IACrB,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,YAAY,KAAK;IAEhE,8DAA8D;IAC9D,IAAI,yBAAyB,IAAI;IACjC,IAAI,0BAA0B,IAAI;IAClC,IAAI,WAAW;QACb,kCAAkC;QAClC,MAAM,EAAE,yBAAyB,EAAE,0BAA0B,EAAE;QAC/D,yBAAyB,0BAA0B,OAAO,OAAO,UAAU,EAAE;QAC7E,0BAA0B,2BAA2B,OAAO,OAAO,UAAU,EAAE;QAC/E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kDAAkD,EAAE,MAAM,IAAI,CAAC,wBAAwB,IAAI,CAAC,OAAO;QAElI,gCAAgC;QAChC,wBAAwB,OAAO,CAAC,CAAC,YAAY;YAC3C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,0BAA0B,EAAE,OAAO,gCAAgC,EAAE,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO;QACzI;IACF;IAEA,MAAM,YAA4B,EAAE;IAEpC,kBAAkB;IAClB,MAAM,OAAO,CAAC,CAAA;QACZ,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,qCAAqC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,CAAC;QAEjH,sDAAsD;QACtD,IAAI,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,GAAG;YAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mCAAmC,EAAE,KAAK,EAAE,CAAC,8BAA8B,CAAC;YAC3G;QACF;QAEA,0CAA0C;QAC1C,IAAI,CAAC,KAAK,IAAI,EAAE,YAAY,QAAQ;YAClC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mCAAmC,EAAE,KAAK,EAAE,CAAC,0BAA0B,CAAC;YACvG;QACF;QAEA,MAAM,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM;QAC1C,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,gCAAgC;QAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;YACvB,EAAE,KAAK,EAAE,CAAC;eACP,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU;cAChC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;uBACR,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;mBAC7B,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;sBACnC,EAAE,OAAO,MAAM,CAAC;qBACjB,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,OAAO;0BACtC,EAAE,0BAA0B,QAAQ,QAAQ,MAAM;QAExE,mBAAmB;QACnB,OAAO,OAAO,CAAC,CAAA;YACb,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,sCAAsC,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;YAEzH,+DAA+D;YAC/D,IAAI,cAAc;YAClB,IAAI,MAAM,SAAS,EAAE;gBACnB,cAAc,kBAAkB,KAAK,EAAE,EAAE,MAAM,IAAI,EAAE;gBAErD,kFAAkF;gBAClF,2EAA2E;gBAC3E,IAAI,aAAa;oBACf,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kCAAkC,EAAE,MAAM,IAAI,CAAC,sCAAsC,CAAC;gBACvH,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kCAAkC,EAAE,MAAM,IAAI,CAAC,0CAA0C,CAAC;gBAC3H;YACF;YAEA,iCAAiC;YACjC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;gBACrB,EAAE,MAAM,IAAI,CAAC;wBACL,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,CAAC;gBAC3C,EAAE,MAAM,UAAU,CAAC;yBACV,EAAE,MAAM,QAAQ,KAAK,YAAY,cAAc,MAAM,QAAQ,CAAC;qBAClE,EAAE,MAAM,SAAS,GAAG,QAAQ,KAAK;wBAC9B,EAAE,cAAc,QAAQ,KAAK;oBACjC,EAAE,MAAM,IAAI,GAAG,QAAQ,KAAK;gCAChB,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;YAEhE,sCAAsC;YACtC,MAAM,WAAW,gBAAgB,MAAM,OAAO;YAE9C,iCAAiC;YACjC,MAAM,UAAU,aAAa,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,MAAM,UAAU;YAEjE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2BAA2B,EAAE,MAAM,IAAI,CAAC;oBACzD,EAAE,WAAW,QAAQ,KAAK;oBAC1B,EAAE,UAAU,QAAQ,KAAK;iBAC5B,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;YAEjD,uEAAuE;YACvE,MAAM,0BAA0B,uBAAuB,GAAG,CAAC,KAAK,EAAE;YAClE,MAAM,2BAA2B,wBAAwB,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,MAAM,IAAI,KAAK;YAC1F,MAAM,aAAa,MAAM,QAAQ,KAAK,OAAO,yDAAyD;YAEtG,oDAAoD;YACpD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2BAA2B,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC;4CAC1D,EAAE,0BAA0B,QAAQ,KAAK;wBAC7D,EAAE,MAAM,IAAI,CAAC;wDACmB,EAAE,wBAAwB,GAAG,CAAC,KAAK,EAAE,IAAI,QAAQ,KAAK;6CACjE,EAAE,2BAA2B,QAAQ,KAAK;uDAChC,EAAE,wBAAwB,GAAG,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,CAAC,wBAAwB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,QAAQ;YAEtK,0FAA0F;YAC1F,qDAAqD;YACrD,IAAI,cAAc,0BAA0B;gBAC1C,6CAA6C;gBAC7C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,IAAI;gBACpC,MAAM,YAAY,MAAM,IAAI,IAAI;gBAChC,MAAM,cAAc,MAAM,YAAY,IAAI,MAAM,IAAI,IAAI;gBACxD,MAAM,YAAY,MAAM,UAAU,IAAI;gBAEtC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;uBAChB,EAAE,SAAS;wBACV,EAAE,UAAU;0BACV,EAAE,YAAY;wBAChB,EAAE,UAAU;sBACd,EAAE,aAAa,QAAQ,KAAK;yCACT,EAAE,2BAA2B,QAAQ,KAAK;sBAC7D,EAAE,UAAU,QAAQ,MAAM;gBAExC,8CAA8C;gBAC9C,IAAI,SAAS;gBACb,IAAI,MAAM,UAAU,KAAK,UAAU,MAAM,UAAU,KAAK,YAAY,MAAM,UAAU,KAAK,QAAQ;oBAC/F,4DAA4D;oBAC5D,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,UAAU,cAAc,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;wBAC1E,mCAAmC;wBACnC,MAAM,YAAY,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC;wBACnF,SAAS;4BACP,MAAM,UAAU,IAAI;4BACpB,YAAY,UAAU,UAAU,IAAI,CAAC;4BACrC,UAAU,UAAU,QAAQ,IAAI,EAAE;wBACpC;wBACA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,gDAAgD,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;oBAClG,OAAO,IAAI,MAAM,UAAU,EAAE;wBAC3B,mCAAmC;wBACnC,SAAS;4BACP,MAAM;4BACN,YAAY,MAAM,UAAU;4BAC5B,UAAU,MAAM,aAAa,IAAI,EAAE;wBACrC;wBACA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,gEAAgE,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;oBAClH,OAAO,IAAI,MAAM,IAAI,KAAK,YAAY;wBACpC,kCAAkC;wBAClC,SAAS;4BACP,MAAM;4BACN,YAAY;gCACV,MAAM;oCAAE,MAAM;oCAAU,aAAa;gCAAsB;gCAC3D,WAAW;oCAAE,MAAM;oCAAU,aAAa;gCAA0B;gCACpE,UAAU;oCAAE,MAAM;oCAAU,aAAa;gCAA0B;gCACnE,QAAQ;oCAAE,MAAM;oCAAU,aAAa;gCAAwB;gCAC/D,MAAM;oCAAE,MAAM;oCAAU,aAAa;gCAAqB;gCAC1D,UAAU;oCAAE,MAAM;oCAAU,aAAa;gCAAwB;4BACnE;4BACA,UAAU,EAAE;wBACd;wBACA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,gEAAgE,CAAC,EAAE;oBACpG;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,2BAA2B,EAAE,SAAS,CAAC,EAAE,UAAU;qCACrD,EAAE,eAAe,GAAG,CAAC,KAAK,EAAE,IAAI,QAAQ,KAAK;8CACpC,EAAE,0BAA0B,QAAQ,KAAK;+CACxC,EAAE,2BAA2B,QAAQ,MAAM;gBAElF,UAAU,IAAI,CAAC;oBACb,QAAQ,KAAK,EAAE;oBACf,UAAU;oBACV,MAAM;oBACN,aAAa;oBACb,MAAM,MAAM,IAAI,IAAI;oBACpB,WAAW;oBACX,oBAAoB,eAAe,GAAG,CAAC,KAAK,EAAE;oBAC9C,6BAA6B;oBAC7B,UAAU,cAAc;oBACxB,SAAS;oBACT,cAAc,UAAU,YAAY,uBAAuB,MAAM,CAAC,MAAM,IAAI,CAAC;oBAC7E,SAAS,MAAM,OAAO;oBACtB,QAAQ;oBACR,mCAAmC;oBACnC,WAAW,MAAM,SAAS,IAAI;oBAC9B,cAAc;gBAChB;gBAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iCAAiC,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC;YAC5H,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,oCAAoC,EAAE,MAAM,IAAI,CAAC,wDAAwD,CAAC;YAC3I;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yEAAyE,CAAC;IACzG,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yCAAyC,EAAE,UAAU,MAAM,EAAE;IAE5F,OAAO;AACT;AAUO,SAAS,6BACd,KAA+B,EAC/B,cAA2B,EAC3B,QAAgB,EAAE;IAElB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yFAAyF,CAAC;IACzH,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,8CAA8C,EAAE,MAAM,MAAM,CAAC,mBAAmB,EAAE,eAAe,IAAI,CAAC,SAAS,EAAE,MAAM,MAAM,EAAE;IAC9J,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,qDAAqD,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;IAE7H,qBAAqB;IACrB,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,YAAY,KAAK;IAEhE,8DAA8D;IAC9D,IAAI,yBAAyB,IAAI;IACjC,IAAI,0BAA0B,IAAI;IAClC,IAAI,WAAW;QACb,kCAAkC;QAClC,MAAM,EAAE,yBAAyB,EAAE,0BAA0B,EAAE;QAC/D,yBAAyB,0BAA0B,OAAO,OAAO,UAAU,EAAE;QAC7E,0BAA0B,2BAA2B,OAAO,OAAO,UAAU,EAAE;QAC/E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,8DAA8D,EAAE,MAAM,IAAI,CAAC,wBAAwB,IAAI,CAAC,OAAO;QAE9I,gCAAgC;QAChC,wBAAwB,OAAO,CAAC,CAAC,YAAY;YAC3C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,sCAAsC,EAAE,OAAO,gCAAgC,EAAE,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO;QACrJ;IACF;IAEA,MAAM,gBAAgC,EAAE;IAExC,kBAAkB;IAClB,MAAM,OAAO,CAAC,CAAA;QACZ,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iDAAiD,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,CAAC;QAE7H,sDAAsD;QACtD,IAAI,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,GAAG;YAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+CAA+C,EAAE,KAAK,EAAE,CAAC,8BAA8B,CAAC;YACvH;QACF;QAEA,0CAA0C;QAC1C,IAAI,CAAC,KAAK,IAAI,EAAE,YAAY,QAAQ;YAClC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+CAA+C,EAAE,KAAK,EAAE,CAAC,0BAA0B,CAAC;YACnH;QACF;QAEA,MAAM,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM;QAC1C,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,gCAAgC;QAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;YACvB,EAAE,KAAK,EAAE,CAAC;eACP,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,UAAU;cAChC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;uBACR,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;mBAC7B,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;sBACnC,EAAE,OAAO,MAAM,CAAC;qBACjB,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,OAAO;0BACtC,EAAE,0BAA0B,QAAQ,QAAQ,MAAM;QAExE,mBAAmB;QACnB,OAAO,OAAO,CAAC,CAAA;YACb,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,kDAAkD,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;YAErI,wDAAwD;YACxD,IAAI,cAAc;YAClB,IAAI,MAAM,SAAS,EAAE;gBACnB,cAAc,kBAAkB,KAAK,EAAE,EAAE,MAAM,IAAI,EAAE;gBAErD,IAAI,aAAa;oBACf,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,8CAA8C,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC;gBAC1G,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,8CAA8C,EAAE,MAAM,IAAI,CAAC,+CAA+C,CAAC;gBAC5I;YACF;YAEA,iCAAiC;YACjC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;gBACrB,EAAE,MAAM,IAAI,CAAC;wBACL,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,CAAC;gBAC3C,EAAE,MAAM,UAAU,CAAC;yBACV,EAAE,MAAM,QAAQ,KAAK,YAAY,cAAc,MAAM,QAAQ,CAAC;qBAClE,EAAE,MAAM,SAAS,GAAG,QAAQ,KAAK;wBAC9B,EAAE,cAAc,QAAQ,KAAK;oBACjC,EAAE,MAAM,IAAI,GAAG,QAAQ,KAAK;gCAChB,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;YAEhE,uFAAuF;YACvF,MAAM,WAAW,gBAAgB,MAAM,OAAO;YAE9C,iCAAiC;YACjC,MAAM,UAAU,aAAa,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,MAAM,UAAU;YAEjE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uCAAuC,EAAE,MAAM,IAAI,CAAC;oBACrE,EAAE,WAAW,QAAQ,KAAK;oBAC1B,EAAE,UAAU,QAAQ,KAAK;iBAC5B,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;YAEjD,uEAAuE;YACvE,MAAM,0BAA0B,uBAAuB,GAAG,CAAC,KAAK,EAAE;YAClE,MAAM,2BAA2B,wBAAwB,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,MAAM,IAAI,KAAK;YAE1F,oDAAoD;YACpD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uCAAuC,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC;4CACtE,EAAE,0BAA0B,QAAQ,KAAK;wBAC7D,EAAE,MAAM,IAAI,CAAC;wDACmB,EAAE,wBAAwB,GAAG,CAAC,KAAK,EAAE,IAAI,QAAQ,KAAK;6CACjE,EAAE,2BAA2B,QAAQ,KAAK;uDAChC,EAAE,wBAAwB,GAAG,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,CAAC,wBAAwB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,QAAQ;YAEtK,iGAAiG;YACjG,uFAAuF;YACvF,IAAI,AAAC,YAAY,WAAY,0BAA0B;gBACrD,6CAA6C;gBAC7C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,IAAI;gBACpC,MAAM,YAAY,MAAM,IAAI,IAAI;gBAChC,MAAM,cAAc,MAAM,YAAY,IAAI,MAAM,IAAI,IAAI;gBACxD,MAAM,YAAY,MAAM,UAAU,IAAI;gBAEtC,IAAI,YAAY,SAAS;oBACvB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,+DAA+D,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,EAAE;gBACvI,OAAO,IAAI,0BAA0B;oBACnC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,iEAAiE,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,EAAE;gBACzI;gBAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe;uBAChB,EAAE,SAAS;wBACV,EAAE,UAAU;0BACV,EAAE,YAAY;wBAChB,EAAE,UAAU;sBACd,EAAE,UAAU,QAAQ,KAAK;2BACpB,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;yCACvB,EAAE,2BAA2B,QAAQ,MAAM;gBAE5E,8CAA8C;gBAC9C,IAAI,SAAS;gBACb,IAAI,MAAM,UAAU,KAAK,UAAU,MAAM,UAAU,KAAK,YAAY,MAAM,UAAU,KAAK,QAAQ;oBAC/F,4DAA4D;oBAC5D,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,UAAU,cAAc,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;wBAC1E,mCAAmC;wBACnC,MAAM,YAAY,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC;wBACnF,SAAS;4BACP,MAAM,UAAU,IAAI;4BACpB,YAAY,UAAU,UAAU,IAAI,CAAC;4BACrC,UAAU,UAAU,QAAQ,IAAI,EAAE;wBACpC;wBACA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,4DAA4D,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;oBAC9G,OAAO,IAAI,MAAM,UAAU,EAAE;wBAC3B,mCAAmC;wBACnC,SAAS;4BACP,MAAM;4BACN,YAAY,MAAM,UAAU;4BAC5B,UAAU,MAAM,aAAa,IAAI,EAAE;wBACrC;wBACA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,4EAA4E,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;oBAC9H,OAAO,IAAI,MAAM,IAAI,KAAK,YAAY;wBACpC,kCAAkC;wBAClC,SAAS;4BACP,MAAM;4BACN,YAAY;gCACV,MAAM;oCAAE,MAAM;oCAAU,aAAa;gCAAsB;gCAC3D,WAAW;oCAAE,MAAM;oCAAU,aAAa;gCAA0B;gCACpE,UAAU;oCAAE,MAAM;oCAAU,aAAa;gCAA0B;gCACnE,QAAQ;oCAAE,MAAM;oCAAU,aAAa;gCAAwB;gCAC/D,MAAM;oCAAE,MAAM;oCAAU,aAAa;gCAAqB;gCAC1D,UAAU;oCAAE,MAAM;oCAAU,aAAa;gCAAwB;4BACnE;4BACA,UAAU,EAAE;wBACd;wBACA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,4EAA4E,CAAC,EAAE;oBAChH;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uCAAuC,EAAE,SAAS,CAAC,EAAE,UAAU;qCACjE,EAAE,eAAe,GAAG,CAAC,KAAK,EAAE,IAAI,QAAQ,KAAK;8CACpC,EAAE,0BAA0B,QAAQ,KAAK;+CACxC,EAAE,2BAA2B,QAAQ,MAAM;gBAElF,cAAc,IAAI,CAAC;oBACjB,QAAQ,KAAK,EAAE;oBACf,UAAU;oBACV,MAAM;oBACN,aAAa;oBACb,MAAM,MAAM,IAAI,IAAI;oBACpB,WAAW;oBACX,mCAAmC;oBACnC,WAAW,MAAM,SAAS,IAAI;oBAC9B,cAAc;oBACd,oBAAoB,eAAe,GAAG,CAAC,KAAK,EAAE;oBAC9C,6BAA6B;oBAC7B,UAAU,YAAY;oBACtB,SAAS;oBACT,cAAc,UAAU,YAAY,uBAAuB,MAAM,CAAC,MAAM,IAAI,CAAC;oBAC7E,QAAQ;oBACR,SAAS,MAAM,OAAO;gBACxB;YACF,OAAO,IAAI,YAAY,CAAC,SAAS;gBAC/B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uCAAuC,EAAE,MAAM,IAAI,CAAC,8CAA8C,CAAC;YACpI,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,uCAAuC,EAAE,MAAM,IAAI,CAAC,0BAA0B,CAAC;YAChH;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,yFAAyF,CAAC;IACzH,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,6DAA6D,EAAE,cAAc,MAAM,EAAE;IACpH,IAAI,cAAc,MAAM,GAAG,GAAG;QAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,wDAAwD,CAAC;QACxF,cAAc,OAAO,CAAC,CAAC,OAAO;YAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,mCAAmC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC,UAAU,EAAE,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,MAAM,SAAS,EAAE;QACnM;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/workflowValidation.ts"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { ValidationError, ValidationErrorCode, ValidationResult, WorkflowValidationOptions, MissingField } from \"./types\";\r\nimport { createValidationError } from \"./errors\";\r\nimport { validateNodes } from \"./nodeValidation\";\r\nimport { validateEdges } from \"./edgeValidation\";\r\nimport { validateStartNode, validateConnectivity, detectCyclesInWorkflow } from \"./connectivityValidation\";\r\nimport { collectMissingRequiredFields } from \"./fieldValidation\";\r\n\r\n/**\r\n * Validates the basic structure of a workflow\r\n *\r\n * @param workflowData The workflow data to validate\r\n * @returns A validation result\r\n */\r\nexport function validateWorkflowStructure(workflowData: any): ValidationResult {\r\n  const errors: ValidationError[] = [];\r\n  const warnings: ValidationError[] = [];\r\n  const infos: ValidationError[] = [];\r\n\r\n  // Check if workflow is a valid object\r\n  if (!workflowData || typeof workflowData !== \"object\") {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_INVALID_JSON,\r\n        \"Workflow must be a valid JSON object\"\r\n      )\r\n    );\r\n    return { isValid: false, errors, warnings, infos };\r\n  }\r\n\r\n  // Check if nodes array exists\r\n  if (!workflowData.nodes || !Array.isArray(workflowData.nodes)) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_MISSING_NODES,\r\n        'Workflow must contain a \"nodes\" array'\r\n      )\r\n    );\r\n  }\r\n\r\n  // Check if edges array exists\r\n  if (!workflowData.edges || !Array.isArray(workflowData.edges)) {\r\n    errors.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_MISSING_EDGES,\r\n        'Workflow must contain an \"edges\" array'\r\n      )\r\n    );\r\n  }\r\n\r\n  // Check workflow_name if present\r\n  if (workflowData.workflow_name && typeof workflowData.workflow_name !== \"string\") {\r\n    warnings.push(\r\n      createValidationError(\r\n        ValidationErrorCode.WORKFLOW_INVALID_NAME,\r\n        \"Workflow name must be a string\",\r\n        \"warning\"\r\n      )\r\n    );\r\n  }\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n  };\r\n}\r\n\r\n/**\r\n * Validates a complete workflow with enhanced initialization checks\r\n *\r\n * @param nodes The array of nodes to validate\r\n * @param edges The array of edges to validate\r\n * @param options Validation options\r\n * @returns A validation result\r\n */\r\nexport function validateWorkflow(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  options: WorkflowValidationOptions = {\r\n    validateConnectivity: true,\r\n    collectMissingFields: true,\r\n    validateFieldTypes: true,\r\n    validateCycles: true,\r\n  }\r\n): ValidationResult {\r\n  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);\r\n  console.log(`[${timestamp}] [validateWorkflow] Starting workflow validation with ${nodes?.length || 0} nodes and ${edges?.length || 0} edges`);\r\n\r\n  // Check if nodes array is valid\r\n  if (!nodes || !Array.isArray(nodes)) {\r\n    console.error(`[${timestamp}] [validateWorkflow] Nodes array is invalid or undefined`);\r\n    return {\r\n      isValid: false,\r\n      errors: [\r\n        createValidationError(\r\n          ValidationErrorCode.WORKFLOW_MISSING_NODES,\r\n          \"Workflow nodes array is invalid or undefined\"\r\n        )\r\n      ],\r\n      warnings: [],\r\n      infos: []\r\n    };\r\n  }\r\n\r\n  // Log the nodes array for debugging\r\n  console.log(`[${timestamp}] [validateWorkflow] Nodes array:`, nodes.map(node => ({\r\n    id: node.id,\r\n    type: node.type,\r\n    dataType: node.data?.type,\r\n    originalType: node.data?.originalType,\r\n    label: node.data?.label\r\n  })));\r\n\r\n  // Check if edges array is valid\r\n  if (!edges || !Array.isArray(edges)) {\r\n    console.error(`[${timestamp}] [validateWorkflow] Edges array is invalid or undefined`);\r\n    return {\r\n      isValid: false,\r\n      errors: [\r\n        createValidationError(\r\n          ValidationErrorCode.WORKFLOW_MISSING_EDGES,\r\n          \"Workflow edges array is invalid or undefined\"\r\n        )\r\n      ],\r\n      warnings: [],\r\n      infos: []\r\n    };\r\n  }\r\n\r\n  // Check if workflow is empty\r\n  if (nodes.length === 0) {\r\n    console.warn(`[${timestamp}] [validateWorkflow] Workflow is empty (no nodes)`);\r\n    return {\r\n      isValid: false,\r\n      errors: [\r\n        createValidationError(\r\n          ValidationErrorCode.WORKFLOW_EMPTY,\r\n          \"Workflow is empty. Please add at least a Start node.\"\r\n        )\r\n      ],\r\n      warnings: [],\r\n      infos: []\r\n    };\r\n  }\r\n\r\n  // Log node types for debugging\r\n  console.log(`[${timestamp}] [validateWorkflow] Node types in workflow:`,\r\n    nodes.map(node => ({\r\n      id: node.id,\r\n      type: node.type,\r\n      dataType: node.data?.type,\r\n      originalType: node.data?.originalType,\r\n      definitionName: node.data?.definition?.name,\r\n      label: node.data?.label\r\n    }))\r\n  );\r\n\r\n  // Step 1: Validate nodes structure\r\n  console.log(`[${timestamp}] [validateWorkflow] Step 1: Validating node structure`);\r\n  const nodeValidation = validateNodes(nodes);\r\n\r\n  // Step 2: Validate edges structure\r\n  console.log(`[${timestamp}] [validateWorkflow] Step 2: Validating edge structure`);\r\n  const edgeValidation = validateEdges(edges, nodes);\r\n\r\n  // Combine validation results\r\n  const errors = [...nodeValidation.errors, ...edgeValidation.errors];\r\n  const warnings = [...nodeValidation.warnings, ...edgeValidation.warnings];\r\n  const infos = [...nodeValidation.infos, ...edgeValidation.infos];\r\n\r\n  // If there are structural errors, return early\r\n  if (errors.length > 0) {\r\n    console.warn(`[${timestamp}] [validateWorkflow] Found ${errors.length} structural errors, returning early`);\r\n    return {\r\n      isValid: false,\r\n      errors,\r\n      warnings,\r\n      infos,\r\n    };\r\n  }\r\n\r\n  // Step 3: Validate StartNode presence\r\n  console.log(`[${timestamp}] [validateWorkflow] Step 3: Validating StartNode presence`);\r\n  const startNodeValidation = validateStartNode(nodes);\r\n  errors.push(...startNodeValidation.errors);\r\n  warnings.push(...startNodeValidation.warnings);\r\n  infos.push(...startNodeValidation.infos);\r\n\r\n  // If there's no StartNode, return early\r\n  if (!startNodeValidation.isValid) {\r\n    console.warn(`[${timestamp}] [validateWorkflow] No StartNode found, validation failed`);\r\n    return {\r\n      isValid: false,\r\n      errors,\r\n      warnings,\r\n      infos,\r\n    };\r\n  }\r\n\r\n  console.log(`[${timestamp}] [validateWorkflow] Found StartNode with ID: ${startNodeValidation.startNodeId}`);\r\n\r\n  // Step 4: Validate connectivity if requested\r\n  let connectedNodes: Set<string> | undefined;\r\n  if (options.validateConnectivity) {\r\n    console.log(`[${timestamp}] [validateWorkflow] Step 4: Validating node connectivity from StartNode`);\r\n    const connectivityValidation = validateConnectivity(\r\n      nodes,\r\n      edges,\r\n      startNodeValidation.startNodeId!\r\n    );\r\n    errors.push(...connectivityValidation.errors);\r\n    warnings.push(...connectivityValidation.warnings);\r\n    infos.push(...connectivityValidation.infos);\r\n    connectedNodes = connectivityValidation.connectedNodes;\r\n\r\n    if (connectedNodes) {\r\n      console.log(`[${timestamp}] [validateWorkflow] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);\r\n    } else {\r\n      console.warn(`[${timestamp}] [validateWorkflow] No connected nodes found from StartNode`);\r\n    }\r\n  } else {\r\n    console.log(`[${timestamp}] [validateWorkflow] Skipping connectivity validation (disabled in options)`);\r\n  }\r\n\r\n  // Step 5: Detect cycles if requested\r\n  if (options.validateCycles) {\r\n    console.log(`[${timestamp}] [validateWorkflow] Step 5: Detecting cycles in workflow`);\r\n    const cycleValidation = detectCyclesInWorkflow(nodes, edges);\r\n    errors.push(...cycleValidation.errors);\r\n    warnings.push(...cycleValidation.warnings);\r\n    infos.push(...cycleValidation.infos);\r\n\r\n    if (cycleValidation.warnings.length > 0) {\r\n      console.warn(`[${timestamp}] [validateWorkflow] Found cycles in workflow`);\r\n    } else {\r\n      console.log(`[${timestamp}] [validateWorkflow] No cycles detected in workflow`);\r\n    }\r\n  } else {\r\n    console.log(`[${timestamp}] [validateWorkflow] Skipping cycle detection (disabled in options)`);\r\n  }\r\n\r\n  // Step 6: Collect missing required fields if requested\r\n  let missingFields: MissingField[] = [];\r\n  if (options.collectMissingFields && connectedNodes) {\r\n    console.log(`[${timestamp}] [validateWorkflow] Step 6: Collecting missing required fields from ${connectedNodes.size} connected nodes`);\r\n    // Pass the edges to the collectMissingRequiredFields function to check if handle inputs are connected\r\n    missingFields = collectMissingRequiredFields(nodes, connectedNodes, edges);\r\n\r\n    if (missingFields.length > 0) {\r\n      console.log(`[${timestamp}] [validateWorkflow] Found ${missingFields.length} missing required fields`);\r\n      // Log details of missing fields for debugging\r\n      missingFields.forEach((field, index) => {\r\n        console.log(`[${timestamp}] [validateWorkflow] Missing field ${index + 1}: Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);\r\n      });\r\n    } else {\r\n      console.log(`[${timestamp}] [validateWorkflow] No missing required fields found`);\r\n    }\r\n  } else {\r\n    console.log(`[${timestamp}] [validateWorkflow] Skipping missing fields collection (disabled in options or no connected nodes)`);\r\n  }\r\n\r\n  // Final validation result\r\n  const isValid = errors.length === 0;\r\n  console.log(`[${timestamp}] [validateWorkflow] Validation complete. Result: ${isValid ? \"VALID\" : \"INVALID\"}`);\r\n  console.log(`[${timestamp}] [validateWorkflow] Errors: ${errors.length}, Warnings: ${warnings.length}, Missing Fields: ${missingFields.length}`);\r\n\r\n  return {\r\n    isValid,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n    missingFields,\r\n    startNodeId: startNodeValidation.startNodeId,\r\n    connectedNodes,\r\n  };\r\n}\r\n\r\n/**\r\n * Main export function for validating a workflow\r\n *\r\n * @param workflowData The workflow data to validate\r\n * @param options Validation options\r\n * @returns A validation result\r\n */\r\nexport default function validate(\r\n  workflowData: any,\r\n  options?: WorkflowValidationOptions\r\n): ValidationResult {\r\n  // First validate the basic structure\r\n  const structureValidation = validateWorkflowStructure(workflowData);\r\n\r\n  if (!structureValidation.isValid) {\r\n    return structureValidation;\r\n  }\r\n\r\n  // Extract nodes and edges\r\n  const nodes = workflowData.nodes || [];\r\n  const edges = workflowData.edges || [];\r\n\r\n  // Validate the workflow\r\n  return validateWorkflow(nodes, edges, options);\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQO,SAAS,0BAA0B,YAAiB;IACzD,MAAM,SAA4B,EAAE;IACpC,MAAM,WAA8B,EAAE;IACtC,MAAM,QAA2B,EAAE;IAEnC,sCAAsC;IACtC,IAAI,CAAC,gBAAgB,OAAO,iBAAiB,UAAU;QACrD,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,qBAAqB,EACzC;QAGJ,OAAO;YAAE,SAAS;YAAO;YAAQ;YAAU;QAAM;IACnD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,KAAK,GAAG;QAC7D,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C;IAGN;IAEA,8BAA8B;IAC9B,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,KAAK,GAAG;QAC7D,OAAO,IAAI,CACT,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C;IAGN;IAEA,iCAAiC;IACjC,IAAI,aAAa,aAAa,IAAI,OAAO,aAAa,aAAa,KAAK,UAAU;QAChF,SAAS,IAAI,CACX,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,qBAAqB,EACzC,kCACA;IAGN;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;IACF;AACF;AAUO,SAAS,iBACd,KAA+B,EAC/B,KAAa,EACb,UAAqC;IACnC,sBAAsB;IACtB,sBAAsB;IACtB,oBAAoB;IACpB,gBAAgB;AAClB,CAAC;IAED,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;IAC1E,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,uDAAuD,EAAE,OAAO,UAAU,EAAE,WAAW,EAAE,OAAO,UAAU,EAAE,MAAM,CAAC;IAE7I,gCAAgC;IAChC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,QAAQ;QACnC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,wDAAwD,CAAC;QACrF,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C;aAEH;YACD,UAAU,EAAE;YACZ,OAAO,EAAE;QACX;IACF;IAEA,oCAAoC;IACpC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,iCAAiC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC/E,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,IAAI,EAAE;YACrB,cAAc,KAAK,IAAI,EAAE;YACzB,OAAO,KAAK,IAAI,EAAE;QACpB,CAAC;IAED,gCAAgC;IAChC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,QAAQ;QACnC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,wDAAwD,CAAC;QACrF,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,sBAAsB,EAC1C;aAEH;YACD,UAAU,EAAE;YACZ,OAAO,EAAE;QACX;IACF;IAEA,6BAA6B;IAC7B,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,iDAAiD,CAAC;QAC7E,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClB,iIAAA,CAAA,sBAAmB,CAAC,cAAc,EAClC;aAEH;YACD,UAAU,EAAE;YACZ,OAAO,EAAE;QACX;IACF;IAEA,+BAA+B;IAC/B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,4CAA4C,CAAC,EACrE,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YACjB,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,IAAI,EAAE;YACrB,cAAc,KAAK,IAAI,EAAE;YACzB,gBAAgB,KAAK,IAAI,EAAE,YAAY;YACvC,OAAO,KAAK,IAAI,EAAE;QACpB,CAAC;IAGH,mCAAmC;IACnC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,sDAAsD,CAAC;IACjF,MAAM,iBAAiB,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD,EAAE;IAErC,mCAAmC;IACnC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,sDAAsD,CAAC;IACjF,MAAM,iBAAiB,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;IAE5C,6BAA6B;IAC7B,MAAM,SAAS;WAAI,eAAe,MAAM;WAAK,eAAe,MAAM;KAAC;IACnE,MAAM,WAAW;WAAI,eAAe,QAAQ;WAAK,eAAe,QAAQ;KAAC;IACzE,MAAM,QAAQ;WAAI,eAAe,KAAK;WAAK,eAAe,KAAK;KAAC;IAEhE,+CAA+C;IAC/C,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,2BAA2B,EAAE,OAAO,MAAM,CAAC,mCAAmC,CAAC;QAC1G,OAAO;YACL,SAAS;YACT;YACA;YACA;QACF;IACF;IAEA,sCAAsC;IACtC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,0DAA0D,CAAC;IACrF,MAAM,sBAAsB,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE;IAC9C,OAAO,IAAI,IAAI,oBAAoB,MAAM;IACzC,SAAS,IAAI,IAAI,oBAAoB,QAAQ;IAC7C,MAAM,IAAI,IAAI,oBAAoB,KAAK;IAEvC,wCAAwC;IACxC,IAAI,CAAC,oBAAoB,OAAO,EAAE;QAChC,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,0DAA0D,CAAC;QACtF,OAAO;YACL,SAAS;YACT;YACA;YACA;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,8CAA8C,EAAE,oBAAoB,WAAW,EAAE;IAE3G,6CAA6C;IAC7C,IAAI;IACJ,IAAI,QAAQ,oBAAoB,EAAE;QAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,wEAAwE,CAAC;QACnG,MAAM,yBAAyB,CAAA,GAAA,kJAAA,CAAA,uBAAoB,AAAD,EAChD,OACA,OACA,oBAAoB,WAAW;QAEjC,OAAO,IAAI,IAAI,uBAAuB,MAAM;QAC5C,SAAS,IAAI,IAAI,uBAAuB,QAAQ;QAChD,MAAM,IAAI,IAAI,uBAAuB,KAAK;QAC1C,iBAAiB,uBAAuB,cAAc;QAEtD,IAAI,gBAAgB;YAClB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,2BAA2B,EAAE,eAAe,IAAI,CAAC,+BAA+B,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;QACrJ,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,4DAA4D,CAAC;QAC1F;IACF,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,2EAA2E,CAAC;IACxG;IAEA,qCAAqC;IACrC,IAAI,QAAQ,cAAc,EAAE;QAC1B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,yDAAyD,CAAC;QACpF,MAAM,kBAAkB,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO;QACtD,OAAO,IAAI,IAAI,gBAAgB,MAAM;QACrC,SAAS,IAAI,IAAI,gBAAgB,QAAQ;QACzC,MAAM,IAAI,IAAI,gBAAgB,KAAK;QAEnC,IAAI,gBAAgB,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,6CAA6C,CAAC;QAC3E,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,mDAAmD,CAAC;QAChF;IACF,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,mEAAmE,CAAC;IAChG;IAEA,uDAAuD;IACvD,IAAI,gBAAgC,EAAE;IACtC,IAAI,QAAQ,oBAAoB,IAAI,gBAAgB;QAClD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qEAAqE,EAAE,eAAe,IAAI,CAAC,gBAAgB,CAAC;QACtI,sGAAsG;QACtG,gBAAgB,CAAA,GAAA,2IAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO,gBAAgB;QAEpE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,2BAA2B,EAAE,cAAc,MAAM,CAAC,wBAAwB,CAAC;YACrG,8CAA8C;YAC9C,cAAc,OAAO,CAAC,CAAC,OAAO;gBAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,mCAAmC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC,UAAU,EAAE,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,MAAM,SAAS,EAAE;YAC9L;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qDAAqD,CAAC;QAClF;IACF,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,mGAAmG,CAAC;IAChI;IAEA,0BAA0B;IAC1B,MAAM,UAAU,OAAO,MAAM,KAAK;IAClC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,kDAAkD,EAAE,UAAU,UAAU,WAAW;IAC7G,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,6BAA6B,EAAE,OAAO,MAAM,CAAC,YAAY,EAAE,SAAS,MAAM,CAAC,kBAAkB,EAAE,cAAc,MAAM,EAAE;IAE/I,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,aAAa,oBAAoB,WAAW;QAC5C;IACF;AACF;AASe,SAAS,SACtB,YAAiB,EACjB,OAAmC;IAEnC,qCAAqC;IACrC,MAAM,sBAAsB,0BAA0B;IAEtD,IAAI,CAAC,oBAAoB,OAAO,EAAE;QAChC,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,QAAQ,aAAa,KAAK,IAAI,EAAE;IACtC,MAAM,QAAQ,aAAa,KAAK,IAAI,EAAE;IAEtC,wBAAwB;IACxB,OAAO,iBAAiB,OAAO,OAAO;AACxC", "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/smartValidation.ts"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { validateWorkflow } from \"./workflowValidation\";\r\nimport { ValidationResult, WorkflowValidationOptions, MissingField } from \"./types\";\r\nimport { FEATURES } from \"@/config/features\";\r\n\r\n/**\r\n * Converts frontend validation result to backend-compatible format\r\n * This is used for backward compatibility with code that expects the backend format\r\n */\r\nexport function convertToBackendFormat(result: ValidationResult): {\r\n  is_valid: boolean;\r\n  missing_fields?: {\r\n    node_id: string;\r\n    node_name: string;\r\n    name: string;\r\n    display_name: string;\r\n    info?: string;\r\n    input_type: string;\r\n  }[];\r\n  error?: string;\r\n} {\r\n  // Convert missing fields to backend format\r\n  const missingFields = result.missingFields?.map((field) => ({\r\n    node_id: field.nodeId,\r\n    node_name: field.nodeName,\r\n    name: field.name,\r\n    display_name: field.displayName,\r\n    info: field.info,\r\n    input_type: field.inputType,\r\n  }));\r\n\r\n  // If there are errors, combine them into a single error message\r\n  let error: string | undefined;\r\n  if (result.errors.length > 0) {\r\n    error = result.errors.map((err) => err.message).join(\"; \");\r\n  }\r\n\r\n  return {\r\n    is_valid: result.isValid,\r\n    missing_fields: missingFields,\r\n    error,\r\n  };\r\n}\r\n\r\n/**\r\n * Smart validation function that uses the appropriate validation method\r\n * based on feature flags\r\n * \r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @param options Validation options\r\n * @returns A validation result\r\n */\r\nexport async function validateWorkflowSmart(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  options?: WorkflowValidationOptions\r\n): Promise<ValidationResult> {\r\n  // Always use frontend validation since backend validation is disabled\r\n  return validateWorkflow(nodes, edges, options);\r\n}\r\n\r\n/**\r\n * Validates a workflow before saving\r\n * \r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @returns A validation result\r\n */\r\nexport async function validateWorkflowBeforeSave(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[]\r\n): Promise<ValidationResult> {\r\n  if (!FEATURES.VALIDATE_ON_SAVE) {\r\n    // If validation on save is disabled, return a valid result\r\n    return {\r\n      isValid: true,\r\n      errors: [],\r\n      warnings: [],\r\n      infos: [],\r\n    };\r\n  }\r\n\r\n  const options: WorkflowValidationOptions = {\r\n    validateConnectivity: true,\r\n    collectMissingFields: false, // Don't collect missing fields for save\r\n    validateFieldTypes: true,\r\n    validateCycles: true,\r\n  };\r\n\r\n  return validateWorkflowSmart(nodes, edges, options);\r\n}\r\n\r\n/**\r\n * Validates a workflow before execution\r\n * \r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @returns A validation result with missing fields\r\n */\r\nexport async function validateWorkflowBeforeExecution(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[]\r\n): Promise<ValidationResult> {\r\n  if (!FEATURES.VALIDATE_ON_EXECUTE) {\r\n    // If validation on execute is disabled, return a valid result\r\n    return {\r\n      isValid: true,\r\n      errors: [],\r\n      warnings: [],\r\n      infos: [],\r\n    };\r\n  }\r\n\r\n  const options: WorkflowValidationOptions = {\r\n    validateConnectivity: true,\r\n    collectMissingFields: true, // Collect missing fields for execution\r\n    validateFieldTypes: true,\r\n    validateCycles: true,\r\n  };\r\n\r\n  return validateWorkflowSmart(nodes, edges, options);\r\n}\r\n\r\n/**\r\n * Validates a workflow during editing\r\n * \r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @returns A validation result\r\n */\r\nexport async function validateWorkflowDuringEditing(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[]\r\n): Promise<ValidationResult> {\r\n  if (!FEATURES.VALIDATE_ON_EDIT) {\r\n    // If validation during editing is disabled, return a valid result\r\n    return {\r\n      isValid: true,\r\n      errors: [],\r\n      warnings: [],\r\n      infos: [],\r\n    };\r\n  }\r\n\r\n  const options: WorkflowValidationOptions = {\r\n    validateConnectivity: false, // Skip connectivity validation during editing\r\n    collectMissingFields: false, // Don't collect missing fields during editing\r\n    validateFieldTypes: true,\r\n    validateCycles: false, // Skip cycle detection during editing\r\n  };\r\n\r\n  return validateWorkflowSmart(nodes, edges, options);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;;;AAMO,SAAS,uBAAuB,MAAwB;IAY7D,2CAA2C;IAC3C,MAAM,gBAAgB,OAAO,aAAa,EAAE,IAAI,CAAC,QAAU,CAAC;YAC1D,SAAS,MAAM,MAAM;YACrB,WAAW,MAAM,QAAQ;YACzB,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,WAAW;YAC/B,MAAM,MAAM,IAAI;YAChB,YAAY,MAAM,SAAS;QAC7B,CAAC;IAED,gEAAgE;IAChE,IAAI;IACJ,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;QAC5B,QAAQ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO,EAAE,IAAI,CAAC;IACvD;IAEA,OAAO;QACL,UAAU,OAAO,OAAO;QACxB,gBAAgB;QAChB;IACF;AACF;AAWO,eAAe,sBACpB,KAA+B,EAC/B,KAAa,EACb,OAAmC;IAEnC,sEAAsE;IACtE,OAAO,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,OAAO;AACxC;AASO,eAAe,2BACpB,KAA+B,EAC/B,KAAa;IAEb,IAAI,CAAC,yHAAA,CAAA,WAAQ,CAAC,gBAAgB,EAAE;QAC9B,2DAA2D;QAC3D,OAAO;YACL,SAAS;YACT,QAAQ,EAAE;YACV,UAAU,EAAE;YACZ,OAAO,EAAE;QACX;IACF;IAEA,MAAM,UAAqC;QACzC,sBAAsB;QACtB,sBAAsB;QACtB,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,OAAO,sBAAsB,OAAO,OAAO;AAC7C;AASO,eAAe,gCACpB,KAA+B,EAC/B,KAAa;IAEb,IAAI,CAAC,yHAAA,CAAA,WAAQ,CAAC,mBAAmB,EAAE;QACjC,8DAA8D;QAC9D,OAAO;YACL,SAAS;YACT,QAAQ,EAAE;YACV,UAAU,EAAE;YACZ,OAAO,EAAE;QACX;IACF;IAEA,MAAM,UAAqC;QACzC,sBAAsB;QACtB,sBAAsB;QACtB,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,OAAO,sBAAsB,OAAO,OAAO;AAC7C;AASO,eAAe,8BACpB,KAA+B,EAC/B,KAAa;IAEb,IAAI,CAAC,yHAAA,CAAA,WAAQ,CAAC,gBAAgB,EAAE;QAC9B,kEAAkE;QAClE,OAAO;YACL,SAAS;YACT,QAAQ,EAAE;YACV,UAAU,EAAE;YACZ,OAAO,EAAE;QACX;IACF;IAEA,MAAM,UAAqC;QACzC,sBAAsB;QACtB,sBAAsB;QACtB,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,OAAO,sBAAsB,OAAO,OAAO;AAC7C", "debugId": null}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/frontendValidationAdapter.ts"], "sourcesContent": ["import { Node, <PERSON> } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { validateWorkflowBeforeExecution } from \"./smartValidation\";\r\nimport { convertToBackendFormat } from \"./smartValidation\";\r\nimport { ValidationResponse } from \"@/lib/workflow-api\";\r\n\r\n/**\r\n * Frontend implementation of the backend validation API\r\n * This function replaces the backend /validate_workflow endpoint\r\n * \r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @returns A ValidationResponse object in the same format as the backend API\r\n */\r\nexport async function validateWorkflowFrontend(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[]\r\n): Promise<ValidationResponse> {\r\n  try {\r\n    // Use our frontend validation implementation\r\n    const validationResult = await validateWorkflowBeforeExecution(nodes, edges);\r\n    \r\n    // Convert to backend format\r\n    const backendFormat = convertToBackendFormat(validationResult);\r\n    \r\n    return {\r\n      is_valid: backendFormat.is_valid,\r\n      missing_fields: backendFormat.missing_fields,\r\n      error: backendFormat.error\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in frontend validation:\", error);\r\n    return {\r\n      is_valid: false,\r\n      error: error instanceof Error ? error.message : String(error)\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;;AAYO,eAAe,yBACpB,KAA+B,EAC/B,KAAa;IAEb,IAAI;QACF,6CAA6C;QAC7C,MAAM,mBAAmB,MAAM,CAAA,GAAA,2IAAA,CAAA,kCAA+B,AAAD,EAAE,OAAO;QAEtE,4BAA4B;QAC5B,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD,EAAE;QAE7C,OAAO;YACL,UAAU,cAAc,QAAQ;YAChC,gBAAgB,cAAc,cAAc;YAC5C,OAAO,cAAc,KAAK;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,UAAU;YACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF", "debugId": null}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/workflow-api.ts"], "sourcesContent": ["// src/lib/workflow-api.ts\r\nimport { Node, Edge } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { validateWorkflowFrontend } from \"@/lib/validation/frontendValidationAdapter\";\r\n\r\n// API base URL\r\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;\r\n\r\n// Interface for missing field\r\nexport interface MissingField {\r\n  nodeId: string;\r\n  nodeName: string;\r\n  name: string;\r\n  displayName: string;\r\n  info?: string;\r\n  input_type: string;\r\n}\r\n\r\n// Interface for validation response\r\nexport interface ValidationResponse {\r\n  is_valid: boolean;\r\n  missing_fields?: MissingField[];\r\n  error?: string;\r\n}\r\n\r\n// Interface for execution response\r\nexport interface ExecutionResponse {\r\n  success: boolean;\r\n  results?: any;\r\n  error?: string;\r\n  correlation_id?: string;\r\n  save_filepath?: string;\r\n  message?: string;\r\n}\r\n\r\n/**\r\n * Validate a workflow before execution\r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @returns Validation result with missing fields if any\r\n */\r\nexport async function validateWorkflow(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n): Promise<ValidationResponse> {\r\n  // Use frontend validation instead of backend API\r\n  return validateWorkflowFrontend(nodes, edges);\r\n}\r\n\r\n/**\r\n * Execute a workflow with provided field values\r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @param fieldValues Values for missing fields, grouped by node ID\r\n * @returns Execution result\r\n */\r\nexport async function executeWorkflowWithValues(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  fieldValues?: Record<string, any>,\r\n  workflow_id?: string,\r\n): Promise<ExecutionResponse> {\r\n  try {\r\n    // Find the StartNode first\r\n    const startNode = nodes.find((node) => node.data.originalType === \"StartNode\");\r\n\r\n    // Ensure the StartNode has a config object with collected_parameters\r\n    if (startNode) {\r\n      console.log(\"Found StartNode before updating:\", startNode);\r\n\r\n      // Ensure the StartNode has a config object\r\n      if (!startNode.data.config) {\r\n        startNode.data.config = {};\r\n      }\r\n\r\n      // Ensure the config has a collected_parameters object\r\n      if (!startNode.data.config.collected_parameters) {\r\n        startNode.data.config.collected_parameters = {};\r\n      }\r\n\r\n      // Ensure all parameters in collected_parameters have the required property set\r\n      // This is critical for pre-built workflows where the required property might not be set\r\n      if (startNode.data.config.collected_parameters) {\r\n        Object.keys(startNode.data.config.collected_parameters).forEach(paramId => {\r\n          const param = startNode.data.config.collected_parameters[paramId];\r\n          // If required is undefined, set it to true (consider required unless explicitly false)\r\n          if (param.required === undefined) {\r\n            console.log(`Setting required=true for parameter ${paramId} in StartNode`);\r\n            param.required = true;\r\n          }\r\n        });\r\n      }\r\n\r\n      console.log(\"StartNode config after ensuring structure:\", startNode.data.config);\r\n    } else {\r\n      console.warn(\"No StartNode found in the workflow, this may cause issues\");\r\n    }\r\n\r\n    // If fieldValues is provided, update the node configs\r\n    if (fieldValues) {\r\n      // Process field values to update individual nodes\r\n      const nodeFieldValues: Record<string, Record<string, any>> = {};\r\n\r\n      // Group field values by node ID\r\n      Object.entries(fieldValues).forEach(([fieldId, value]) => {\r\n        // Extract node ID and field name from the field ID\r\n        const [nodeId, ...fieldNameParts] = fieldId.split(\"_\");\r\n        const fieldName = fieldNameParts.join(\"_\");\r\n\r\n        // Initialize node entry if it doesn't exist\r\n        if (!nodeFieldValues[nodeId]) {\r\n          nodeFieldValues[nodeId] = {};\r\n        }\r\n\r\n        // Add field value to the node\r\n        nodeFieldValues[nodeId][fieldName] = value;\r\n      });\r\n\r\n      // Update nodes with their respective field values\r\n      nodes = nodes.map((node) => {\r\n        if (nodeFieldValues[node.id]) {\r\n          console.log(`Updating node ${node.id} config with values:`, nodeFieldValues[node.id]);\r\n\r\n          // Create a proper deep copy without JSON.stringify to avoid string escaping issues\r\n          const updatedNode = {\r\n            ...node,\r\n            data: {\r\n              ...node.data,\r\n              config: {\r\n                ...node.data.config,\r\n              }\r\n            }\r\n          };\r\n\r\n          // Ensure config exists\r\n          if (!updatedNode.data.config) {\r\n            updatedNode.data.config = {};\r\n          }\r\n\r\n          // Update the config with the new values\r\n          Object.entries(nodeFieldValues[node.id]).forEach(([key, value]) => {\r\n            updatedNode.data.config[key] = value;\r\n          });\r\n\r\n          console.log(`Node ${node.id} config after update:`, updatedNode.data.config);\r\n          return updatedNode;\r\n        }\r\n        return node;\r\n      });\r\n\r\n      // Update the StartNode with collected parameters\r\n      if (startNode) {\r\n        // Create a deep copy of the collected parameters or initialize if not exists\r\n        const collectedParameters = { ...(startNode.data.config?.collected_parameters || {}) };\r\n\r\n        // Process each field value directly\r\n        Object.entries(fieldValues).forEach(([fieldId, fieldValue]) => {\r\n          // Skip if this is not a valid field ID format\r\n          if (!fieldId.includes(\"_\")) return;\r\n\r\n          // Extract node ID and field name\r\n          const [nodeId, ...fieldNameParts] = fieldId.split(\"_\");\r\n          const fieldName = fieldNameParts.join(\"_\");\r\n\r\n          // Get the node for additional metadata\r\n          const node = nodes.find((n) => n.id === nodeId);\r\n          if (!node) return;\r\n\r\n          // Process value if it's a JSON string\r\n          let processedValue = fieldValue;\r\n          if (typeof fieldValue === \"string\") {\r\n            // Check if the value looks like JSON\r\n            if (fieldValue.trim().startsWith(\"{\") || fieldValue.trim().startsWith(\"[\")) {\r\n              try {\r\n                // Parse the JSON string to an object or array\r\n                processedValue = JSON.parse(fieldValue);\r\n                console.log(`Successfully parsed JSON for ${fieldName}:`, processedValue);\r\n              } catch (e) {\r\n                console.error(`Failed to parse JSON for ${fieldName} field:`, e);\r\n              }\r\n            }\r\n          }\r\n\r\n          // Check if this parameter is connected to the StartNode\r\n          const isConnectedToStartNode = edges.some((edge) => {\r\n            // Check if this edge connects the StartNode to this node\r\n            const isFromStartNode = edge.source === startNode.id;\r\n            const isToThisNode = edge.target === nodeId;\r\n\r\n            // Check if the target handle matches this field\r\n            let matchesField = false;\r\n            if (edge.targetHandle) {\r\n              // Handle different formats of target handles\r\n              if (edge.targetHandle === fieldName) {\r\n                matchesField = true;\r\n              } else if (edge.targetHandle === `input_${fieldName}`) {\r\n                matchesField = true;\r\n              } else if (edge.targetHandle.includes(fieldName)) {\r\n                matchesField = true;\r\n              }\r\n            }\r\n\r\n            return isFromStartNode && isToThisNode && matchesField;\r\n          });\r\n\r\n          // Store the parameter with full metadata\r\n          collectedParameters[fieldId] = {\r\n            node_id: nodeId,\r\n            node_name: node.data.label || \"Unknown Node\",\r\n            input_name: fieldName,\r\n            value: processedValue,\r\n            connected_to_start: isConnectedToStartNode,\r\n          };\r\n        });\r\n\r\n        // Update the StartNode config\r\n        startNode.data.config = {\r\n          ...startNode.data.config,\r\n          collected_parameters: collectedParameters,\r\n        };\r\n\r\n        console.log(\"StartNode config after updating collected parameters:\", startNode.data.config);\r\n\r\n        // Also update the global window.startNodeCollectedParameters for future reference\r\n        if (typeof window !== \"undefined\") {\r\n          // Use type assertion to avoid TypeScript errors\r\n          (window as any).startNodeCollectedParameters = startNode.data.config.collected_parameters;\r\n          console.log(\r\n            \"Updated global startNodeCollectedParameters:\",\r\n            (window as any).startNodeCollectedParameters,\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    // Filter out nodes that are not connected to the StartNode\r\n    let filteredNodes = nodes;\r\n    let filteredEdges = edges;\r\n\r\n    if (startNode) {\r\n      console.log(\"[WORKFLOW EXECUTE] Filtering out unconnected nodes before execution\");\r\n\r\n      // Import the getConnectedNodes function from validation utils\r\n      const { getConnectedNodes } = await import(\"@/lib/validation/utils\");\r\n\r\n      // Find all nodes connected to the StartNode\r\n      const connectedNodes = getConnectedNodes(nodes, edges, startNode.id);\r\n      console.log(`[WORKFLOW EXECUTE] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);\r\n\r\n      // Check if there are any disconnected nodes\r\n      const disconnectedNodesCount = nodes.length - connectedNodes.size;\r\n      if (disconnectedNodesCount > 0) {\r\n        console.log(`[WORKFLOW EXECUTE] Found ${disconnectedNodesCount} disconnected nodes that will be excluded from execution`);\r\n\r\n        // Filter nodes to include only those connected to the StartNode\r\n        filteredNodes = nodes.filter(node => connectedNodes.has(node.id));\r\n\r\n        // Filter edges to include only those connecting filtered nodes\r\n        filteredEdges = edges.filter(edge =>\r\n          connectedNodes.has(edge.source) && connectedNodes.has(edge.target)\r\n        );\r\n\r\n        console.log(`[WORKFLOW EXECUTE] Filtered workflow contains ${filteredNodes.length} nodes and ${filteredEdges.length} edges`);\r\n\r\n        // Import toast dynamically to avoid server-side rendering issues\r\n        try {\r\n          // Use dynamic import for toast\r\n          const { toast } = await import(\"sonner\");\r\n\r\n          // Show a warning toast notification\r\n          toast.warning(\r\n            `${disconnectedNodesCount} unconnected ${disconnectedNodesCount === 1 ? 'node has' : 'nodes have'} been excluded from execution.`,\r\n            {\r\n              description: \"Only nodes connected to the Start node are included in the workflow execution.\",\r\n              duration: 5000,\r\n            }\r\n          );\r\n        } catch (error) {\r\n          console.error(\"Failed to show toast notification:\", error);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Log the node configs to help with debugging\r\n    console.log(\"Executing workflow with filtered nodes:\", filteredNodes);\r\n    console.log(\"StartNode config before execution:\", startNode?.data.config);\r\n\r\n    // Log specific node configs that might have updated values\r\n    filteredNodes.forEach((node) => {\r\n      if (node.data.originalType === \"ScriptGenerateNode\" || node.id.includes(\"generate-script\")) {\r\n        console.log(`Script Generate Node ${node.id} config:`, node.data.config);\r\n      }\r\n    });\r\n\r\n    const response = await fetch(`${API_BASE_URL}/execute`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ nodes: filteredNodes, edges: filteredEdges, workflow_id }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Error executing workflow: ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error(\"Error executing workflow:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;;;AAG1B;;AAGO,MAAM;AAmCN,eAAe,iBACpB,KAA+B,EAC/B,KAAa;IAEb,iDAAiD;IACjD,OAAO,CAAA,GAAA,qJAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO;AACzC;AASO,eAAe,0BACpB,KAA+B,EAC/B,KAAa,EACb,WAAiC,EACjC,WAAoB;IAEpB,IAAI;QACF,2BAA2B;QAC3B,MAAM,YAAY,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,CAAC,YAAY,KAAK;QAElE,qEAAqE;QACrE,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,2CAA2C;YAC3C,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE;gBAC1B,UAAU,IAAI,CAAC,MAAM,GAAG,CAAC;YAC3B;YAEA,sDAAsD;YACtD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBAC/C,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC;YAChD;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBAC9C,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAA;oBAC9D,MAAM,QAAQ,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ;oBACjE,uFAAuF;oBACvF,IAAI,MAAM,QAAQ,KAAK,WAAW;wBAChC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,aAAa,CAAC;wBACzE,MAAM,QAAQ,GAAG;oBACnB;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,8CAA8C,UAAU,IAAI,CAAC,MAAM;QACjF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;QAEA,sDAAsD;QACtD,IAAI,aAAa;YACf,kDAAkD;YAClD,MAAM,kBAAuD,CAAC;YAE9D,gCAAgC;YAChC,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,SAAS,MAAM;gBACnD,mDAAmD;gBACnD,MAAM,CAAC,QAAQ,GAAG,eAAe,GAAG,QAAQ,KAAK,CAAC;gBAClD,MAAM,YAAY,eAAe,IAAI,CAAC;gBAEtC,4CAA4C;gBAC5C,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;oBAC5B,eAAe,CAAC,OAAO,GAAG,CAAC;gBAC7B;gBAEA,8BAA8B;gBAC9B,eAAe,CAAC,OAAO,CAAC,UAAU,GAAG;YACvC;YAEA,kDAAkD;YAClD,QAAQ,MAAM,GAAG,CAAC,CAAC;gBACjB,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC,EAAE;oBAC5B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,oBAAoB,CAAC,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC;oBAEpF,mFAAmF;oBACnF,MAAM,cAAc;wBAClB,GAAG,IAAI;wBACP,MAAM;4BACJ,GAAG,KAAK,IAAI;4BACZ,QAAQ;gCACN,GAAG,KAAK,IAAI,CAAC,MAAM;4BACrB;wBACF;oBACF;oBAEA,uBAAuB;oBACvB,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE;wBAC5B,YAAY,IAAI,CAAC,MAAM,GAAG,CAAC;oBAC7B;oBAEA,wCAAwC;oBACxC,OAAO,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC5D,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;oBACjC;oBAEA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,qBAAqB,CAAC,EAAE,YAAY,IAAI,CAAC,MAAM;oBAC3E,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,WAAW;gBACb,6EAA6E;gBAC7E,MAAM,sBAAsB;oBAAE,GAAI,UAAU,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;gBAAE;gBAErF,oCAAoC;gBACpC,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,SAAS,WAAW;oBACxD,8CAA8C;oBAC9C,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM;oBAE5B,iCAAiC;oBACjC,MAAM,CAAC,QAAQ,GAAG,eAAe,GAAG,QAAQ,KAAK,CAAC;oBAClD,MAAM,YAAY,eAAe,IAAI,CAAC;oBAEtC,uCAAuC;oBACvC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBACxC,IAAI,CAAC,MAAM;oBAEX,sCAAsC;oBACtC,IAAI,iBAAiB;oBACrB,IAAI,OAAO,eAAe,UAAU;wBAClC,qCAAqC;wBACrC,IAAI,WAAW,IAAI,GAAG,UAAU,CAAC,QAAQ,WAAW,IAAI,GAAG,UAAU,CAAC,MAAM;4BAC1E,IAAI;gCACF,8CAA8C;gCAC9C,iBAAiB,KAAK,KAAK,CAAC;gCAC5B,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,UAAU,CAAC,CAAC,EAAE;4BAC5D,EAAE,OAAO,GAAG;gCACV,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,UAAU,OAAO,CAAC,EAAE;4BAChE;wBACF;oBACF;oBAEA,wDAAwD;oBACxD,MAAM,yBAAyB,MAAM,IAAI,CAAC,CAAC;wBACzC,yDAAyD;wBACzD,MAAM,kBAAkB,KAAK,MAAM,KAAK,UAAU,EAAE;wBACpD,MAAM,eAAe,KAAK,MAAM,KAAK;wBAErC,gDAAgD;wBAChD,IAAI,eAAe;wBACnB,IAAI,KAAK,YAAY,EAAE;4BACrB,6CAA6C;4BAC7C,IAAI,KAAK,YAAY,KAAK,WAAW;gCACnC,eAAe;4BACjB,OAAO,IAAI,KAAK,YAAY,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE;gCACrD,eAAe;4BACjB,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ,CAAC,YAAY;gCAChD,eAAe;4BACjB;wBACF;wBAEA,OAAO,mBAAmB,gBAAgB;oBAC5C;oBAEA,yCAAyC;oBACzC,mBAAmB,CAAC,QAAQ,GAAG;wBAC7B,SAAS;wBACT,WAAW,KAAK,IAAI,CAAC,KAAK,IAAI;wBAC9B,YAAY;wBACZ,OAAO;wBACP,oBAAoB;oBACtB;gBACF;gBAEA,8BAA8B;gBAC9B,UAAU,IAAI,CAAC,MAAM,GAAG;oBACtB,GAAG,UAAU,IAAI,CAAC,MAAM;oBACxB,sBAAsB;gBACxB;gBAEA,QAAQ,GAAG,CAAC,yDAAyD,UAAU,IAAI,CAAC,MAAM;gBAE1F,kFAAkF;gBAClF,uCAAmC;;gBAOnC;YACF;QACF;QAEA,2DAA2D;QAC3D,IAAI,gBAAgB;QACpB,IAAI,gBAAgB;QAEpB,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YAEZ,8DAA8D;YAC9D,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAE9B,4CAA4C;YAC5C,MAAM,iBAAiB,kBAAkB,OAAO,OAAO,UAAU,EAAE;YACnE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,eAAe,IAAI,CAAC,+BAA+B,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;YAEpI,4CAA4C;YAC5C,MAAM,yBAAyB,MAAM,MAAM,GAAG,eAAe,IAAI;YACjE,IAAI,yBAAyB,GAAG;gBAC9B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,uBAAuB,wDAAwD,CAAC;gBAExH,gEAAgE;gBAChE,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,eAAe,GAAG,CAAC,KAAK,EAAE;gBAE/D,+DAA+D;gBAC/D,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAC3B,eAAe,GAAG,CAAC,KAAK,MAAM,KAAK,eAAe,GAAG,CAAC,KAAK,MAAM;gBAGnE,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,cAAc,MAAM,CAAC,WAAW,EAAE,cAAc,MAAM,CAAC,MAAM,CAAC;gBAE3H,iEAAiE;gBACjE,IAAI;oBACF,+BAA+B;oBAC/B,MAAM,EAAE,KAAK,EAAE,GAAG;oBAElB,oCAAoC;oBACpC,MAAM,OAAO,CACX,GAAG,uBAAuB,aAAa,EAAE,2BAA2B,IAAI,aAAa,aAAa,8BAA8B,CAAC,EACjI;wBACE,aAAa;wBACb,UAAU;oBACZ;gBAEJ,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;QACF;QAEA,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,2CAA2C;QACvD,QAAQ,GAAG,CAAC,sCAAsC,WAAW,KAAK;QAElE,2DAA2D;QAC3D,cAAc,OAAO,CAAC,CAAC;YACrB,IAAI,KAAK,IAAI,CAAC,YAAY,KAAK,wBAAwB,KAAK,EAAE,CAAC,QAAQ,CAAC,oBAAoB;gBAC1F,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM;YACzE;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,OAAO;gBAAe,OAAO;gBAAe;YAAY;QACjF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF", "debugId": null}}, {"offset": {"line": 2093, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/validation/index.ts"], "sourcesContent": ["// Export all validation types and functions\r\nexport * from \"./types\";\r\nexport * from \"./errors\";\r\nexport * from \"./utils\";\r\nexport * from \"./nodeValidation\";\r\nexport * from \"./edgeValidation\";\r\nexport * from \"./connectivityValidation\";\r\nexport * from \"./fieldValidation\";\r\nexport * from \"./workflowValidation\";\r\nexport * from \"./smartValidation\";\r\nexport * from \"./frontendValidationAdapter\";\r\n\r\n// Export the default validate function\r\nexport { default } from \"./workflowValidation\";\r\n"], "names": [], "mappings": "AAAA,4CAA4C", "debugId": null}}, {"offset": {"line": 2130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/approvalUtils.ts"], "sourcesContent": ["/**\r\n * Utility functions for handling workflow approval events\r\n * This centralizes the approval event dispatching and tracking to prevent duplicate events\r\n */\r\n\r\n// Add global type definitions for our debugging variables\r\ndeclare global {\r\n  interface Window {\r\n    _lastApprovalTimestamp?: number;\r\n    _pendingApproval?: {\r\n      correlationId: string;\r\n      nodeId: string;\r\n      nodeName: string;\r\n      timestamp: number;\r\n    };\r\n    _approvalEventHistory?: Array<{\r\n      correlationId: string;\r\n      nodeId: string;\r\n      nodeName: string;\r\n      timestamp: number;\r\n      status: string;\r\n    }>;\r\n  }\r\n}\r\n\r\n// Initialize the approval event history array\r\nif (typeof window !== 'undefined') {\r\n  window._approvalEventHistory = window._approvalEventHistory || [];\r\n}\r\n\r\n// Track already dispatched approval events to prevent duplicates\r\nconst dispatchedApprovalEvents = new Set<string>();\r\n\r\n/**\r\n * Dispatches a workflow approval needed event, with smarter deduplication\r\n *\r\n * @param correlationId The correlation ID of the workflow execution\r\n * @param nodeId The ID of the node requiring approval\r\n * @param nodeName The name of the node requiring approval\r\n */\r\nexport function dispatchApprovalNeededEvent(correlationId: string, nodeId: string, nodeName: string): void {\r\n  // Skip if any parameter is missing or invalid\r\n  if (!correlationId || !nodeId || nodeId === \"unknown\") {\r\n    console.log(`Skipping approval event due to invalid parameters: correlationId=${correlationId}, nodeId=${nodeId}`);\r\n    return;\r\n  }\r\n\r\n  // Create a unique key for this approval request\r\n  const approvalKey = `${correlationId}_${nodeId}`;\r\n\r\n  // Get the current timestamp\r\n  const currentTimestamp = Date.now();\r\n\r\n  // Add to approval history for debugging\r\n  if (window._approvalEventHistory) {\r\n    window._approvalEventHistory.push({\r\n      correlationId,\r\n      nodeId,\r\n      nodeName: nodeName || nodeId,\r\n      timestamp: currentTimestamp,\r\n      status: 'requested'\r\n    });\r\n\r\n    // Keep only the last 10 events\r\n    if (window._approvalEventHistory.length > 10) {\r\n      window._approvalEventHistory = window._approvalEventHistory.slice(-10);\r\n    }\r\n  }\r\n\r\n  // Check if we've already dispatched this approval\r\n  // Only dispatch if:\r\n  // 1. We haven't seen this exact approval key before, OR\r\n  // 2. It's been more than 5 seconds since the last dispatch (to handle UI issues)\r\n  const shouldDispatch = !dispatchedApprovalEvents.has(approvalKey) ||\r\n                         (currentTimestamp - (window._lastApprovalTimestamp || 0) > 5000);\r\n\r\n  if (shouldDispatch) {\r\n    console.log(`Dispatching approval needed event for node ${nodeName || nodeId} (${nodeId})`);\r\n\r\n    // Add to tracking set\r\n    dispatchedApprovalEvents.add(approvalKey);\r\n\r\n    // Store the last approval timestamp in a global variable for debugging\r\n    window._lastApprovalTimestamp = currentTimestamp;\r\n\r\n    // Dispatch the event with additional metadata\r\n    const approvalEvent = new CustomEvent(\"workflow-approval-needed\", {\r\n      detail: {\r\n        correlationId,\r\n        nodeId,\r\n        nodeName: nodeName || nodeId,\r\n        timestamp: currentTimestamp,\r\n        approvalKey\r\n      }\r\n    });\r\n\r\n    window.dispatchEvent(approvalEvent);\r\n\r\n    // Also set a flag on the window object that can be checked for debugging\r\n    window._pendingApproval = {\r\n      correlationId,\r\n      nodeId,\r\n      nodeName: nodeName || nodeId,\r\n      timestamp: currentTimestamp\r\n    };\r\n\r\n    // Force a UI update\r\n    setTimeout(() => {\r\n      window.dispatchEvent(new CustomEvent('approval-ui-update'));\r\n    }, 200);\r\n  } else {\r\n    console.log(`Skipping duplicate approval event for node ${nodeName || nodeId} (${nodeId})`);\r\n  }\r\n}\r\n\r\n/**\r\n * Clears a specific approval event from the tracking system\r\n *\r\n * @param correlationId The correlation ID of the workflow execution\r\n * @param nodeId The ID of the node requiring approval\r\n */\r\nexport function clearApprovalEvent(correlationId: string, nodeId: string): void {\r\n  const approvalKey = `${correlationId}_${nodeId}`;\r\n  dispatchedApprovalEvents.delete(approvalKey);\r\n\r\n  // Add to approval history for debugging\r\n  if (window._approvalEventHistory) {\r\n    window._approvalEventHistory.push({\r\n      correlationId,\r\n      nodeId,\r\n      nodeName: nodeId,\r\n      timestamp: Date.now(),\r\n      status: 'cleared'\r\n    });\r\n\r\n    // Keep only the last 10 events\r\n    if (window._approvalEventHistory.length > 10) {\r\n      window._approvalEventHistory = window._approvalEventHistory.slice(-10);\r\n    }\r\n  }\r\n\r\n  console.log(`Cleared approval event for node ${nodeId} with correlation ID ${correlationId}`);\r\n}\r\n\r\n/**\r\n * Clears all tracked approval events\r\n * Useful when components unmount or when resetting state\r\n */\r\nexport function clearAllApprovalEvents(): void {\r\n  dispatchedApprovalEvents.clear();\r\n\r\n  // Add to approval history for debugging\r\n  if (window._approvalEventHistory) {\r\n    window._approvalEventHistory.push({\r\n      correlationId: 'all',\r\n      nodeId: 'all',\r\n      nodeName: 'all',\r\n      timestamp: Date.now(),\r\n      status: 'cleared_all'\r\n    });\r\n\r\n    // Keep only the last 10 events\r\n    if (window._approvalEventHistory.length > 10) {\r\n      window._approvalEventHistory = window._approvalEventHistory.slice(-10);\r\n    }\r\n  }\r\n\r\n  // Clear the pending approval flag\r\n  window._pendingApproval = undefined;\r\n\r\n  console.log(\"Cleared all approval events\");\r\n}\r\n\r\n/**\r\n * Checks if an approval event has already been dispatched\r\n *\r\n * @param correlationId The correlation ID of the workflow execution\r\n * @param nodeId The ID of the node requiring approval\r\n * @returns True if the approval event has already been dispatched\r\n */\r\nexport function hasApprovalEventBeenDispatched(correlationId: string, nodeId: string): boolean {\r\n  const approvalKey = `${correlationId}_${nodeId}`;\r\n  return dispatchedApprovalEvents.has(approvalKey);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0DAA0D;;;;;;;AAoB1D,8CAA8C;AAC9C,uCAAmC;;AAEnC;AAEA,iEAAiE;AACjE,MAAM,2BAA2B,IAAI;AAS9B,SAAS,4BAA4B,aAAqB,EAAE,MAAc,EAAE,QAAgB;IACjG,8CAA8C;IAC9C,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,WAAW;QACrD,QAAQ,GAAG,CAAC,CAAC,iEAAiE,EAAE,cAAc,SAAS,EAAE,QAAQ;QACjH;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc,GAAG,cAAc,CAAC,EAAE,QAAQ;IAEhD,4BAA4B;IAC5B,MAAM,mBAAmB,KAAK,GAAG;IAEjC,wCAAwC;IACxC,IAAI,OAAO,qBAAqB,EAAE;QAChC,OAAO,qBAAqB,CAAC,IAAI,CAAC;YAChC;YACA;YACA,UAAU,YAAY;YACtB,WAAW;YACX,QAAQ;QACV;QAEA,+BAA+B;QAC/B,IAAI,OAAO,qBAAqB,CAAC,MAAM,GAAG,IAAI;YAC5C,OAAO,qBAAqB,GAAG,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACrE;IACF;IAEA,kDAAkD;IAClD,oBAAoB;IACpB,wDAAwD;IACxD,iFAAiF;IACjF,MAAM,iBAAiB,CAAC,yBAAyB,GAAG,CAAC,gBAC7B,mBAAmB,CAAC,OAAO,sBAAsB,IAAI,CAAC,IAAI;IAElF,IAAI,gBAAgB;QAClB,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,YAAY,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;QAE1F,sBAAsB;QACtB,yBAAyB,GAAG,CAAC;QAE7B,uEAAuE;QACvE,OAAO,sBAAsB,GAAG;QAEhC,8CAA8C;QAC9C,MAAM,gBAAgB,IAAI,YAAY,4BAA4B;YAChE,QAAQ;gBACN;gBACA;gBACA,UAAU,YAAY;gBACtB,WAAW;gBACX;YACF;QACF;QAEA,OAAO,aAAa,CAAC;QAErB,yEAAyE;QACzE,OAAO,gBAAgB,GAAG;YACxB;YACA;YACA,UAAU,YAAY;YACtB,WAAW;QACb;QAEA,oBAAoB;QACpB,WAAW;YACT,OAAO,aAAa,CAAC,IAAI,YAAY;QACvC,GAAG;IACL,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,YAAY,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5F;AACF;AAQO,SAAS,mBAAmB,aAAqB,EAAE,MAAc;IACtE,MAAM,cAAc,GAAG,cAAc,CAAC,EAAE,QAAQ;IAChD,yBAAyB,MAAM,CAAC;IAEhC,wCAAwC;IACxC,IAAI,OAAO,qBAAqB,EAAE;QAChC,OAAO,qBAAqB,CAAC,IAAI,CAAC;YAChC;YACA;YACA,UAAU;YACV,WAAW,KAAK,GAAG;YACnB,QAAQ;QACV;QAEA,+BAA+B;QAC/B,IAAI,OAAO,qBAAqB,CAAC,MAAM,GAAG,IAAI;YAC5C,OAAO,qBAAqB,GAAG,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACrE;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,OAAO,qBAAqB,EAAE,eAAe;AAC9F;AAMO,SAAS;IACd,yBAAyB,KAAK;IAE9B,wCAAwC;IACxC,IAAI,OAAO,qBAAqB,EAAE;QAChC,OAAO,qBAAqB,CAAC,IAAI,CAAC;YAChC,eAAe;YACf,QAAQ;YACR,UAAU;YACV,WAAW,KAAK,GAAG;YACnB,QAAQ;QACV;QAEA,+BAA+B;QAC/B,IAAI,OAAO,qBAAqB,CAAC,MAAM,GAAG,IAAI;YAC5C,OAAO,qBAAqB,GAAG,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACrE;IACF;IAEA,kCAAkC;IAClC,OAAO,gBAAgB,GAAG;IAE1B,QAAQ,GAAG,CAAC;AACd;AASO,SAAS,+BAA+B,aAAqB,EAAE,MAAc;IAClF,MAAM,cAAc,GAAG,cAAc,CAAC,EAAE,QAAQ;IAChD,OAAO,yBAAyB,GAAG,CAAC;AACtC", "debugId": null}}, {"offset": {"line": 2256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/mcp_marketplace_transform.ts"], "sourcesContent": ["/**\r\n * Utility functions to transform MCP Marketplace components for the orchestration service\r\n */\r\nimport { Node, Edge } from \"reactflow\";\r\nimport { WorkflowNodeData, MCPSchemaInfo } from \"@/types\";\r\nimport { useComponentStateStore } from \"@/store/mcpToolsStore\";\r\n\r\n/**\r\n * Transform a workflow for the orchestration service\r\n *\r\n * @param nodes The workflow nodes\r\n * @param edges The workflow edges\r\n * @returns The transformed workflow data\r\n */\r\nexport function transformWorkflowForOrchestration(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n): { nodes: Node<WorkflowNodeData>[]; edges: Edge[] } {\r\n  // Create proper deep copies to avoid modifying the originals and prevent string escaping\r\n  const transformedNodes = nodes.map(node => ({\r\n    ...node,\r\n    data: {\r\n      ...node.data,\r\n      config: { ...node.data.config },\r\n      definition: node.data.definition ? { ...node.data.definition } : undefined\r\n    }\r\n  }));\r\n  const transformedEdges = edges.map(edge => ({ ...edge }));\r\n\r\n  // Fix each node\r\n  for (let i = 0; i < transformedNodes.length; i++) {\r\n    transformedNodes[i] = fixMCPMarketplaceNode(transformedNodes[i]);\r\n  }\r\n\r\n  return {\r\n    nodes: transformedNodes,\r\n    edges: transformedEdges,\r\n  };\r\n}\r\n\r\n/**\r\n * Fix an MCP Marketplace node to match the expected template\r\n *\r\n * @param node The node to fix\r\n * @returns The fixed node\r\n */\r\nexport function fixMCPMarketplaceNode(node: Node<WorkflowNodeData>): Node<WorkflowNodeData> {\r\n  // Create a proper deep copy to avoid modifying the original and prevent string escaping\r\n  const fixedNode = {\r\n    ...node,\r\n    data: {\r\n      ...node.data,\r\n      config: { ...node.data.config },\r\n      definition: node.data.definition ? { ...node.data.definition } : undefined\r\n    }\r\n  };\r\n\r\n  // Check if this is an MCP Marketplace component\r\n  const nodeData = fixedNode.data;\r\n  const nodeType = nodeData?.type;\r\n  const originalType = nodeData?.originalType;\r\n\r\n  const isMCPMarketplace =\r\n    nodeType === \"mcp\" ||\r\n    nodeType === \"MCPMarketplaceComponent\" ||\r\n    (originalType && originalType.includes(\"mcp_\"));\r\n\r\n  if (!isMCPMarketplace) {\r\n    return fixedNode;\r\n  }\r\n\r\n  // Get the node definition and config\r\n  const definition = nodeData?.definition;\r\n  const config = nodeData?.config || {};\r\n\r\n  // Extract input values\r\n  const inputValues: Record<string, any> = {};\r\n\r\n  // PRIORITY 1: Get values from the component state store (highest priority)\r\n  // These are the most up-to-date values from the UI\r\n  const stateStore = useComponentStateStore.getState();\r\n  const nodeState = stateStore.nodes[node.id] || {};\r\n\r\n  // Check if there are any values in the component state\r\n  if (nodeState) {\r\n    // Get values from the config in the component state\r\n    const stateConfig = nodeState.config || {};\r\n    for (const [key, value] of Object.entries(stateConfig)) {\r\n      if (value !== null && value !== undefined && value !== \"\") {\r\n        inputValues[key] = value;\r\n      }\r\n    }\r\n\r\n    // Also check for direct values in the component state\r\n    for (const [key, value] of Object.entries(nodeState)) {\r\n      if (key !== \"config\" && value !== null && value !== undefined && value !== \"\") {\r\n        inputValues[key] = value;\r\n      }\r\n    }\r\n  }\r\n\r\n  // PRIORITY 2: Get values from the node data config\r\n  // These are values that might have been set programmatically\r\n  if (node.data.config) {\r\n    for (const [key, value] of Object.entries(node.data.config)) {\r\n      // Skip special keys that are not user inputs and skip the inputs array\r\n      if (\r\n        ![\r\n          \"mode\",\r\n          \"selected_tool_name\",\r\n          \"stdio_command\",\r\n          \"tool_args\",\r\n          \"node_id\",\r\n          \"_internal_state\",\r\n          \"inputs\",\r\n        ].includes(key) &&\r\n        value !== null &&\r\n        value !== undefined &&\r\n        value !== \"\"\r\n      ) {\r\n        // Only add if not already set from component state\r\n        if (!(key in inputValues)) {\r\n          inputValues[key] = value;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // PRIORITY 3: Get values from the inputs in the definition\r\n  // These are the default values defined in the component\r\n  if (definition && definition.inputs) {\r\n    const inputs = definition.inputs;\r\n    for (const input of inputs) {\r\n      const name = input.name;\r\n      const value = input.value;\r\n\r\n      // Skip empty values and special handle inputs\r\n      if (\r\n        value === null ||\r\n        value === undefined ||\r\n        value === \"\" ||\r\n        (typeof value === \"object\" && Object.keys(value).length === 0) ||\r\n        name.endsWith(\"_handle\")\r\n      ) {\r\n        continue;\r\n      }\r\n\r\n      // Only add if not already set from higher priority sources\r\n      if (!(name in inputValues)) {\r\n        inputValues[name] = value;\r\n      }\r\n    }\r\n  }\r\n\r\n  // PRIORITY 4: Get values from tool_args if it exists\r\n  // These are values that might have been set for MCP Tools\r\n  if (config.tool_args) {\r\n    const toolArgs = config.tool_args;\r\n    for (const [name, value] of Object.entries(toolArgs)) {\r\n      if (value !== null && value !== undefined && value !== \"\") {\r\n        // Only add if not already set from higher priority sources\r\n        if (!(name in inputValues)) {\r\n          inputValues[name] = value;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Log the values for debugging\r\n  console.log(`[MCP Transform] Node ID: ${node.id}`);\r\n  console.log(`[MCP Transform] Original type: ${originalType}`);\r\n  console.log(`[MCP Transform] Node type: ${nodeType}`);\r\n  console.log(`[MCP Transform] Is MCP Marketplace: ${isMCPMarketplace}`);\r\n  console.log(`[MCP Transform] Final input values:`, inputValues);\r\n\r\n  // Special handling for API request nodes to ensure method is preserved\r\n  if (nodeData.originalType === \"ApiRequestNode\" || nodeData.type === \"ApiRequestNode\") {\r\n    console.log(`[MCP Transform] Preserving API request node configuration`);\r\n\r\n    // Make sure to preserve the method value from the original config\r\n    if (nodeData.config && nodeData.config.method) {\r\n      console.log(\r\n        `[MCP Transform] Preserving method ${nodeData.config.method} for API request node`,\r\n      );\r\n      inputValues.method = nodeData.config.method;\r\n    }\r\n  }\r\n\r\n  // Create a new config with the input values directly\r\n  nodeData.config = inputValues;\r\n\r\n  return fixedNode;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAGD;;AASO,SAAS,kCACd,KAA+B,EAC/B,KAAa;IAEb,yFAAyF;IACzF,MAAM,mBAAmB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC1C,GAAG,IAAI;YACP,MAAM;gBACJ,GAAG,KAAK,IAAI;gBACZ,QAAQ;oBAAE,GAAG,KAAK,IAAI,CAAC,MAAM;gBAAC;gBAC9B,YAAY,KAAK,IAAI,CAAC,UAAU,GAAG;oBAAE,GAAG,KAAK,IAAI,CAAC,UAAU;gBAAC,IAAI;YACnE;QACF,CAAC;IACD,MAAM,mBAAmB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YAAE,GAAG,IAAI;QAAC,CAAC;IAEvD,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;QAChD,gBAAgB,CAAC,EAAE,GAAG,sBAAsB,gBAAgB,CAAC,EAAE;IACjE;IAEA,OAAO;QACL,OAAO;QACP,OAAO;IACT;AACF;AAQO,SAAS,sBAAsB,IAA4B;IAChE,wFAAwF;IACxF,MAAM,YAAY;QAChB,GAAG,IAAI;QACP,MAAM;YACJ,GAAG,KAAK,IAAI;YACZ,QAAQ;gBAAE,GAAG,KAAK,IAAI,CAAC,MAAM;YAAC;YAC9B,YAAY,KAAK,IAAI,CAAC,UAAU,GAAG;gBAAE,GAAG,KAAK,IAAI,CAAC,UAAU;YAAC,IAAI;QACnE;IACF;IAEA,gDAAgD;IAChD,MAAM,WAAW,UAAU,IAAI;IAC/B,MAAM,WAAW,UAAU;IAC3B,MAAM,eAAe,UAAU;IAE/B,MAAM,mBACJ,aAAa,SACb,aAAa,6BACZ,gBAAgB,aAAa,QAAQ,CAAC;IAEzC,IAAI,CAAC,kBAAkB;QACrB,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,UAAU,UAAU,CAAC;IAEpC,uBAAuB;IACvB,MAAM,cAAmC,CAAC;IAE1C,2EAA2E;IAC3E,mDAAmD;IACnD,MAAM,aAAa,6HAAA,CAAA,yBAAsB,CAAC,QAAQ;IAClD,MAAM,YAAY,WAAW,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC;IAEhD,uDAAuD;IACvD,wCAAe;QACb,oDAAoD;QACpD,MAAM,cAAc,UAAU,MAAM,IAAI,CAAC;QACzC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,aAAc;YACtD,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;gBACzD,WAAW,CAAC,IAAI,GAAG;YACrB;QACF;QAEA,sDAAsD;QACtD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,WAAY;YACpD,IAAI,QAAQ,YAAY,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;gBAC7E,WAAW,CAAC,IAAI,GAAG;YACrB;QACF;IACF;IAEA,mDAAmD;IACnD,6DAA6D;IAC7D,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;QACpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC,MAAM,EAAG;YAC3D,uEAAuE;YACvE,IACE,CAAC;gBACC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAAC,QAAQ,CAAC,QACX,UAAU,QACV,UAAU,aACV,UAAU,IACV;gBACA,mDAAmD;gBACnD,IAAI,CAAC,CAAC,OAAO,WAAW,GAAG;oBACzB,WAAW,CAAC,IAAI,GAAG;gBACrB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,wDAAwD;IACxD,IAAI,cAAc,WAAW,MAAM,EAAE;QACnC,MAAM,SAAS,WAAW,MAAM;QAChC,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,OAAO,MAAM,IAAI;YACvB,MAAM,QAAQ,MAAM,KAAK;YAEzB,8CAA8C;YAC9C,IACE,UAAU,QACV,UAAU,aACV,UAAU,MACT,OAAO,UAAU,YAAY,OAAO,IAAI,CAAC,OAAO,MAAM,KAAK,KAC5D,KAAK,QAAQ,CAAC,YACd;gBACA;YACF;YAEA,2DAA2D;YAC3D,IAAI,CAAC,CAAC,QAAQ,WAAW,GAAG;gBAC1B,WAAW,CAAC,KAAK,GAAG;YACtB;QACF;IACF;IAEA,qDAAqD;IACrD,0DAA0D;IAC1D,IAAI,OAAO,SAAS,EAAE;QACpB,MAAM,WAAW,OAAO,SAAS;QACjC,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;YACpD,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;gBACzD,2DAA2D;gBAC3D,IAAI,CAAC,CAAC,QAAQ,WAAW,GAAG;oBAC1B,WAAW,CAAC,KAAK,GAAG;gBACtB;YACF;QACF;IACF;IAEA,+BAA+B;IAC/B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,EAAE,EAAE;IACjD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,cAAc;IAC5D,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,UAAU;IACpD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,kBAAkB;IACrE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,CAAC,EAAE;IAEnD,uEAAuE;IACvE,IAAI,SAAS,YAAY,KAAK,oBAAoB,SAAS,IAAI,KAAK,kBAAkB;QACpF,QAAQ,GAAG,CAAC,CAAC,yDAAyD,CAAC;QAEvE,kEAAkE;QAClE,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE;YAC7C,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,SAAS,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC;YAEpF,YAAY,MAAM,GAAG,SAAS,MAAM,CAAC,MAAM;QAC7C;IACF;IAEA,qDAAqD;IACrD,SAAS,MAAM,GAAG;IAElB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/field-utils.ts"], "sourcesContent": ["/**\r\n * Utility functions for field handling in the workflow builder\r\n */\r\n\r\n/**\r\n * Determines whether a field should be included in the execution dialog or start node data\r\n * based on its configuration and connection status.\r\n *\r\n * @param isRequired Whether the field is required\r\n * @param isDirectlyConnected Whether the field is directly connected to the Start node\r\n * @param hasConfiguredValue Whether the field already has a value configured in the inspector panel\r\n * @param hasIncomingConnection Whether the field has an incoming connection from another node\r\n * @returns Boolean indicating whether the field should be included\r\n */\r\nexport function shouldIncludeField(\r\n  isRequired: boolean,\r\n  isDirectlyConnected: boolean,\r\n  hasConfiguredValue: boolean,\r\n  hasIncomingConnection: boolean = false\r\n): boolean {\r\n  // Fields directly connected to the Start node should ALWAYS be included,\r\n  // regardless of whether they have pre-configured values or incoming connections\r\n  if (isDirectlyConnected) {\r\n    return true;\r\n  }\r\n\r\n  // Fields with incoming connections from other nodes should be excluded\r\n  // (unless they're directly connected to the Start node, which we already checked)\r\n  if (hasIncomingConnection) {\r\n    return false;\r\n  }\r\n\r\n  // For required fields, only include them if they don't have a configured value\r\n  return isRequired && !hasConfiguredValue;\r\n}\r\n\r\n/**\r\n * Logs detailed information about a field's status for debugging purposes\r\n *\r\n * @param context The context where this function is called (e.g., 'ExecutionDialog', 'ExtractStartNodeData')\r\n * @param fieldId The ID of the field\r\n * @param nodeName The name of the node containing the field\r\n * @param fieldName The name of the field\r\n * @param isRequired Whether the field is required\r\n * @param requiredValue The actual value of the required property\r\n * @param isDirectlyConnected Whether the field is directly connected to the Start node\r\n * @param hasConfiguredValue Whether the field already has a value configured in the inspector panel\r\n * @param configuredValue The actual configured value, if any\r\n * @param hasIncomingConnection Whether the field has an incoming connection from another node\r\n */\r\nexport function logFieldStatus(\r\n  context: string,\r\n  fieldId: string,\r\n  nodeName: string,\r\n  fieldName: string,\r\n  isRequired: boolean,\r\n  requiredValue: any,\r\n  isDirectlyConnected: boolean,\r\n  hasConfiguredValue: boolean,\r\n  configuredValue?: any,\r\n  hasIncomingConnection: boolean = false\r\n): void {\r\n  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);\r\n  const shouldInclude = shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection);\r\n\r\n  console.log(`[${timestamp}] [${context}] Field ${nodeName}.${fieldName} status check:\r\n    - required property value: ${requiredValue === undefined ? \"undefined\" : requiredValue}\r\n    - required !== false: ${isRequired ? \"YES\" : \"NO\"}\r\n    - directly connected to Start: ${isDirectlyConnected ? \"YES\" : \"NO\"}\r\n    - has configured value: ${hasConfiguredValue ? \"YES\" : \"NO\"}\r\n    - has incoming connection: ${hasIncomingConnection ? \"YES\" : \"NO\"}\r\n    - configured value: ${hasConfiguredValue ? JSON.stringify(configuredValue) : \"undefined\"}\r\n    - Should include: ${shouldInclude ? \"YES\" : \"NO\"}`);\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;CASC;;;;AACM,SAAS,mBACd,UAAmB,EACnB,mBAA4B,EAC5B,kBAA2B,EAC3B,wBAAiC,KAAK;IAEtC,yEAAyE;IACzE,gFAAgF;IAChF,IAAI,qBAAqB;QACvB,OAAO;IACT;IAEA,uEAAuE;IACvE,kFAAkF;IAClF,IAAI,uBAAuB;QACzB,OAAO;IACT;IAEA,+EAA+E;IAC/E,OAAO,cAAc,CAAC;AACxB;AAgBO,SAAS,eACd,OAAe,EACf,OAAe,EACf,QAAgB,EAChB,SAAiB,EACjB,UAAmB,EACnB,aAAkB,EAClB,mBAA4B,EAC5B,kBAA2B,EAC3B,eAAqB,EACrB,wBAAiC,KAAK;IAEtC,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;IAC1E,MAAM,gBAAgB,mBAAmB,YAAY,qBAAqB,oBAAoB;IAE9F,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,EAAE,QAAQ,QAAQ,EAAE,SAAS,CAAC,EAAE,UAAU;+BAC1C,EAAE,kBAAkB,YAAY,cAAc,cAAc;0BACjE,EAAE,aAAa,QAAQ,KAAK;mCACnB,EAAE,sBAAsB,QAAQ,KAAK;4BAC5C,EAAE,qBAAqB,QAAQ,KAAK;+BACjC,EAAE,wBAAwB,QAAQ,KAAK;wBAC9C,EAAE,qBAAqB,KAAK,SAAS,CAAC,mBAAmB,YAAY;sBACvE,EAAE,gBAAgB,QAAQ,MAAM;AACtD", "debugId": null}}, {"offset": {"line": 2460, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/mock-credential-service.ts"], "sourcesContent": ["/**\r\n * Mock credential service that uses localStorage for credential management.\r\n * This is a temporary solution until the backend credential API is implemented.\r\n */\r\n\r\nimport {\r\n  Credential,\r\n  CredentialCreate,\r\n  CredentialListResponse,\r\n  CredentialDeleteResponse,\r\n} from \"./api\";\r\n\r\nconst STORAGE_KEY = \"workflow_builder_credentials\";\r\n\r\n/**\r\n * Get all credentials from localStorage\r\n */\r\nfunction getStoredCredentials(): Credential[] {\r\n  try {\r\n    const storedData = localStorage.getItem(STORAGE_KEY);\r\n    if (!storedData) return [];\r\n    return JSON.parse(storedData);\r\n  } catch (error) {\r\n    console.error(\"Error reading credentials from localStorage:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\n/**\r\n * Save credentials to localStorage\r\n */\r\nfunction saveCredentials(credentials: Credential[]): void {\r\n  try {\r\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(credentials));\r\n  } catch (error) {\r\n    console.error(\"Error saving credentials to localStorage:\", error);\r\n  }\r\n}\r\n\r\n/**\r\n * Fetch all credentials (mock implementation)\r\n */\r\nexport async function mockFetchCredentials(): Promise<CredentialListResponse> {\r\n  // Simulate network delay\r\n  await new Promise((resolve) => setTimeout(resolve, 300));\r\n\r\n  const credentials = getStoredCredentials();\r\n  console.log(\"Mock: Fetched credentials\", credentials);\r\n\r\n  return { credentials };\r\n}\r\n\r\n/**\r\n * Create a new credential (mock implementation)\r\n */\r\nexport async function mockCreateCredential(credential: CredentialCreate): Promise<Credential> {\r\n  // Simulate network delay\r\n  await new Promise((resolve) => setTimeout(resolve, 500));\r\n\r\n  // Generate ID from name\r\n  const id = credential.name.toLowerCase().replace(/\\s+/g, \"_\");\r\n\r\n  // Check if credential with this ID already exists\r\n  const credentials = getStoredCredentials();\r\n  if (credentials.some((c) => c.id === id)) {\r\n    throw new Error(`Credential with ID '${id}' already exists`);\r\n  }\r\n\r\n  // Create new credential\r\n  const newCredential: Credential = {\r\n    id,\r\n    name: credential.name,\r\n    type: credential.type,\r\n  };\r\n\r\n  // Save to localStorage\r\n  saveCredentials([...credentials, newCredential]);\r\n\r\n  // Also save the value in a separate item for security (in a real app, this would be on the server)\r\n  try {\r\n    localStorage.setItem(`${STORAGE_KEY}_${id}_value`, credential.value);\r\n  } catch (error) {\r\n    console.error(`Error saving credential value for ${id}:`, error);\r\n  }\r\n\r\n  console.log(\"Mock: Created credential\", newCredential);\r\n\r\n  return newCredential;\r\n}\r\n\r\n/**\r\n * Delete a credential (mock implementation)\r\n */\r\nexport async function mockDeleteCredential(\r\n  credentialId: string,\r\n): Promise<CredentialDeleteResponse> {\r\n  // Simulate network delay\r\n  await new Promise((resolve) => setTimeout(resolve, 400));\r\n\r\n  // Get current credentials\r\n  const credentials = getStoredCredentials();\r\n\r\n  // Check if credential exists\r\n  if (!credentials.some((c) => c.id === credentialId)) {\r\n    throw new Error(`Credential with ID '${credentialId}' not found`);\r\n  }\r\n\r\n  // Filter out the credential to delete\r\n  const updatedCredentials = credentials.filter((c) => c.id !== credentialId);\r\n\r\n  // Save updated list\r\n  saveCredentials(updatedCredentials);\r\n\r\n  // Remove the value\r\n  try {\r\n    localStorage.removeItem(`${STORAGE_KEY}_${credentialId}_value`);\r\n  } catch (error) {\r\n    console.error(`Error removing credential value for ${credentialId}:`, error);\r\n  }\r\n\r\n  console.log(\"Mock: Deleted credential\", credentialId);\r\n\r\n  return {\r\n    success: true,\r\n    message: `Credential '${credentialId}' deleted successfully`,\r\n  };\r\n}\r\n\r\n/**\r\n * Get a credential value (mock implementation)\r\n * This would be used by the WorkflowContext in a real implementation\r\n */\r\nexport function mockGetCredentialValue(credentialId: string): string | null {\r\n  try {\r\n    return localStorage.getItem(`${STORAGE_KEY}_${credentialId}_value`);\r\n  } catch (error) {\r\n    console.error(`Error getting credential value for ${credentialId}:`, error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AASD,MAAM,cAAc;AAEpB;;CAEC,GACD,SAAS;IACP,IAAI;QACF,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO,EAAE;IACX;AACF;AAEA;;CAEC,GACD,SAAS,gBAAgB,WAAyB;IAChD,IAAI;QACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAKO,eAAe;IACpB,yBAAyB;IACzB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IAEnD,MAAM,cAAc;IACpB,QAAQ,GAAG,CAAC,6BAA6B;IAEzC,OAAO;QAAE;IAAY;AACvB;AAKO,eAAe,qBAAqB,UAA4B;IACrE,yBAAyB;IACzB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IAEnD,wBAAwB;IACxB,MAAM,KAAK,WAAW,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;IAEzD,kDAAkD;IAClD,MAAM,cAAc;IACpB,IAAI,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK;QACxC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,gBAAgB,CAAC;IAC7D;IAEA,wBAAwB;IACxB,MAAM,gBAA4B;QAChC;QACA,MAAM,WAAW,IAAI;QACrB,MAAM,WAAW,IAAI;IACvB;IAEA,uBAAuB;IACvB,gBAAgB;WAAI;QAAa;KAAc;IAE/C,mGAAmG;IACnG,IAAI;QACF,aAAa,OAAO,CAAC,GAAG,YAAY,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,WAAW,KAAK;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;IAC5D;IAEA,QAAQ,GAAG,CAAC,4BAA4B;IAExC,OAAO;AACT;AAKO,eAAe,qBACpB,YAAoB;IAEpB,yBAAyB;IACzB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IAEnD,0BAA0B;IAC1B,MAAM,cAAc;IAEpB,6BAA6B;IAC7B,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,eAAe;QACnD,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,aAAa,WAAW,CAAC;IAClE;IAEA,sCAAsC;IACtC,MAAM,qBAAqB,YAAY,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IAE9D,oBAAoB;IACpB,gBAAgB;IAEhB,mBAAmB;IACnB,IAAI;QACF,aAAa,UAAU,CAAC,GAAG,YAAY,CAAC,EAAE,aAAa,MAAM,CAAC;IAChE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,aAAa,CAAC,CAAC,EAAE;IACxE;IAEA,QAAQ,GAAG,CAAC,4BAA4B;IAExC,OAAO;QACL,SAAS;QACT,SAAS,CAAC,YAAY,EAAE,aAAa,sBAAsB,CAAC;IAC9D;AACF;AAMO,SAAS,uBAAuB,YAAoB;IACzD,IAAI;QACF,OAAO,aAAa,OAAO,CAAC,GAAG,YAAY,CAAC,EAAE,aAAa,MAAM,CAAC;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,aAAa,CAAC,CAAC,EAAE;QACrE,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2569, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/api.ts"], "sourcesContent": ["// src/lib/api.ts\r\nimport { WorkflowNodeData, ComponentsApiResponse } from \"@/types\";\r\nimport { Node, Edge } from \"reactflow\";\r\nimport { transformWorkflowForOrchestration } from \"./mcp_marketplace_transform\";\r\nimport { useComponentStateStore } from \"@/store/mcpToolsStore\";\r\nimport { API_ENDPOINTS } from \"./apiConfig\";\r\nimport { getClientAccessToken } from \"./clientCookies\";\r\nimport { getAccessToken } from \"./cookies\";\r\nimport { getConnectedNodes } from \"./validation/utils\";\r\nimport { collectAllFields } from \"./validation/fieldValidation\";\r\nimport { MissingField } from \"./validation/types\";\r\nimport { shouldIncludeField, logFieldStatus } from \"./field-utils\";\r\n// Use environment variables for API URLs\r\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;\r\nexport const BACKEND_API_URL = API_BASE_URL;\r\n\r\n// Debug function for MCP Tools\r\nexport async function debugMCPTools(data: any): Promise<any> {\r\n  try {\r\n    // Make sure the selected_tool_name is included in the node_config if available\r\n    if (data.node_config && !data.node_config.selected_tool_name && data.node_config.inputs) {\r\n      // Try to find the selected_tool_name in the inputs\r\n      const toolInput = data.node_config.inputs.find(\r\n        (input: any) => input.name === \"selected_tool_name\",\r\n      );\r\n      if (toolInput && toolInput.value) {\r\n        data.node_config.selected_tool_name = toolInput.value;\r\n        console.log(\r\n          `Added selected_tool_name to debug payload: ${data.node_config.selected_tool_name}`,\r\n        );\r\n      }\r\n    }\r\n\r\n    console.log(`Sending debug MCP tools request with data:`, data);\r\n\r\n    const response = await fetch(`${BACKEND_API_URL}/debug_mcp_tools`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API Error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log(`Debug MCP tools response:`, result);\r\n\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Failed to debug MCP Tools:\", error);\r\n    return { success: false, error: String(error) };\r\n  }\r\n}\r\n\r\n// Direct function to fetch MCP tools\r\nexport async function fetchMCPTools(nodeConfig: any, buttonName: string): Promise<any> {\r\n  try {\r\n    // Create a clean config with only essential properties to avoid duplicating inputs\r\n    const cleanConfig: any = {\r\n      // Extract only the essential configuration values\r\n      mode: nodeConfig.mode || \"Stdio\",\r\n      command: nodeConfig.command || \"\",\r\n      sse_url: nodeConfig.sse_url || \"\",\r\n      selected_tool_name: nodeConfig.selected_tool_name || \"\",\r\n      connection_status: nodeConfig.connection_status || \"Not Connected\",\r\n    };\r\n\r\n    // Save the original mode for all operations\r\n    const originalMode = cleanConfig.mode;\r\n    console.log(`Original mode before operation: ${originalMode}`);\r\n\r\n    // Ensure SSE URL is properly included for SSE mode\r\n    if (buttonName === \"fetch_sse_tools\") {\r\n      console.log(`SSE URL from config: ${cleanConfig.sse_url}`);\r\n\r\n      // Force mode to SSE for this button\r\n      cleanConfig.mode = \"SSE\";\r\n      console.log(`Forced mode to SSE for fetch_sse_tools button`);\r\n\r\n      // If SSE URL is not in the config, log a warning\r\n      if (!cleanConfig.sse_url) {\r\n        console.warn(\"SSE URL is missing in the config!\");\r\n      }\r\n    } else if (buttonName === \"fetch_stdio_tools\") {\r\n      // Force mode to Stdio for this button\r\n      cleanConfig.mode = \"Stdio\";\r\n      console.log(`Forced mode to Stdio for fetch_stdio_tools button`);\r\n    } else if (buttonName === \"selected_tool_name\") {\r\n      // For tool selection, preserve the current mode and connection status\r\n      console.log(`Tool selection action: ${buttonName}`);\r\n      console.log(`Preserving current mode: ${cleanConfig.mode}`);\r\n      console.log(`Preserving connection status: ${cleanConfig.connection_status}`);\r\n\r\n      // Make sure connection_status is set to Connected for tool selection\r\n      if (cleanConfig.connection_status !== \"Connected\") {\r\n        console.log(`Setting connection_status to Connected for tool selection`);\r\n        cleanConfig.connection_status = \"Connected\";\r\n      }\r\n    }\r\n    // Don't change the mode for other buttons\r\n\r\n    // Ensure connection_status is included\r\n    if (!cleanConfig.connection_status) {\r\n      cleanConfig.connection_status = \"Not Connected\";\r\n    }\r\n\r\n    // Create a payload that simulates what the backend expects\r\n    const payload: any = {\r\n      config: cleanConfig,\r\n    };\r\n\r\n    // Set the button value based on the button name\r\n    if (buttonName === \"selected_tool_name\") {\r\n      payload.selected_tool_name = cleanConfig.selected_tool_name;\r\n      console.log(`Setting selected_tool_name in payload: ${cleanConfig.selected_tool_name}`);\r\n\r\n      // Make sure connection_status is set to Connected\r\n      if (cleanConfig.connection_status !== \"Connected\") {\r\n        console.log(`Ensuring connection_status is Connected for tool selection`);\r\n        cleanConfig.connection_status = \"Connected\";\r\n        payload.config.connection_status = \"Connected\";\r\n      }\r\n    } else {\r\n      payload[buttonName] = true; // Set the button value to true\r\n    }\r\n\r\n    // IMPORTANT: Always preserve the mode from the config for all operations\r\n    // This ensures we don't reset to Stdio when selecting a tool or performing other operations\r\n    console.log(`Preserving mode in payload: ${originalMode}`);\r\n    payload.config.mode = originalMode;\r\n\r\n    // Always make sure we include the selected_tool_name if available\r\n    console.log(`Selected tool name from config: ${cleanConfig.selected_tool_name}`);\r\n    if (!cleanConfig.selected_tool_name && cleanConfig.inputs) {\r\n      // Try to find the selected_tool_name in the inputs\r\n      const toolInput = cleanConfig.inputs.find(\r\n        (input: any) => input.name === \"selected_tool_name\",\r\n      );\r\n      if (toolInput && toolInput.value) {\r\n        cleanConfig.selected_tool_name = toolInput.value;\r\n        console.log(`Found selected_tool_name in inputs: ${cleanConfig.selected_tool_name}`);\r\n      }\r\n    }\r\n\r\n    // Make sure the selected_tool_name is included in the payload\r\n    if (cleanConfig.selected_tool_name) {\r\n      payload.config.selected_tool_name = cleanConfig.selected_tool_name;\r\n      console.log(`Added selected_tool_name to payload: ${payload.config.selected_tool_name}`);\r\n    }\r\n\r\n    console.log(`Sending direct MCP tools fetch request with payload:`, payload);\r\n\r\n    const response = await fetch(`${BACKEND_API_URL}/fetch_mcp_tools`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(payload),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API Error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    const responseData = await response.json();\r\n    console.log(`MCP tools fetch response:`, responseData);\r\n\r\n    // Extract the result from the response\r\n    const result = responseData.result || responseData;\r\n\r\n    // Process the result to ensure it's properly structured\r\n    if (result && result.inputs) {\r\n      console.log(`Processing result with ${result.inputs.length} inputs`);\r\n\r\n      // Ensure mode is preserved in the result\r\n      if (buttonName === \"fetch_sse_tools\") {\r\n        // For SSE mode, ensure the mode is set to SSE\r\n        result.mode = \"SSE\";\r\n\r\n        // Also fix the mode in the inputs array\r\n        if (Array.isArray(result.inputs)) {\r\n          const modeInput = result.inputs.find((input: any) => input.name === \"mode\");\r\n          if (modeInput) {\r\n            console.log(`Setting mode input value to SSE`);\r\n            modeInput.value = \"SSE\";\r\n          }\r\n        }\r\n      } else if (buttonName === \"fetch_stdio_tools\") {\r\n        // For Stdio mode, ensure the mode is set to Stdio\r\n        result.mode = \"Stdio\";\r\n\r\n        // Also fix the mode in the inputs array\r\n        if (Array.isArray(result.inputs)) {\r\n          const modeInput = result.inputs.find((input: any) => input.name === \"mode\");\r\n          if (modeInput) {\r\n            console.log(`Setting mode input value to Stdio`);\r\n            modeInput.value = \"Stdio\";\r\n          }\r\n        }\r\n      }\r\n\r\n      // Store the tool schemas if available\r\n      if (result._internal_state && result._internal_state.tool_schemas) {\r\n        console.log(\r\n          `Storing ${Object.keys(result._internal_state.tool_schemas).length} tool schemas`,\r\n        );\r\n        // You could store these in a global state if needed\r\n      }\r\n    }\r\n\r\n    return responseData.success ? result : responseData;\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch MCP tools:\", error);\r\n    return { success: false, error: String(error) };\r\n  }\r\n}\r\n\r\nexport async function fetchComponents(): Promise<ComponentsApiResponse> {\r\n  try {\r\n    const response = await fetch(`http://localhost:8000/api/v1/components`);\r\n    console.log(\"Sent the request to fetch components with the url as :\", response.url);\r\n    if (!response.ok) {\r\n      throw new Error(`API Error: ${response.status} ${response.statusText}`);\r\n    }\r\n    const data: ComponentsApiResponse = await response.json();\r\n    console.log(\"Fetched components:\", data); // For debugging\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch components:\", error);\r\n    // Return empty object or re-throw error based on how you want to handle failures\r\n    return {};\r\n  }\r\n}\r\n\r\n/**\r\n * Fetch MCP (Marketplace Components) from the API\r\n * @returns A promise that resolves to a ComponentsApiResponse object\r\n */\r\nexport async function fetchMCPComponents(): Promise<ComponentsApiResponse> {\r\n  try {\r\n    // Get the access token based on environment\r\n    let accessToken;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      accessToken = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      accessToken = await getAccessToken();\r\n    }\r\n\r\n    // Prepare headers with authentication\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    // Add Authorization header if we have a token\r\n    if (accessToken) {\r\n      headers[\"Authorization\"] = `Bearer ${accessToken}`;\r\n    }\r\n\r\n    const response = await fetch(`${API_ENDPOINTS.MCPS.LIST}`, {\r\n      method: \"GET\",\r\n      headers: headers,\r\n    });\r\n\r\n    console.log(\"Sent the request to fetch MCP components with the url as:\", response.url);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API Error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    // The API returns an array of MCP components, but we need to transform it to match ComponentsApiResponse\r\n    const responseData = await response.json();\r\n    console.log(\"Fetched MCP response:\", responseData); // For debugging\r\n\r\n    // Check if the response is in the expected format (array or data property containing array)\r\n    let mcpData: any[] = [];\r\n\r\n    if (Array.isArray(responseData)) {\r\n      // If the response is already an array, use it directly\r\n      console.log(\"MCP response is an array with\", responseData.length, \"items\");\r\n      mcpData = responseData;\r\n    } else if (responseData && responseData.data && Array.isArray(responseData.data)) {\r\n      // If the response has a data property that is an array, use that\r\n      console.log(\"MCP response has data property with\", responseData.data.length, \"items\");\r\n      mcpData = responseData.data;\r\n    } else {\r\n      // Handle the case where the response is an object with the data we need\r\n      console.error(\"MCP response is not in expected format\");\r\n      console.log(\"MCP response type:\", typeof responseData);\r\n      console.log(\"MCP response structure:\", Object.keys(responseData || {}));\r\n\r\n      // If the response is the example data provided by the user, use it directly\r\n      if (responseData && typeof responseData === 'object' && responseData.data) {\r\n        console.log(\"Using data property from response\");\r\n        mcpData = Array.isArray(responseData.data) ? responseData.data : [responseData.data];\r\n      } else if (responseData && typeof responseData === 'object') {\r\n        // If it's an object with MCP properties, wrap it in an array\r\n        console.log(\"Wrapping response object in array\");\r\n        mcpData = [responseData];\r\n      } else {\r\n        // Return empty object if we can't process the response\r\n        console.error(\"Cannot process MCP response, returning empty MCP category\");\r\n        return { \"MCP\": {} };\r\n      }\r\n    }\r\n\r\n    console.log(\"Processed MCP data:\", mcpData);\r\n\r\n    // Transform the MCP array into the ComponentsApiResponse format\r\n    // Create a \"MCP\" category in the components object\r\n    const mcpComponents: ComponentsApiResponse = {\r\n      \"MCP\": {}\r\n    };\r\n\r\n    // Process each MCP component\r\n    mcpData.forEach((mcp: any) => {\r\n      // For each tool in the MCP, create a component\r\n      if (mcp.mcp_tools_config && mcp.mcp_tools_config.tools) {\r\n        mcp.mcp_tools_config.tools.forEach((tool: any) => {\r\n          // Create a unique component name\r\n          const componentName = `MCP_${mcp.name}_${tool.name}`.replace(/\\s+/g, '_');\r\n\r\n          // Create the component definition\r\n          mcpComponents[\"MCP\"][componentName] = {\r\n            name: componentName,\r\n            display_name: `${mcp.name} - ${tool.name}`,\r\n            description: tool.description || mcp.description,\r\n            category: \"MCP\",\r\n            icon: \"Cloud\", // Default icon for MCP components\r\n            beta: true,\r\n            inputs: [],\r\n            outputs: [],\r\n            is_valid: true,\r\n            path: `mcp.${componentName.toLowerCase()}`,\r\n            type: \"MCP\",\r\n            mcp_info: {\r\n              server_id: mcp.id || \"\",\r\n              server_path: mcp.url || \"\",\r\n              tool_name: tool.name,\r\n              input_schema: tool.input_schema,\r\n              output_schema: tool.output_schema\r\n            }\r\n          };\r\n\r\n          // Convert input schema to input definitions\r\n          if (tool.input_schema && tool.input_schema.properties) {\r\n            const requiredFields = tool.input_schema.required || [];\r\n\r\n            Object.entries(tool.input_schema.properties).forEach(([propName, propSchema]: [string, any]) => {\r\n              const inputDef = {\r\n                name: propName,\r\n                display_name: propSchema.title || propName.replace(/_/g, ' '),\r\n                info: propSchema.description || \"\",\r\n                input_type: mapJsonSchemaTypeToInputType(propSchema.type, propSchema),\r\n                input_types: null,\r\n                required: requiredFields.includes(propName),\r\n                is_handle: true, // Make all inputs connectable\r\n                is_list: propSchema.type === \"array\",\r\n                real_time_refresh: false,\r\n                advanced: false,\r\n                value: propSchema.default || null,\r\n                options: propSchema.enum ? propSchema.enum : null,\r\n                visibility_rules: null,\r\n                visibility_logic: \"OR\" as \"OR\" | \"AND\"\r\n              };\r\n\r\n              mcpComponents[\"MCP\"][componentName].inputs.push(inputDef);\r\n            });\r\n          }\r\n\r\n          // Add output definitions based on output schema\r\n          if (tool.output_schema && tool.output_schema.properties) {\r\n            Object.entries(tool.output_schema.properties).forEach(([propName, propSchema]: [string, any]) => {\r\n              const outputDef = {\r\n                name: propName,\r\n                display_name: propSchema.title || propName.replace(/_/g, ' '),\r\n                output_type: mapJsonSchemaTypeToOutputType(propSchema.type)\r\n              };\r\n\r\n              mcpComponents[\"MCP\"][componentName].outputs.push(outputDef);\r\n            });\r\n          }\r\n\r\n          // If no outputs were defined, add a default output\r\n          if (mcpComponents[\"MCP\"][componentName].outputs.length === 0) {\r\n            mcpComponents[\"MCP\"][componentName].outputs.push({\r\n              name: \"result\",\r\n              display_name: \"Result\",\r\n              output_type: \"any\"\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n\r\n    console.log(\"Transformed MCP components:\", mcpComponents);\r\n    return mcpComponents;\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch MCP components:\", error);\r\n    // Return empty object with just the MCP category\r\n    return { \"MCP\": {} };\r\n  }\r\n}\r\n\r\n/**\r\n * Helper function to map JSON Schema types to input types\r\n */\r\nfunction mapJsonSchemaTypeToInputType(schemaType: string, schema: any): string {\r\n  switch (schemaType) {\r\n    case \"string\":\r\n      if (schema.format === \"uri\") return \"url\";\r\n      if (schema.enum) return \"dropdown\";\r\n      return \"string\";\r\n    case \"number\":\r\n    case \"integer\":\r\n      return \"number\";\r\n    case \"boolean\":\r\n      return \"boolean\";\r\n    case \"object\":\r\n      return \"object\"; // Changed from \"json\" to \"object\" for better representation\r\n    case \"array\":\r\n      return \"list\";\r\n    default:\r\n      return \"string\";\r\n  }\r\n}\r\n\r\n/**\r\n * Helper function to map JSON Schema types to output types\r\n */\r\nfunction mapJsonSchemaTypeToOutputType(schemaType: string): string {\r\n  switch (schemaType) {\r\n    case \"string\":\r\n      return \"string\";\r\n    case \"number\":\r\n    case \"integer\":\r\n      return \"number\";\r\n    case \"boolean\":\r\n      return \"boolean\";\r\n    case \"object\":\r\n      return \"object\";\r\n    case \"array\":\r\n      return \"list\";\r\n    default:\r\n      return \"any\";\r\n  }\r\n}\r\n\r\n// Describes the data structure sent TO the backend for execution\r\nexport interface WorkflowExecutionPayload {\r\n  nodes: Node<WorkflowNodeData>[];\r\n  edges: Edge[]; // Changed from connections to edges to match backend expectations\r\n  workflow_id: string; // Optional workflow ID\r\n}\r\n\r\n// Describes the expected data structure received FROM the backend after execution\r\nexport interface ExecutionResult {\r\n  success: boolean;\r\n  results?: Record<string, any>; // Optional: Dictionary mapping node_id to its output data\r\n  error?: string; // Optional: Error message if success is false\r\n  message?: string; // Optional: Success message\r\n  correlation_id?: string; // Optional: Correlation ID for tracking execution\r\n  // You could add more fields here later, like execution logs, timing, etc.\r\n}\r\n\r\n// Describes the data structure sent TO the backend for saving a workflow\r\nexport interface WorkflowSavePayload {\r\n  nodes: Node<WorkflowNodeData>[];\r\n  edges: any[]; // Using any[] for flexibility (previously called connections)\r\n  filename?: string; // Optional filename\r\n  workflow_name?: string; // Optional workflow name\r\n  workflow_id?: string; // Optional workflow ID\r\n}\r\n\r\n// Describes the expected data structure received FROM the backend after saving\r\nexport interface SaveResult {\r\n  success: boolean;\r\n  filepath?: string; // Optional: Path where the file was saved\r\n  workflow_id?: string; // Optional: ID of the saved workflow\r\n  error?: string; // Optional: Error message if success is false\r\n  message?: string; // Optional: Additional information about the save operation\r\n}\r\n\r\n// Describes the data structure sent TO the backend for workflow validation\r\nexport interface WorkflowValidationPayload {\r\n  workflow_data: any; // The workflow data to validate\r\n}\r\n\r\n// Describes the expected data structure received FROM the backend after validation\r\nexport interface ValidationResult {\r\n  is_valid: boolean;\r\n  error?: string; // Optional: Error message if is_valid is false\r\n}\r\n\r\nexport async function executeWorkflow(payload: WorkflowExecutionPayload): Promise<ExecutionResult> {\r\n  // Get the component state store\r\n  const componentState = useComponentStateStore.getState();\r\n\r\n  // Add component state to the nodes\r\n  const nodesWithState = payload.nodes.map((node) => {\r\n    // Create a proper deep copy of the node to avoid string escaping issues\r\n    const nodeCopy = {\r\n      ...node,\r\n      data: {\r\n        ...node.data,\r\n        config: { ...node.data.config },\r\n        definition: node.data.definition ? { ...node.data.definition } : undefined\r\n      }\r\n    };\r\n\r\n    // Get the component state for this node\r\n    const nodeState = componentState.nodes[node.id];\r\n\r\n    // Log the original node data\r\n    console.log(`Original node ${node.id} data:`, {\r\n      config: node.data.config,\r\n      definition: node.data.definition\r\n        ? {\r\n            inputs: node.data.definition.inputs,\r\n          }\r\n        : null,\r\n    });\r\n\r\n    // If there's state for this node, add it to the node data\r\n    if (nodeState) {\r\n      // Add the component state to the node data\r\n      (nodeCopy.data as any).component_state = nodeState;\r\n\r\n      // If there's a config in the component state, add it to the node config\r\n      if (nodeState.config) {\r\n        // Merge the component state config with the node config\r\n        nodeCopy.data.config = {\r\n          ...nodeCopy.data.config,\r\n          ...nodeState.config,\r\n        };\r\n\r\n        // Log the component state config\r\n        console.log(`Component state config for node ${node.id}:`, nodeState.config);\r\n      }\r\n\r\n      // Also add any direct values from the component state to the node config\r\n      for (const [key, value] of Object.entries(nodeState)) {\r\n        if (key !== \"config\" && value !== null && value !== undefined && value !== \"\") {\r\n          nodeCopy.data.config[key] = value;\r\n          console.log(\r\n            `Added direct value from component state for node ${node.id}: ${key}=${value}`,\r\n          );\r\n        }\r\n      }\r\n\r\n      // Log the node with state\r\n      console.log(`Added component state to node ${node.id}:`, nodeState);\r\n      console.log(`Updated node config:`, nodeCopy.data.config);\r\n    } else {\r\n      // If there's no state for this node, check if there are any values in the inputs\r\n      if (nodeCopy.data.definition && nodeCopy.data.definition.inputs) {\r\n        const inputs = nodeCopy.data.definition.inputs;\r\n        for (const input of inputs) {\r\n          if (\r\n            input.value !== null &&\r\n            input.value !== undefined &&\r\n            input.value !== \"\" &&\r\n            (typeof input.value !== \"object\" || Object.keys(input.value).length > 0)\r\n          ) {\r\n            // Add the input value to the node config\r\n            nodeCopy.data.config[input.name] = input.value;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return nodeCopy;\r\n  });\r\n\r\n  // Create a new payload with the nodes with state\r\n  const payloadWithState = {\r\n    nodes: nodesWithState,\r\n    edges: payload.edges,\r\n  };\r\n\r\n  // Transform the payload for the external orchestration service\r\n  // This will properly format MCP Marketplace components\r\n  const { nodes, edges } = transformWorkflowForOrchestration(\r\n    payloadWithState.nodes,\r\n    payloadWithState.edges,\r\n  );\r\n\r\n  // Post-process the transformed nodes to ensure API request method is preserved\r\n  const processedNodes = nodes.map((node) => {\r\n    // Check if this is an API request node\r\n    if (node.data.originalType === \"ApiRequestNode\" || node.data.type === \"ApiRequestNode\") {\r\n      console.log(`Processing API request node ${node.id}`);\r\n\r\n      // Ensure the method is preserved from the original node\r\n      const originalNode = payloadWithState.nodes.find((n) => n.id === node.id);\r\n      if (originalNode && originalNode.data.config && originalNode.data.config.method) {\r\n        console.log(\r\n          `Preserving method ${originalNode.data.config.method} for API request node ${node.id}`,\r\n        );\r\n        if (node.data.config) {\r\n          node.data.config.method = originalNode.data.config.method;\r\n        }\r\n      }\r\n    }\r\n    return node;\r\n  });\r\n\r\n  // Create the transformed payload without mcp_configs\r\n  const transformedPayload = {\r\n    nodes: processedNodes,\r\n    edges,\r\n    workflow_id: payload.workflow_id, // Include the workflow_id from the original payload\r\n  };\r\n\r\n  // Log the original payload\r\n  console.log(`Original execution payload:`, payload);\r\n\r\n  // Log the payload with state\r\n  console.log(`Payload with state:`, payloadWithState);\r\n\r\n  // Log the transformed payload\r\n  console.log(`Transformed payload:`, transformedPayload);\r\n\r\n  // Save the payloads to localStorage for debugging\r\n  try {\r\n    localStorage.setItem(\"original_payload\", JSON.stringify(payload));\r\n    localStorage.setItem(\"payload_with_state\", JSON.stringify(payloadWithState));\r\n    localStorage.setItem(\"transformed_payload\", JSON.stringify(transformedPayload));\r\n    console.log(\"Saved payloads to localStorage for debugging\");\r\n  } catch (error) {\r\n    console.error(\"Error saving payloads to localStorage:\", error);\r\n  }\r\n\r\n  // Use the backend API for execution, which will forward to the external service\r\n  const endpoint = `${BACKEND_API_URL}/execute`; // Backend API endpoint\r\n\r\n  console.log(`Sending execution request to ${endpoint} with payload:`, transformedPayload);\r\n\r\n  try {\r\n    // Get the access token based on environment\r\n    let accessToken;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      accessToken = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      accessToken = await getAccessToken();\r\n    }\r\n\r\n    // Prepare headers with authentication\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    // Add Authorization header if we have a token\r\n    if (accessToken) {\r\n      headers[\"Authorization\"] = `Bearer ${accessToken}`;\r\n    }\r\n\r\n    const response = await fetch(endpoint, {\r\n      method: \"POST\",\r\n      headers: headers,\r\n      body: JSON.stringify(transformedPayload), // Convert the JavaScript object to a JSON string\r\n    });\r\n\r\n    // Check if the request was successful (status code 2xx)\r\n    if (!response.ok) {\r\n      let errorDetail = \"Unknown execution error\";\r\n      try {\r\n        // Try to parse more detailed error from backend response body\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        // If parsing fails, use the status text\r\n        errorDetail = response.statusText;\r\n      }\r\n      // Throw an error to be caught by the catch block below\r\n      throw new Error(`Execution failed: ${response.status} ${errorDetail}`);\r\n    }\r\n\r\n    // If successful, parse the JSON response from the backend\r\n    const resultData = await response.json();\r\n\r\n    // Ensure the result matches the expected structure (basic check)\r\n    if (typeof resultData.success !== \"boolean\") {\r\n      throw new Error(\"Invalid response format received from backend.\");\r\n    }\r\n\r\n    console.log(\"Received execution result:\", resultData);\r\n    return resultData as ExecutionResult; // Type assertion\r\n  } catch (error) {\r\n    console.error(\"Error executing workflow via API:\", error);\r\n    // Return a standardized error format consistent with ExecutionResult\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error),\r\n    };\r\n  }\r\n}\r\n\r\nexport async function saveWorkflowToServer(payload: WorkflowSavePayload): Promise<SaveResult> {\r\n  // Determine if this is a new workflow or an existing one\r\n  const isExistingWorkflow = !!payload.workflow_id;\r\n\r\n  // Use the appropriate endpoint based on whether it's a new or existing workflow\r\n  let endpoint = '';\r\n\r\n  if (isExistingWorkflow && payload.workflow_id) {\r\n    // For existing workflows, use the UPDATE endpoint with the workflow ID in the URL\r\n    endpoint = `${API_ENDPOINTS.WORKFLOWS.UPDATE(payload.workflow_id)}`;\r\n  }\r\n\r\n  // Use appropriate HTTP method based on whether it's a new or existing workflow\r\n  const method = isExistingWorkflow ? \"PATCH\" : \"POST\";\r\n\r\n  console.log(`Sending save workflow request to ${endpoint} with method ${method} and payload:`, payload);\r\n\r\n  // Find the StartNode to update its configuration\r\n  const startNode = payload.nodes.find(node =>\r\n    node.data.originalType === \"StartNode\" ||\r\n    node.id === \"start-node\" ||\r\n    node.data.label === \"Start\"\r\n  );\r\n\r\n  // Check if we have a StartNode\r\n  if (startNode) {\r\n    console.log(`[WORKFLOW SAVE] Found StartNode with ID: ${startNode.id}`);\r\n\r\n    // Ensure the StartNode has a config object\r\n    if (!startNode.data.config) {\r\n      console.log(`[WORKFLOW SAVE] StartNode has no config object, creating one`);\r\n      startNode.data.config = {};\r\n    }\r\n\r\n    // Ensure the config has a collected_parameters object\r\n    if (!startNode.data.config.collected_parameters) {\r\n      console.log(`[WORKFLOW SAVE] StartNode has no collected_parameters object, creating one`);\r\n      startNode.data.config.collected_parameters = {};\r\n    }\r\n\r\n    // Create a fresh collected_parameters object\r\n    if (startNode.data.config) {\r\n      startNode.data.config.collected_parameters = {};\r\n    }\r\n\r\n    console.log(`[WORKFLOW SAVE] Using Run button's graph traversal logic to find all connected nodes`);\r\n\r\n    // Use the same getConnectedNodes function that the Run button uses\r\n    // This performs a breadth-first search to find all nodes reachable from the StartNode\r\n    const connectedNodes = getConnectedNodes(payload.nodes, payload.edges, startNode.id);\r\n    console.log(`[WORKFLOW SAVE] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);\r\n\r\n    // Use the same collectAllFields function that the Run button uses\r\n    // This collects all fields from connected nodes, including required and optional fields\r\n    const allFields = collectAllFields(payload.nodes, connectedNodes, payload.edges);\r\n    console.log(`[WORKFLOW SAVE] Collected ${allFields.length} fields from connected nodes`);\r\n\r\n    // Log detailed field information\r\n    if (allFields.length > 0) {\r\n      console.log(`[WORKFLOW SAVE] Field details:`);\r\n      allFields.forEach((field: MissingField, index: number) => {\r\n        console.log(`[WORKFLOW SAVE]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}, Required: ${field.required ? \"YES\" : \"NO\"}`);\r\n      });\r\n    }\r\n\r\n    // Add only fields that should be included to the StartNode's collected_parameters\r\n    if (allFields.length > 0) {\r\n      console.log(`[WORKFLOW SAVE] Adding fields to StartNode collected_parameters (excluding fields with incoming connections)`);\r\n\r\n      allFields.forEach((field: MissingField) => {\r\n        const fieldId = `${field.nodeId}_${field.name}`;\r\n\r\n        // Check if this field has an incoming connection from another node\r\n        const hasIncomingConnection = field.is_handle === true && field.is_connected === true;\r\n\r\n        // Use the shared utility function to determine if the field should be included\r\n        const isRequired = field.required !== false;\r\n        const isDirectlyConnected = field.directly_connected_to_start === true;\r\n        const hasConfiguredValue = field.currentValue !== undefined;\r\n\r\n        // Log field status for debugging\r\n        console.log(`[WORKFLOW SAVE] Field ${fieldId} status:\r\n          - required: ${isRequired ? \"YES\" : \"NO\"}\r\n          - directly connected to Start: ${isDirectlyConnected ? \"YES\" : \"NO\"}\r\n          - has configured value: ${hasConfiguredValue ? \"YES\" : \"NO\"}\r\n          - has incoming connection: ${hasIncomingConnection ? \"YES\" : \"NO\"}`);\r\n\r\n        // Only include fields that should be included based on our filtering logic\r\n        if (shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection)) {\r\n          if (startNode.data.config && startNode.data.config.collected_parameters) {\r\n            startNode.data.config.collected_parameters[fieldId] = {\r\n              node_id: field.nodeId,\r\n              node_name: field.nodeName,\r\n              input_name: field.name,\r\n              value: field.currentValue,\r\n              connected_to_start: true,\r\n              required: field.required,\r\n              input_type: field.inputType,\r\n              options: field.options\r\n            };\r\n\r\n            console.log(`[WORKFLOW SAVE] Added field ${fieldId} to StartNode collected_parameters`);\r\n          }\r\n        } else {\r\n          console.log(`[WORKFLOW SAVE] Skipping field ${fieldId} - excluded by filtering logic`);\r\n        }\r\n      });\r\n\r\n      console.log(`[WORKFLOW SAVE] Finished adding fields to StartNode collected_parameters`);\r\n    } else {\r\n      console.log(`[WORKFLOW SAVE] No fields found to add to StartNode collected_parameters`);\r\n    }\r\n\r\n    // Log the final StartNode configuration\r\n    console.log(`[WORKFLOW SAVE] StartNode complete data:`, startNode.data);\r\n    console.log(`[WORKFLOW SAVE] StartNode config:`, startNode.data.config);\r\n    console.log(`[WORKFLOW SAVE] StartNode collected_parameters:`, startNode.data.config.collected_parameters);\r\n  } else {\r\n    console.log(`[WORKFLOW SAVE] No StartNode found in the workflow`);\r\n  }\r\n\r\n  // Extract the start node data\r\n  const start_node_data = extractStartNodeData(payload.nodes, payload.edges);\r\n  console.log(`[WORKFLOW SAVE] Extracted start_node_data:`, start_node_data);\r\n\r\n  // Initialize variables for filtered nodes and edges\r\n  let filteredNodes = payload.nodes;\r\n  let filteredEdges = payload.edges;\r\n\r\n  // Filter out nodes that are not connected to the StartNode\r\n  if (startNode) {\r\n    console.log(`[WORKFLOW SAVE] Using Run button's graph traversal logic to find all connected nodes`);\r\n\r\n    // Use the getConnectedNodes function to find all nodes reachable from the StartNode\r\n    const connectedNodes = getConnectedNodes(payload.nodes, payload.edges, startNode.id);\r\n    console.log(`[WORKFLOW SAVE] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);\r\n\r\n    // Check if there are any disconnected nodes\r\n    const disconnectedNodesCount = payload.nodes.length - connectedNodes.size;\r\n    if (disconnectedNodesCount > 0) {\r\n      console.log(`[WORKFLOW SAVE] Found ${disconnectedNodesCount} disconnected nodes that will be excluded from the saved workflow`);\r\n\r\n      // Filter nodes to include only those connected to the StartNode\r\n      filteredNodes = payload.nodes.filter(node => connectedNodes.has(node.id));\r\n\r\n      // Filter edges to include only those connecting filtered nodes\r\n      filteredEdges = payload.edges.filter(edge =>\r\n        connectedNodes.has(edge.source) && connectedNodes.has(edge.target)\r\n      );\r\n\r\n      console.log(`[WORKFLOW SAVE] Filtered workflow contains ${filteredNodes.length} nodes and ${filteredEdges.length} edges`);\r\n\r\n      // Import toast dynamically to avoid server-side rendering issues\r\n      try {\r\n        // Use dynamic import for toast\r\n        const { toast } = await import(\"sonner\");\r\n\r\n        // Show a warning toast notification\r\n        toast.warning(\r\n          `${disconnectedNodesCount} unconnected ${disconnectedNodesCount === 1 ? 'node has' : 'nodes have'} been excluded from the saved workflow.`,\r\n          {\r\n            description: \"Only nodes connected to the Start node are included in the workflow.\",\r\n            duration: 5000,\r\n          }\r\n        );\r\n      } catch (error) {\r\n        console.error(\"Failed to show toast notification:\", error);\r\n      }\r\n    }\r\n  } else {\r\n    // If no StartNode is found, use the original payload\r\n    console.warn(`[WORKFLOW SAVE] No StartNode found, using original payload`);\r\n  }\r\n\r\n  // Create the request payload with filtered nodes and edges\r\n  const requestPayload = {\r\n    name: payload.filename,\r\n    description: payload.filename,\r\n    workflow_data: {\r\n      nodes: filteredNodes,\r\n      edges: filteredEdges\r\n    },\r\n    start_node_data: start_node_data\r\n  };\r\n\r\n  try {\r\n    // Get the access token based on environment\r\n    let accessToken;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      accessToken = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      accessToken = await getAccessToken();\r\n    }\r\n\r\n    // Prepare headers with authentication\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    // Add Authorization header if we have a token\r\n    if (accessToken) {\r\n      headers[\"Authorization\"] = `Bearer ${accessToken}`;\r\n    }\r\n\r\n    // Log the final request payload with StartNode config\r\n  console.log(`[WORKFLOW SAVE] Final request payload:`, requestPayload);\r\n  console.log(`[WORKFLOW SAVE] Final request payload JSON:`, JSON.stringify(requestPayload, null, 2));\r\n\r\n  const response = await fetch(endpoint, {\r\n      method: method,\r\n      headers: headers,\r\n      body: JSON.stringify(requestPayload),\r\n    });\r\n\r\n    // Check if the request was successful (status code 2xx)\r\n    if (!response.ok) {\r\n      let errorDetail = \"Unknown save error\";\r\n      try {\r\n        // Try to parse more detailed error from backend response body\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        // If parsing fails, use the status text\r\n        errorDetail = response.statusText;\r\n      }\r\n\r\n      // Handle specific error codes\r\n      if (response.status === 400) {\r\n        errorDetail = \"Invalid workflow data. Please check your workflow configuration.\";\r\n      } else if (response.status === 403) {\r\n        errorDetail = \"You don't have permission to save this workflow.\";\r\n      } else if (response.status === 404) {\r\n        errorDetail = \"Workflow not found. It may have been deleted.\";\r\n      } else if (response.status === 500) {\r\n        errorDetail = \"Server error occurred while saving the workflow. Please try again later.\";\r\n      }\r\n\r\n      // Throw an error to be caught by the catch block below\r\n      throw new Error(`Save failed: ${response.status} ${errorDetail}`);\r\n    }\r\n\r\n    // If successful, parse the JSON response from the backend\r\n    const resultData = await response.json();\r\n\r\n    // Handle different response formats from the new endpoint\r\n    let saveResult: SaveResult;\r\n\r\n    // The new endpoint returns a workflow object directly\r\n    if (resultData.workflow) {\r\n      // New endpoint format\r\n      saveResult = {\r\n        success: true,\r\n        workflow_id: resultData.workflow.id,\r\n        message: \"Workflow saved successfully\",\r\n        error: undefined\r\n      };\r\n    }\r\n    // Fallback for other response formats\r\n    else if (typeof resultData.success === \"boolean\") {\r\n      // Old format with success property\r\n      saveResult = {\r\n        success: resultData.success,\r\n        workflow_id: resultData.workflow_id,\r\n        message: resultData.message || \"Workflow saved successfully\",\r\n        error: resultData.error\r\n      };\r\n    }\r\n    else {\r\n      throw new Error(\"Invalid response format received from backend.\");\r\n    }\r\n\r\n    console.log(\"Received save result:\", saveResult);\r\n    return saveResult;\r\n  } catch (error) {\r\n    console.error(\"Error saving workflow via API:\", error);\r\n    // Return a standardized error format consistent with SaveResult\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error),\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Processes a JSON object into a structured representation with proper nested properties\r\n *\r\n * @param jsonData The JSON object to process\r\n * @param parentKey The parent key for nested properties\r\n * @param parentTransitionId The transition ID to use for all properties\r\n * @returns Processed object with proper structure\r\n */\r\nexport function processJsonObject(\r\n  jsonData: any,\r\n  parentKey: string = '',\r\n  parentTransitionId: string = ''\r\n): {\r\n  field: string;\r\n  type: string;\r\n  transition_id: string;\r\n  properties?: Array<{\r\n    field: string;\r\n    type: string;\r\n    transition_id: string;\r\n    properties?: any[];\r\n  }>;\r\n  enum?: string[];\r\n} {\r\n  // Handle null or undefined\r\n  if (jsonData === null || jsonData === undefined) {\r\n    return {\r\n      field: parentKey,\r\n      type: 'string',\r\n      transition_id: parentTransitionId\r\n    };\r\n  }\r\n\r\n  // If it's an object (but not an array), process it as an object with properties\r\n  if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {\r\n    const properties: Array<{\r\n      field: string;\r\n      type: string;\r\n      transition_id: string;\r\n      properties?: any[];\r\n    }> = [];\r\n\r\n    // Process each property of the object\r\n    Object.entries(jsonData).forEach(([key, value]) => {\r\n      if (value === null || value === undefined) {\r\n        // Handle null values as simple string properties\r\n        properties.push({\r\n          field: key,\r\n          type: 'string',\r\n          transition_id: parentTransitionId\r\n        });\r\n      } else if (typeof value === 'object' && !Array.isArray(value)) {\r\n        // Recursively process nested objects\r\n        const nestedObject = processJsonObject(value, key, parentTransitionId);\r\n        properties.push(nestedObject);\r\n      } else if (Array.isArray(value)) {\r\n        // Handle arrays\r\n        if (value.length > 0 && typeof value[0] === 'object') {\r\n          // If array contains objects, process the first one as an example\r\n          const nestedObject = processJsonObject(value[0], key, parentTransitionId);\r\n          properties.push({\r\n            field: key,\r\n            type: 'array',\r\n            transition_id: parentTransitionId,\r\n            properties: [nestedObject]\r\n          });\r\n        } else {\r\n          // Simple array\r\n          properties.push({\r\n            field: key,\r\n            type: 'array',\r\n            transition_id: parentTransitionId\r\n          });\r\n        }\r\n      } else {\r\n        // Handle primitive types\r\n        let type: string = typeof value;\r\n        if (type === 'number') {\r\n          // Check if it's an integer or float\r\n          type = Number.isInteger(value) ? 'integer' : 'number';\r\n        }\r\n\r\n        properties.push({\r\n          field: key,\r\n          type: type,\r\n          transition_id: parentTransitionId\r\n        });\r\n      }\r\n    });\r\n\r\n    // Return the object with its properties\r\n    return {\r\n      field: parentKey,\r\n      type: 'object',\r\n      transition_id: parentTransitionId,\r\n      properties: properties\r\n    };\r\n  } else if (Array.isArray(jsonData)) {\r\n    // It's an array\r\n    if (jsonData.length > 0 && typeof jsonData[0] === 'object') {\r\n      // If array contains objects, process the first one as an example\r\n      const nestedObject = processJsonObject(jsonData[0], `${parentKey}Item`, parentTransitionId);\r\n      return {\r\n        field: parentKey,\r\n        type: 'array',\r\n        transition_id: parentTransitionId,\r\n        properties: [nestedObject]\r\n      };\r\n    } else {\r\n      // Simple array\r\n      return {\r\n        field: parentKey,\r\n        type: 'array',\r\n        transition_id: parentTransitionId\r\n      };\r\n    }\r\n  } else {\r\n    // It's a primitive type\r\n    let type: string = typeof jsonData;\r\n    if (type === 'number') {\r\n      // Check if it's an integer or float\r\n      type = Number.isInteger(jsonData) ? 'integer' : 'number';\r\n    }\r\n\r\n    return {\r\n      field: parentKey,\r\n      type: type,\r\n      transition_id: parentTransitionId\r\n    };\r\n  }\r\n}\r\n\r\n// The flattenJson function has been replaced by processJsonObject\r\n\r\n/**\r\n * Checks if a handle input is connected to another node\r\n *\r\n * @param nodeId The ID of the node containing the handle\r\n * @param inputName The name of the handle input\r\n * @param edges The array of edges in the workflow\r\n * @returns True if the handle is connected to another node\r\n */\r\nfunction isConnected(nodeId: string, inputName: string, edges: Edge[]): boolean {\r\n  // For target handles, the connection would be to a target handle with the input name\r\n  const isConnected = edges.some(edge =>\r\n    edge.target === nodeId && edge.targetHandle === inputName\r\n  );\r\n\r\n  // Log all edges for debugging\r\n  console.log(`[EXTRACT_START_NODE] All edges (${edges.length}):`);\r\n  edges.forEach((edge, index) => {\r\n    console.log(`[EXTRACT_START_NODE] Edge ${index + 1}: source=${edge.source}, sourceHandle=${edge.sourceHandle}, target=${edge.target}, targetHandle=${edge.targetHandle}`);\r\n  });\r\n\r\n  // Log the specific check we're making\r\n  console.log(`[EXTRACT_START_NODE] Checking if handle ${nodeId}.${inputName} is connected: ${isConnected ? \"YES\" : \"NO\"}`);\r\n\r\n  // If connected, log which edge(s) connect to this handle\r\n  if (isConnected) {\r\n    const connectingEdges = edges.filter(edge => edge.target === nodeId && edge.targetHandle === inputName);\r\n    connectingEdges.forEach((edge, index) => {\r\n      console.log(`[EXTRACT_START_NODE] Found connecting edge ${index + 1}: source=${edge.source}, sourceHandle=${edge.sourceHandle}, target=${edge.target}, targetHandle=${edge.targetHandle}`);\r\n    });\r\n  }\r\n\r\n  return isConnected;\r\n}\r\n\r\n/**\r\n * Extracts required input fields from the start node\r\n * @param nodes The workflow nodes\r\n * @param edges The array of edges in the workflow\r\n * @returns Array of start node data objects\r\n */\r\nfunction extractStartNodeData(nodes: Node<WorkflowNodeData>[], edges: Edge[] = []): Array<{\r\n  field: string;\r\n  type: string;\r\n  enum?: string[];\r\n  transition_id: string;\r\n  properties?: Array<{\r\n    field: string;\r\n    type: string;\r\n    transition_id: string;\r\n    properties?: any[];\r\n  }>;\r\n}> {\r\n  console.log(`[EXTRACT_START_NODE] Starting extraction from ${nodes.length} nodes`);\r\n\r\n  // Find the start node\r\n  const startNode = nodes.find(node =>\r\n    node.data.originalType === \"StartNode\" ||\r\n    node.id === \"start-node\" ||\r\n    node.data.label === \"Start\"\r\n  );\r\n\r\n  if (!startNode) {\r\n    console.log(`[EXTRACT_START_NODE] No StartNode found in the workflow`);\r\n    return [];\r\n  }\r\n\r\n  console.log(`[EXTRACT_START_NODE] Found StartNode with ID: ${startNode.id}`);\r\n\r\n  if (!startNode.data.config) {\r\n    console.log(`[EXTRACT_START_NODE] StartNode has no config object`);\r\n    return [];\r\n  }\r\n\r\n  if (!startNode.data.config.collected_parameters) {\r\n    console.log(`[EXTRACT_START_NODE] StartNode has no collected_parameters object`);\r\n    return [];\r\n  }\r\n\r\n  console.log(`[EXTRACT_START_NODE] StartNode config:`, startNode.data.config);\r\n  console.log(`[EXTRACT_START_NODE] StartNode collected_parameters:`, startNode.data.config.collected_parameters);\r\n\r\n  const result: Array<{\r\n    field: string;\r\n    type: string;\r\n    enum?: string[];\r\n    transition_id: string;\r\n    properties?: Array<{\r\n      field: string;\r\n      type: string;\r\n      transition_id: string;\r\n      properties?: any[];\r\n    }>;\r\n  }> = [];\r\n\r\n  // Extract parameters from the start node\r\n  const params = startNode.data.config.collected_parameters;\r\n\r\n  Object.entries(params).forEach(([fieldId, paramData]: [string, any]) => {\r\n    console.log(`[EXTRACT_START_NODE] Processing field: ${fieldId}`, paramData);\r\n\r\n    // Extract node_id and field_name from the fieldId\r\n    const nodeId = paramData.node_id;\r\n    const fieldName = paramData.input_name;\r\n\r\n    console.log(`[EXTRACT_START_NODE] Extracted nodeId: ${nodeId}, fieldName: ${fieldName}`);\r\n\r\n    // Check if this field is required (consider it required unless explicitly marked as optional)\r\n    const isRequired = paramData.required !== false;\r\n\r\n    // Check if this field is directly connected to the Start node\r\n    const isDirectlyConnected = paramData.connected_to_start === true;\r\n\r\n    // Find the node in the current workflow\r\n    const node = nodes.find(node => node.id === nodeId);\r\n\r\n    // Check if the node has a config with a value for this field\r\n    const hasConfiguredValue = node?.data?.config && node.data.config[fieldName] !== undefined;\r\n\r\n    // Check if this field has an incoming connection from another node\r\n    const hasIncomingConnection = isConnected(nodeId, fieldName, edges);\r\n\r\n    // Use the shared utility function to log field status\r\n    logFieldStatus(\r\n      \"ExtractStartNodeData\",\r\n      fieldId,\r\n      node?.data?.label || \"Unknown Node\",\r\n      fieldName,\r\n      isRequired,\r\n      paramData.required,\r\n      isDirectlyConnected || false,\r\n      hasConfiguredValue || false,\r\n      hasConfiguredValue && node?.data?.config ? node.data.config[fieldName] : undefined,\r\n      hasIncomingConnection\r\n    );\r\n\r\n    // Use the shared utility function to determine if the field should be included\r\n    if (shouldIncludeField(isRequired, isDirectlyConnected || false, hasConfiguredValue || false, hasIncomingConnection)) {\r\n      // Determine the data type\r\n      let dataType = paramData.input_type || \"string\";\r\n      let enumValues: string[] | undefined = undefined;\r\n\r\n      if (paramData.input_type) {\r\n        dataType = paramData.input_type;\r\n        console.log(`[EXTRACT_START_NODE] Found dataType in paramData.input_type: ${dataType}`);\r\n      } else {\r\n        console.log(`[EXTRACT_START_NODE] No input_type found, using default: ${dataType}`);\r\n      }\r\n\r\n      if ((dataType === \"json\" || dataType === \"object\") && paramData.value) {\r\n        try {\r\n          // If value is already an object, use it directly\r\n          const jsonData = typeof paramData.value === 'object' ?\r\n            paramData.value :\r\n            JSON.parse(paramData.value);\r\n\r\n          console.log(`[EXTRACT_START_NODE] Processing ${dataType} type data`);\r\n\r\n          // Process the JSON object with proper structure\r\n          const processedObject = processJsonObject(jsonData, fieldName, nodeId);\r\n          console.log(`[EXTRACT_START_NODE] Processed object structure:`, processedObject);\r\n\r\n          // Add the processed object to the result\r\n          result.push(processedObject);\r\n\r\n          // Skip adding the original JSON field since we've added the processed version\r\n          return;\r\n        } catch (error) {\r\n          console.log(`[EXTRACT_START_NODE] Error processing ${dataType}: ${error}. Using as regular string.`);\r\n          // Fall back to treating it as a string\r\n          dataType = \"string\";\r\n        }\r\n      }\r\n\r\n      // Check for enum values if dataType is enum\r\n      if (dataType === \"enum\") {\r\n        if (Array.isArray(paramData.options)) {\r\n          enumValues = paramData.options;\r\n          console.log(`[EXTRACT_START_NODE] Found enum values in paramData.options`);\r\n        }\r\n      }\r\n\r\n      console.log(`[EXTRACT_START_NODE] Determined dataType: ${dataType}`);\r\n\r\n      // For regular fields, create a simple field object\r\n      result.push({\r\n        field: fieldName,\r\n        type: dataType,\r\n        ...(enumValues && { enum: enumValues }),\r\n        transition_id: nodeId\r\n      });\r\n    } else {\r\n      console.log(`[EXTRACT_START_NODE] Skipping field ${fieldId} (not required, has configured value, or not directly connected)`);\r\n    }\r\n  });\r\n\r\n  console.log(`[EXTRACT_START_NODE] Final extracted result:`, result);\r\n  return result;\r\n}\r\n\r\nexport async function validateWorkflow(\r\n  payload: WorkflowValidationPayload,\r\n): Promise<ValidationResult> {\r\n  const endpoint = `${BACKEND_API_URL}/validate_workflow`; // Endpoint for validation\r\n\r\n  console.log(`Sending validation request to ${endpoint} with payload:`, payload);\r\n\r\n  try {\r\n    // Get the access token based on environment\r\n    let accessToken;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      accessToken = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      accessToken = await getAccessToken();\r\n    }\r\n\r\n    // Prepare headers with authentication\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    // Add Authorization header if we have a token\r\n    if (accessToken) {\r\n      headers[\"Authorization\"] = `Bearer ${accessToken}`;\r\n    }\r\n\r\n    const response = await fetch(endpoint, {\r\n      method: \"POST\",\r\n      headers: headers,\r\n      body: JSON.stringify(payload),\r\n    });\r\n\r\n    // Check if the request was successful (status code 2xx)\r\n    if (!response.ok) {\r\n      let errorDetail = \"Unknown validation error\";\r\n      try {\r\n        // Try to parse more detailed error from backend response body\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        // If parsing fails, use the status text\r\n        errorDetail = response.statusText;\r\n      }\r\n      // Throw an error to be caught by the catch block below\r\n      throw new Error(`Validation failed: ${response.status} ${errorDetail}`);\r\n    }\r\n\r\n    // If successful, parse the JSON response from the backend\r\n    const resultData = await response.json();\r\n\r\n    // Ensure the result matches the expected structure (basic check)\r\n    if (typeof resultData.is_valid !== \"boolean\") {\r\n      throw new Error(\"Invalid response format received from backend.\");\r\n    }\r\n\r\n    console.log(\"Received validation result:\", resultData);\r\n    return resultData as ValidationResult; // Type assertion\r\n  } catch (error) {\r\n    console.error(\"Error validating workflow via API:\", error);\r\n    // Return a standardized error format consistent with ValidationResult\r\n    return {\r\n      is_valid: false,\r\n      error: error instanceof Error ? error.message : String(error),\r\n    };\r\n  }\r\n}\r\n\r\n// Credential Management Types and Functions\r\nexport interface Credential {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n}\r\n\r\nexport interface CredentialCreate {\r\n  name: string;\r\n  type: string;\r\n  value: string;\r\n}\r\n\r\nexport interface CredentialListResponse {\r\n  credentials: Credential[];\r\n}\r\n\r\nexport interface CredentialDeleteResponse {\r\n  success: boolean;\r\n  message?: string;\r\n}\r\n\r\n// Import mock implementations\r\nimport {\r\n  mockFetchCredentials,\r\n  mockCreateCredential,\r\n  mockDeleteCredential,\r\n} from \"./mock-credential-service\";\r\n\r\n// Fetch all credentials\r\nexport async function fetchCredentials(): Promise<CredentialListResponse> {\r\n  // Use mock implementation for now\r\n  return mockFetchCredentials();\r\n\r\n  // The code below is kept for future implementation\r\n  /*\r\n  const endpoint = `${API_BASE_URL}/credentials`;\r\n  console.log(`Fetching credentials from ${endpoint}`);\r\n\r\n  try {\r\n    const response = await fetch(endpoint);\r\n\r\n    if (!response.ok) {\r\n      let errorDetail = 'Unknown error';\r\n      try {\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        errorDetail = response.statusText;\r\n      }\r\n      throw new Error(`Failed to fetch credentials: ${errorDetail}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(\"Received credentials:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching credentials:\", error);\r\n    throw error; // Re-throw to let the component handle the error\r\n  }\r\n  */\r\n}\r\n\r\n// Create a new credential\r\nexport async function createCredential(credential: CredentialCreate): Promise<Credential> {\r\n  // Use mock implementation for now\r\n  return mockCreateCredential(credential);\r\n\r\n  // The code below is kept for future implementation\r\n  /*\r\n  const endpoint = `${API_BASE_URL}/credentials`;\r\n  console.log(`Creating credential at ${endpoint}`);\r\n\r\n  try {\r\n    const response = await fetch(endpoint, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(credential),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      let errorDetail = 'Unknown error';\r\n      try {\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        errorDetail = response.statusText;\r\n      }\r\n      throw new Error(`Failed to create credential: ${errorDetail}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(\"Created credential:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating credential:\", error);\r\n    throw error; // Re-throw to let the component handle the error\r\n  }\r\n  */\r\n}\r\n\r\n// Delete a credential\r\nexport async function deleteCredential(credentialId: string): Promise<CredentialDeleteResponse> {\r\n  // Use mock implementation for now\r\n  return mockDeleteCredential(credentialId);\r\n\r\n  // The code below is kept for future implementation\r\n  /*\r\n  const endpoint = `${API_BASE_URL}/credentials/${credentialId}`;\r\n  console.log(`Deleting credential at ${endpoint}`);\r\n\r\n  try {\r\n    const response = await fetch(endpoint, {\r\n      method: 'DELETE',\r\n    });\r\n\r\n    if (!response.ok) {\r\n      let errorDetail = 'Unknown error';\r\n      try {\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        errorDetail = response.statusText;\r\n      }\r\n      throw new Error(`Failed to delete credential: ${errorDetail}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(\"Deleted credential:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting credential:\", error);\r\n    throw error; // Re-throw to let the component handle the error\r\n  }\r\n  */\r\n}\r\n\r\n// Workflow Execution with User Inputs\r\nexport interface WorkflowExecuteWithUserInputsPayload {\r\n  user_id:string;\r\n  workflow_id: string;\r\n  approval: boolean; // Changed from string to boolean for proper type safety\r\n  payload: {\r\n    user_dependent_fields: string[];\r\n    user_payload_template: Record<string, any>;\r\n  };\r\n}\r\n\r\nexport interface WorkflowExecuteResponse {\r\n  correlationId?: string;\r\n  success: boolean;\r\n  message?: string;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Executes a workflow with user-provided inputs\r\n *\r\n * @param payload The execution payload containing workflow_id, approval status, and user inputs\r\n * @returns A promise that resolves to a WorkflowExecuteResponse\r\n */\r\nexport async function executeWorkflowWithUserInputs(\r\n  payload: WorkflowExecuteWithUserInputsPayload\r\n): Promise<WorkflowExecuteResponse> {\r\n  try {\r\n    console.log(`Executing workflow with user inputs:`, payload);\r\n\r\n    // Get the access token based on environment\r\n    let accessToken;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      accessToken = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      accessToken = await getAccessToken();\r\n    }\r\n\r\n    // Prepare headers with authentication\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    // Add Authorization header if we have a token\r\n    if (accessToken) {\r\n      headers[\"Authorization\"] = `Bearer ${accessToken}`;\r\n    }\r\n\r\n    // Send the request to the execution endpoint\r\n    const response = await fetch(\r\n      API_ENDPOINTS.WORKFLOW_EXECUTION.EXECUTE,\r\n      {\r\n        method: \"POST\",\r\n        headers: headers,\r\n        body: JSON.stringify(payload),\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      let errorDetail = \"Unknown execution error\";\r\n      try {\r\n        // Try to parse more detailed error from response body\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        // If parsing fails, use the status text\r\n        errorDetail = response.statusText;\r\n      }\r\n      throw new Error(`Execution failed: ${response.status} ${errorDetail}`);\r\n    }\r\n\r\n    const resultData = await response.json();\r\n    console.log(\"Received execution result:\", resultData);\r\n\r\n    return {\r\n      success: true,\r\n      correlationId: resultData.correlationId,\r\n      message: resultData.message || \"Workflow execution started successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error executing workflow with user inputs:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error),\r\n    };\r\n  }\r\n}\r\n\r\n// Workflow Approval Types and Functions\r\nexport interface ApprovalResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Sends an approval decision for a workflow that is waiting for approval.\r\n *\r\n * @param correlationId The correlation ID of the workflow execution\r\n * @param decision The approval decision ('approve' or 'reject')\r\n * @returns A promise that resolves to an ApprovalResponse\r\n */\r\nexport async function sendApprovalDecision(\r\n  correlationId: string,\r\n  decision: \"approve\" | \"reject\",\r\n): Promise<ApprovalResponse> {\r\n  try {\r\n    console.log(`Sending ${decision} decision for correlation ID: ${correlationId}`);\r\n\r\n    // Direct call to the external API, not going through our backend\r\n    const response = await fetch(\r\n      API_ENDPOINTS.WORKFLOW_EXECUTION.APPROVE,\r\n      {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${getClientAccessToken()}`,\r\n        },\r\n        body: JSON.stringify({\r\n          correlationId,\r\n          decision,\r\n        }),\r\n      },\r\n    );\r\n\r\n    if (!response.ok) {\r\n      let errorDetail = \"Unknown approval error\";\r\n      try {\r\n        // Try to parse more detailed error from response body\r\n        const errorData = await response.json();\r\n        errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);\r\n      } catch (parseError) {\r\n        // If parsing fails, use the status text\r\n        errorDetail = response.statusText;\r\n      }\r\n      throw new Error(`Approval failed: ${response.status} ${errorDetail}`);\r\n    }\r\n\r\n    const resultData = await response.json();\r\n    console.log(\"Received approval result:\", resultData);\r\n\r\n    return {\r\n      success: true,\r\n      message: resultData.message || `Workflow ${decision}d successfully`,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error sending approval decision:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;;;;;;;;;;;;;;AAGjB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AA03CA,8BAA8B;AAC9B;;;;;;;;;AAz3CO,MAAM;AACN,MAAM,kBAAkB;AAGxB,eAAe,cAAc,IAAS;IAC3C,IAAI;QACF,+EAA+E;QAC/E,IAAI,KAAK,WAAW,IAAI,CAAC,KAAK,WAAW,CAAC,kBAAkB,IAAI,KAAK,WAAW,CAAC,MAAM,EAAE;YACvF,mDAAmD;YACnD,MAAM,YAAY,KAAK,WAAW,CAAC,MAAM,CAAC,IAAI,CAC5C,CAAC,QAAe,MAAM,IAAI,KAAK;YAEjC,IAAI,aAAa,UAAU,KAAK,EAAE;gBAChC,KAAK,WAAW,CAAC,kBAAkB,GAAG,UAAU,KAAK;gBACrD,QAAQ,GAAG,CACT,CAAC,2CAA2C,EAAE,KAAK,WAAW,CAAC,kBAAkB,EAAE;YAEvF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE;QAE1D,MAAM,WAAW,MAAM,MAAM,GAAG,gBAAgB,gBAAgB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAEzC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,SAAS;YAAO,OAAO,OAAO;QAAO;IAChD;AACF;AAGO,eAAe,cAAc,UAAe,EAAE,UAAkB;IACrE,IAAI;QACF,mFAAmF;QACnF,MAAM,cAAmB;YACvB,kDAAkD;YAClD,MAAM,WAAW,IAAI,IAAI;YACzB,SAAS,WAAW,OAAO,IAAI;YAC/B,SAAS,WAAW,OAAO,IAAI;YAC/B,oBAAoB,WAAW,kBAAkB,IAAI;YACrD,mBAAmB,WAAW,iBAAiB,IAAI;QACrD;QAEA,4CAA4C;QAC5C,MAAM,eAAe,YAAY,IAAI;QACrC,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,cAAc;QAE7D,mDAAmD;QACnD,IAAI,eAAe,mBAAmB;YACpC,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,YAAY,OAAO,EAAE;YAEzD,oCAAoC;YACpC,YAAY,IAAI,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC;YAE3D,iDAAiD;YACjD,IAAI,CAAC,YAAY,OAAO,EAAE;gBACxB,QAAQ,IAAI,CAAC;YACf;QACF,OAAO,IAAI,eAAe,qBAAqB;YAC7C,sCAAsC;YACtC,YAAY,IAAI,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;QACjE,OAAO,IAAI,eAAe,sBAAsB;YAC9C,sEAAsE;YACtE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY;YAClD,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,YAAY,IAAI,EAAE;YAC1D,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,YAAY,iBAAiB,EAAE;YAE5E,qEAAqE;YACrE,IAAI,YAAY,iBAAiB,KAAK,aAAa;gBACjD,QAAQ,GAAG,CAAC,CAAC,yDAAyD,CAAC;gBACvE,YAAY,iBAAiB,GAAG;YAClC;QACF;QACA,0CAA0C;QAE1C,uCAAuC;QACvC,uCAAoC;;QAEpC;QAEA,2DAA2D;QAC3D,MAAM,UAAe;YACnB,QAAQ;QACV;QAEA,gDAAgD;QAChD,IAAI,eAAe,sBAAsB;YACvC,QAAQ,kBAAkB,GAAG,YAAY,kBAAkB;YAC3D,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,YAAY,kBAAkB,EAAE;YAEtF,kDAAkD;YAClD,IAAI,YAAY,iBAAiB,KAAK,aAAa;gBACjD,QAAQ,GAAG,CAAC,CAAC,0DAA0D,CAAC;gBACxE,YAAY,iBAAiB,GAAG;gBAChC,QAAQ,MAAM,CAAC,iBAAiB,GAAG;YACrC;QACF,OAAO;YACL,OAAO,CAAC,WAAW,GAAG,MAAM,+BAA+B;QAC7D;QAEA,yEAAyE;QACzE,4FAA4F;QAC5F,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,cAAc;QACzD,QAAQ,MAAM,CAAC,IAAI,GAAG;QAEtB,kEAAkE;QAClE,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,YAAY,kBAAkB,EAAE;QAC/E,IAAI,CAAC,YAAY,kBAAkB,IAAI,YAAY,MAAM,EAAE;YACzD,mDAAmD;YACnD,MAAM,YAAY,YAAY,MAAM,CAAC,IAAI,CACvC,CAAC,QAAe,MAAM,IAAI,KAAK;YAEjC,IAAI,aAAa,UAAU,KAAK,EAAE;gBAChC,YAAY,kBAAkB,GAAG,UAAU,KAAK;gBAChD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,kBAAkB,EAAE;YACrF;QACF;QAEA,8DAA8D;QAC9D,IAAI,YAAY,kBAAkB,EAAE;YAClC,QAAQ,MAAM,CAAC,kBAAkB,GAAG,YAAY,kBAAkB;YAClE,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,MAAM,CAAC,kBAAkB,EAAE;QACzF;QAEA,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC,EAAE;QAEpE,MAAM,WAAW,MAAM,MAAM,GAAG,gBAAgB,gBAAgB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QACxC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAEzC,uCAAuC;QACvC,MAAM,SAAS,aAAa,MAAM,IAAI;QAEtC,wDAAwD;QACxD,IAAI,UAAU,OAAO,MAAM,EAAE;YAC3B,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAEnE,yCAAyC;YACzC,IAAI,eAAe,mBAAmB;gBACpC,8CAA8C;gBAC9C,OAAO,IAAI,GAAG;gBAEd,wCAAwC;gBACxC,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;oBAChC,MAAM,YAAY,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,QAAe,MAAM,IAAI,KAAK;oBACpE,IAAI,WAAW;wBACb,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC;wBAC7C,UAAU,KAAK,GAAG;oBACpB;gBACF;YACF,OAAO,IAAI,eAAe,qBAAqB;gBAC7C,kDAAkD;gBAClD,OAAO,IAAI,GAAG;gBAEd,wCAAwC;gBACxC,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;oBAChC,MAAM,YAAY,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,QAAe,MAAM,IAAI,KAAK;oBACpE,IAAI,WAAW;wBACb,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC;wBAC/C,UAAU,KAAK,GAAG;oBACpB;gBACF;YACF;YAEA,sCAAsC;YACtC,IAAI,OAAO,eAAe,IAAI,OAAO,eAAe,CAAC,YAAY,EAAE;gBACjE,QAAQ,GAAG,CACT,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,OAAO,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,aAAa,CAAC;YAEnF,oDAAoD;YACtD;QACF;QAEA,OAAO,aAAa,OAAO,GAAG,SAAS;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,SAAS;YAAO,OAAO,OAAO;QAAO;IAChD;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,uCAAuC,CAAC;QACtE,QAAQ,GAAG,CAAC,0DAA0D,SAAS,GAAG;QAClF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxE;QACA,MAAM,OAA8B,MAAM,SAAS,IAAI;QACvD,QAAQ,GAAG,CAAC,uBAAuB,OAAO,gBAAgB;QAC1D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,iFAAiF;QACjF,OAAO,CAAC;IACV;AACF;AAMO,eAAe;IACpB,IAAI;QACF,4CAA4C;QAC5C,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QACnC;QAEA,sCAAsC;QACtC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,8CAA8C;QAC9C,IAAI,aAAa;YACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;QACpD;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;YACzD,QAAQ;YACR,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,6DAA6D,SAAS,GAAG;QAErF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,yGAAyG;QACzG,MAAM,eAAe,MAAM,SAAS,IAAI;QACxC,QAAQ,GAAG,CAAC,yBAAyB,eAAe,gBAAgB;QAEpE,4FAA4F;QAC5F,IAAI,UAAiB,EAAE;QAEvB,IAAI,MAAM,OAAO,CAAC,eAAe;YAC/B,uDAAuD;YACvD,QAAQ,GAAG,CAAC,iCAAiC,aAAa,MAAM,EAAE;YAClE,UAAU;QACZ,OAAO,IAAI,gBAAgB,aAAa,IAAI,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,GAAG;YAChF,iEAAiE;YACjE,QAAQ,GAAG,CAAC,uCAAuC,aAAa,IAAI,CAAC,MAAM,EAAE;YAC7E,UAAU,aAAa,IAAI;QAC7B,OAAO;YACL,wEAAwE;YACxE,QAAQ,KAAK,CAAC;YACd,QAAQ,GAAG,CAAC,sBAAsB,OAAO;YACzC,QAAQ,GAAG,CAAC,2BAA2B,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAEpE,4EAA4E;YAC5E,IAAI,gBAAgB,OAAO,iBAAiB,YAAY,aAAa,IAAI,EAAE;gBACzE,QAAQ,GAAG,CAAC;gBACZ,UAAU,MAAM,OAAO,CAAC,aAAa,IAAI,IAAI,aAAa,IAAI,GAAG;oBAAC,aAAa,IAAI;iBAAC;YACtF,OAAO,IAAI,gBAAgB,OAAO,iBAAiB,UAAU;gBAC3D,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,UAAU;oBAAC;iBAAa;YAC1B,OAAO;gBACL,uDAAuD;gBACvD,QAAQ,KAAK,CAAC;gBACd,OAAO;oBAAE,OAAO,CAAC;gBAAE;YACrB;QACF;QAEA,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,gEAAgE;QAChE,mDAAmD;QACnD,MAAM,gBAAuC;YAC3C,OAAO,CAAC;QACV;QAEA,6BAA6B;QAC7B,QAAQ,OAAO,CAAC,CAAC;YACf,+CAA+C;YAC/C,IAAI,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,CAAC,KAAK,EAAE;gBACtD,IAAI,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAClC,iCAAiC;oBACjC,MAAM,gBAAgB,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ;oBAErE,kCAAkC;oBAClC,aAAa,CAAC,MAAM,CAAC,cAAc,GAAG;wBACpC,MAAM;wBACN,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;wBAC1C,aAAa,KAAK,WAAW,IAAI,IAAI,WAAW;wBAChD,UAAU;wBACV,MAAM;wBACN,MAAM;wBACN,QAAQ,EAAE;wBACV,SAAS,EAAE;wBACX,UAAU;wBACV,MAAM,CAAC,IAAI,EAAE,cAAc,WAAW,IAAI;wBAC1C,MAAM;wBACN,UAAU;4BACR,WAAW,IAAI,EAAE,IAAI;4BACrB,aAAa,IAAI,GAAG,IAAI;4BACxB,WAAW,KAAK,IAAI;4BACpB,cAAc,KAAK,YAAY;4BAC/B,eAAe,KAAK,aAAa;wBACnC;oBACF;oBAEA,4CAA4C;oBAC5C,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,UAAU,EAAE;wBACrD,MAAM,iBAAiB,KAAK,YAAY,CAAC,QAAQ,IAAI,EAAE;wBAEvD,OAAO,OAAO,CAAC,KAAK,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,WAA0B;4BACzF,MAAM,WAAW;gCACf,MAAM;gCACN,cAAc,WAAW,KAAK,IAAI,SAAS,OAAO,CAAC,MAAM;gCACzD,MAAM,WAAW,WAAW,IAAI;gCAChC,YAAY,6BAA6B,WAAW,IAAI,EAAE;gCAC1D,aAAa;gCACb,UAAU,eAAe,QAAQ,CAAC;gCAClC,WAAW;gCACX,SAAS,WAAW,IAAI,KAAK;gCAC7B,mBAAmB;gCACnB,UAAU;gCACV,OAAO,WAAW,OAAO,IAAI;gCAC7B,SAAS,WAAW,IAAI,GAAG,WAAW,IAAI,GAAG;gCAC7C,kBAAkB;gCAClB,kBAAkB;4BACpB;4BAEA,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;wBAClD;oBACF;oBAEA,gDAAgD;oBAChD,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,UAAU,EAAE;wBACvD,OAAO,OAAO,CAAC,KAAK,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,WAA0B;4BAC1F,MAAM,YAAY;gCAChB,MAAM;gCACN,cAAc,WAAW,KAAK,IAAI,SAAS,OAAO,CAAC,MAAM;gCACzD,aAAa,8BAA8B,WAAW,IAAI;4BAC5D;4BAEA,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;wBACnD;oBACF;oBAEA,mDAAmD;oBACnD,IAAI,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;wBAC5D,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;4BAC/C,MAAM;4BACN,cAAc;4BACd,aAAa;wBACf;oBACF;gBACF;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,iDAAiD;QACjD,OAAO;YAAE,OAAO,CAAC;QAAE;IACrB;AACF;AAEA;;CAEC,GACD,SAAS,6BAA6B,UAAkB,EAAE,MAAW;IACnE,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,MAAM,KAAK,OAAO,OAAO;YACpC,IAAI,OAAO,IAAI,EAAE,OAAO;YACxB,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,UAAU,4DAA4D;QAC/E,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA;;CAEC,GACD,SAAS,8BAA8B,UAAkB;IACvD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAgDO,eAAe,gBAAgB,OAAiC;IACrE,gCAAgC;IAChC,MAAM,iBAAiB,6HAAA,CAAA,yBAAsB,CAAC,QAAQ;IAEtD,mCAAmC;IACnC,MAAM,iBAAiB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,wEAAwE;QACxE,MAAM,WAAW;YACf,GAAG,IAAI;YACP,MAAM;gBACJ,GAAG,KAAK,IAAI;gBACZ,QAAQ;oBAAE,GAAG,KAAK,IAAI,CAAC,MAAM;gBAAC;gBAC9B,YAAY,KAAK,IAAI,CAAC,UAAU,GAAG;oBAAE,GAAG,KAAK,IAAI,CAAC,UAAU;gBAAC,IAAI;YACnE;QACF;QAEA,wCAAwC;QACxC,MAAM,YAAY,eAAe,KAAK,CAAC,KAAK,EAAE,CAAC;QAE/C,6BAA6B;QAC7B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE;YAC5C,QAAQ,KAAK,IAAI,CAAC,MAAM;YACxB,YAAY,KAAK,IAAI,CAAC,UAAU,GAC5B;gBACE,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM;YACrC,IACA;QACN;QAEA,0DAA0D;QAC1D,IAAI,WAAW;YACb,2CAA2C;YAC1C,SAAS,IAAI,CAAS,eAAe,GAAG;YAEzC,wEAAwE;YACxE,IAAI,UAAU,MAAM,EAAE;gBACpB,wDAAwD;gBACxD,SAAS,IAAI,CAAC,MAAM,GAAG;oBACrB,GAAG,SAAS,IAAI,CAAC,MAAM;oBACvB,GAAG,UAAU,MAAM;gBACrB;gBAEA,iCAAiC;gBACjC,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;YAC7E;YAEA,yEAAyE;YACzE,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,WAAY;gBACpD,IAAI,QAAQ,YAAY,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;oBAC7E,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;oBAC5B,QAAQ,GAAG,CACT,CAAC,iDAAiD,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,OAAO;gBAElF;YACF;YAEA,0BAA0B;YAC1B,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;YACzD,QAAQ,GAAG,CAAC,CAAC,oBAAoB,CAAC,EAAE,SAAS,IAAI,CAAC,MAAM;QAC1D,OAAO;YACL,iFAAiF;YACjF,IAAI,SAAS,IAAI,CAAC,UAAU,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBAC/D,MAAM,SAAS,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM;gBAC9C,KAAK,MAAM,SAAS,OAAQ;oBAC1B,IACE,MAAM,KAAK,KAAK,QAChB,MAAM,KAAK,KAAK,aAChB,MAAM,KAAK,KAAK,MAChB,CAAC,OAAO,MAAM,KAAK,KAAK,YAAY,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG,CAAC,GACvE;wBACA,yCAAyC;wBACzC,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,KAAK;oBAChD;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,mBAAmB;QACvB,OAAO;QACP,OAAO,QAAQ,KAAK;IACtB;IAEA,+DAA+D;IAC/D,uDAAuD;IACvD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,oCAAiC,AAAD,EACvD,iBAAiB,KAAK,EACtB,iBAAiB,KAAK;IAGxB,+EAA+E;IAC/E,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC;QAChC,uCAAuC;QACvC,IAAI,KAAK,IAAI,CAAC,YAAY,KAAK,oBAAoB,KAAK,IAAI,CAAC,IAAI,KAAK,kBAAkB;YACtF,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,EAAE,EAAE;YAEpD,wDAAwD;YACxD,MAAM,eAAe,iBAAiB,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;YACxE,IAAI,gBAAgB,aAAa,IAAI,CAAC,MAAM,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/E,QAAQ,GAAG,CACT,CAAC,kBAAkB,EAAE,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,KAAK,EAAE,EAAE;gBAExF,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;oBACpB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC3D;YACF;QACF;QACA,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,qBAAqB;QACzB,OAAO;QACP;QACA,aAAa,QAAQ,WAAW;IAClC;IAEA,2BAA2B;IAC3B,QAAQ,GAAG,CAAC,CAAC,2BAA2B,CAAC,EAAE;IAE3C,6BAA6B;IAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE;IAEnC,8BAA8B;IAC9B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAEpC,kDAAkD;IAClD,IAAI;QACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QACxD,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;QAC1D,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC3D,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;IAC1D;IAEA,gFAAgF;IAChF,MAAM,WAAW,GAAG,gBAAgB,QAAQ,CAAC,EAAE,uBAAuB;IAEtE,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,cAAc,CAAC,EAAE;IAEtE,IAAI;QACF,4CAA4C;QAC5C,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QACnC;QAEA,sCAAsC;QACtC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,8CAA8C;QAC9C,IAAI,aAAa;YACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;QACpD;QAEA,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;YACT,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,wDAAwD;QACxD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,cAAc;YAClB,IAAI;gBACF,8DAA8D;gBAC9D,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,cAAc,UAAU,MAAM,IAAI,KAAK,SAAS,CAAC;YACnD,EAAE,OAAO,YAAY;gBACnB,wCAAwC;gBACxC,cAAc,SAAS,UAAU;YACnC;YACA,uDAAuD;YACvD,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,aAAa;QACvE;QAEA,0DAA0D;QAC1D,MAAM,aAAa,MAAM,SAAS,IAAI;QAEtC,iEAAiE;QACjE,IAAI,OAAO,WAAW,OAAO,KAAK,WAAW;YAC3C,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,YAA+B,iBAAiB;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,qEAAqE;QACrE,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF;AAEO,eAAe,qBAAqB,OAA4B;IACrE,yDAAyD;IACzD,MAAM,qBAAqB,CAAC,CAAC,QAAQ,WAAW;IAEhD,gFAAgF;IAChF,IAAI,WAAW;IAEf,IAAI,sBAAsB,QAAQ,WAAW,EAAE;QAC7C,kFAAkF;QAClF,WAAW,GAAG,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,WAAW,GAAG;IACrE;IAEA,+EAA+E;IAC/E,MAAM,SAAS,qBAAqB,UAAU;IAE9C,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,aAAa,EAAE,OAAO,aAAa,CAAC,EAAE;IAE/F,iDAAiD;IACjD,MAAM,YAAY,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAA,OACnC,KAAK,IAAI,CAAC,YAAY,KAAK,eAC3B,KAAK,EAAE,KAAK,gBACZ,KAAK,IAAI,CAAC,KAAK,KAAK;IAGtB,+BAA+B;IAC/B,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,UAAU,EAAE,EAAE;QAEtE,2CAA2C;QAC3C,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE;YAC1B,QAAQ,GAAG,CAAC,CAAC,4DAA4D,CAAC;YAC1E,UAAU,IAAI,CAAC,MAAM,GAAG,CAAC;QAC3B;QAEA,sDAAsD;QACtD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;YAC/C,QAAQ,GAAG,CAAC,CAAC,0EAA0E,CAAC;YACxF,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC;QAChD;QAEA,6CAA6C;QAC7C,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE;YACzB,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC;QAChD;QAEA,QAAQ,GAAG,CAAC,CAAC,oFAAoF,CAAC;QAElG,mEAAmE;QACnE,sFAAsF;QACtF,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAAK,EAAE,UAAU,EAAE;QACnF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,eAAe,IAAI,CAAC,+BAA+B,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;QAEjI,kEAAkE;QAClE,wFAAwF;QACxF,MAAM,YAAY,CAAA,GAAA,2IAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,KAAK,EAAE,gBAAgB,QAAQ,KAAK;QAC/E,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU,MAAM,CAAC,4BAA4B,CAAC;QAEvF,iCAAiC;QACjC,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;YAC5C,UAAU,OAAO,CAAC,CAAC,OAAqB;gBACtC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC,UAAU,EAAE,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,MAAM,SAAS,CAAC,YAAY,EAAE,MAAM,QAAQ,GAAG,QAAQ,MAAM;YAC5M;QACF;QAEA,kFAAkF;QAClF,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,QAAQ,GAAG,CAAC,CAAC,4GAA4G,CAAC;YAE1H,UAAU,OAAO,CAAC,CAAC;gBACjB,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;gBAE/C,mEAAmE;gBACnE,MAAM,wBAAwB,MAAM,SAAS,KAAK,QAAQ,MAAM,YAAY,KAAK;gBAEjF,+EAA+E;gBAC/E,MAAM,aAAa,MAAM,QAAQ,KAAK;gBACtC,MAAM,sBAAsB,MAAM,2BAA2B,KAAK;gBAClE,MAAM,qBAAqB,MAAM,YAAY,KAAK;gBAElD,iCAAiC;gBACjC,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,QAAQ;sBAC/B,EAAE,aAAa,QAAQ,KAAK;yCACT,EAAE,sBAAsB,QAAQ,KAAK;kCAC5C,EAAE,qBAAqB,QAAQ,KAAK;qCACjC,EAAE,wBAAwB,QAAQ,MAAM;gBAErE,2EAA2E;gBAC3E,IAAI,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,qBAAqB,oBAAoB,wBAAwB;oBAClG,IAAI,UAAU,IAAI,CAAC,MAAM,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;wBACvE,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,GAAG;4BACpD,SAAS,MAAM,MAAM;4BACrB,WAAW,MAAM,QAAQ;4BACzB,YAAY,MAAM,IAAI;4BACtB,OAAO,MAAM,YAAY;4BACzB,oBAAoB;4BACpB,UAAU,MAAM,QAAQ;4BACxB,YAAY,MAAM,SAAS;4BAC3B,SAAS,MAAM,OAAO;wBACxB;wBAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,QAAQ,kCAAkC,CAAC;oBACxF;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,QAAQ,8BAA8B,CAAC;gBACvF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,wEAAwE,CAAC;QACxF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,wEAAwE,CAAC;QACxF;QAEA,wCAAwC;QACxC,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC,EAAE,UAAU,IAAI;QACtE,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC,EAAE,UAAU,IAAI,CAAC,MAAM;QACtE,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB;IAC3G,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;IAClE;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,qBAAqB,QAAQ,KAAK,EAAE,QAAQ,KAAK;IACzE,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE;IAE1D,oDAAoD;IACpD,IAAI,gBAAgB,QAAQ,KAAK;IACjC,IAAI,gBAAgB,QAAQ,KAAK;IAEjC,2DAA2D;IAC3D,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,CAAC,oFAAoF,CAAC;QAElG,oFAAoF;QACpF,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAAK,EAAE,UAAU,EAAE;QACnF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,eAAe,IAAI,CAAC,+BAA+B,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;QAEjI,4CAA4C;QAC5C,MAAM,yBAAyB,QAAQ,KAAK,CAAC,MAAM,GAAG,eAAe,IAAI;QACzE,IAAI,yBAAyB,GAAG;YAC9B,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,uBAAuB,iEAAiE,CAAC;YAE9H,gEAAgE;YAChE,gBAAgB,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,eAAe,GAAG,CAAC,KAAK,EAAE;YAEvE,+DAA+D;YAC/D,gBAAgB,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OACnC,eAAe,GAAG,CAAC,KAAK,MAAM,KAAK,eAAe,GAAG,CAAC,KAAK,MAAM;YAGnE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,cAAc,MAAM,CAAC,WAAW,EAAE,cAAc,MAAM,CAAC,MAAM,CAAC;YAExH,iEAAiE;YACjE,IAAI;gBACF,+BAA+B;gBAC/B,MAAM,EAAE,KAAK,EAAE,GAAG;gBAElB,oCAAoC;gBACpC,MAAM,OAAO,CACX,GAAG,uBAAuB,aAAa,EAAE,2BAA2B,IAAI,aAAa,aAAa,uCAAuC,CAAC,EAC1I;oBACE,aAAa;oBACb,UAAU;gBACZ;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;IACF,OAAO;QACL,qDAAqD;QACrD,QAAQ,IAAI,CAAC,CAAC,0DAA0D,CAAC;IAC3E;IAEA,2DAA2D;IAC3D,MAAM,iBAAiB;QACrB,MAAM,QAAQ,QAAQ;QACtB,aAAa,QAAQ,QAAQ;QAC7B,eAAe;YACb,OAAO;YACP,OAAO;QACT;QACA,iBAAiB;IACnB;IAEA,IAAI;QACF,4CAA4C;QAC5C,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QACnC;QAEA,sCAAsC;QACtC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,8CAA8C;QAC9C,IAAI,aAAa;YACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;QACpD;QAEA,sDAAsD;QACxD,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE;QACtD,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM;QAEhG,MAAM,WAAW,MAAM,MAAM,UAAU;YACnC,QAAQ;YACR,SAAS;YACT,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,wDAAwD;QACxD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,cAAc;YAClB,IAAI;gBACF,8DAA8D;gBAC9D,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,cAAc,UAAU,MAAM,IAAI,KAAK,SAAS,CAAC;YACnD,EAAE,OAAO,YAAY;gBACnB,wCAAwC;gBACxC,cAAc,SAAS,UAAU;YACnC;YAEA,8BAA8B;YAC9B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,cAAc;YAChB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,cAAc;YAChB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,cAAc;YAChB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,cAAc;YAChB;YAEA,uDAAuD;YACvD,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,aAAa;QAClE;QAEA,0DAA0D;QAC1D,MAAM,aAAa,MAAM,SAAS,IAAI;QAEtC,0DAA0D;QAC1D,IAAI;QAEJ,sDAAsD;QACtD,IAAI,WAAW,QAAQ,EAAE;YACvB,sBAAsB;YACtB,aAAa;gBACX,SAAS;gBACT,aAAa,WAAW,QAAQ,CAAC,EAAE;gBACnC,SAAS;gBACT,OAAO;YACT;QACF,OAEK,IAAI,OAAO,WAAW,OAAO,KAAK,WAAW;YAChD,mCAAmC;YACnC,aAAa;gBACX,SAAS,WAAW,OAAO;gBAC3B,aAAa,WAAW,WAAW;gBACnC,SAAS,WAAW,OAAO,IAAI;gBAC/B,OAAO,WAAW,KAAK;YACzB;QACF,OACK;YACH,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,yBAAyB;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,gEAAgE;QAChE,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF;AAUO,SAAS,kBACd,QAAa,EACb,YAAoB,EAAE,EACtB,qBAA6B,EAAE;IAa/B,2BAA2B;IAC3B,IAAI,aAAa,QAAQ,aAAa,WAAW;QAC/C,OAAO;YACL,OAAO;YACP,MAAM;YACN,eAAe;QACjB;IACF;IAEA,gFAAgF;IAChF,IAAI,OAAO,aAAa,YAAY,CAAC,MAAM,OAAO,CAAC,WAAW;QAC5D,MAAM,aAKD,EAAE;QAEP,sCAAsC;QACtC,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC5C,IAAI,UAAU,QAAQ,UAAU,WAAW;gBACzC,iDAAiD;gBACjD,WAAW,IAAI,CAAC;oBACd,OAAO;oBACP,MAAM;oBACN,eAAe;gBACjB;YACF,OAAO,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC,QAAQ;gBAC7D,qCAAqC;gBACrC,MAAM,eAAe,kBAAkB,OAAO,KAAK;gBACnD,WAAW,IAAI,CAAC;YAClB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,gBAAgB;gBAChB,IAAI,MAAM,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,UAAU;oBACpD,iEAAiE;oBACjE,MAAM,eAAe,kBAAkB,KAAK,CAAC,EAAE,EAAE,KAAK;oBACtD,WAAW,IAAI,CAAC;wBACd,OAAO;wBACP,MAAM;wBACN,eAAe;wBACf,YAAY;4BAAC;yBAAa;oBAC5B;gBACF,OAAO;oBACL,eAAe;oBACf,WAAW,IAAI,CAAC;wBACd,OAAO;wBACP,MAAM;wBACN,eAAe;oBACjB;gBACF;YACF,OAAO;gBACL,yBAAyB;gBACzB,IAAI,OAAe,OAAO;gBAC1B,IAAI,SAAS,UAAU;oBACrB,oCAAoC;oBACpC,OAAO,OAAO,SAAS,CAAC,SAAS,YAAY;gBAC/C;gBAEA,WAAW,IAAI,CAAC;oBACd,OAAO;oBACP,MAAM;oBACN,eAAe;gBACjB;YACF;QACF;QAEA,wCAAwC;QACxC,OAAO;YACL,OAAO;YACP,MAAM;YACN,eAAe;YACf,YAAY;QACd;IACF,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;QAClC,gBAAgB;QAChB,IAAI,SAAS,MAAM,GAAG,KAAK,OAAO,QAAQ,CAAC,EAAE,KAAK,UAAU;YAC1D,iEAAiE;YACjE,MAAM,eAAe,kBAAkB,QAAQ,CAAC,EAAE,EAAE,GAAG,UAAU,IAAI,CAAC,EAAE;YACxE,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,eAAe;gBACf,YAAY;oBAAC;iBAAa;YAC5B;QACF,OAAO;YACL,eAAe;YACf,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,eAAe;YACjB;QACF;IACF,OAAO;QACL,wBAAwB;QACxB,IAAI,OAAe,OAAO;QAC1B,IAAI,SAAS,UAAU;YACrB,oCAAoC;YACpC,OAAO,OAAO,SAAS,CAAC,YAAY,YAAY;QAClD;QAEA,OAAO;YACL,OAAO;YACP,MAAM;YACN,eAAe;QACjB;IACF;AACF;AAEA,kEAAkE;AAElE;;;;;;;CAOC,GACD,SAAS,YAAY,MAAc,EAAE,SAAiB,EAAE,KAAa;IACnE,qFAAqF;IACrF,MAAM,cAAc,MAAM,IAAI,CAAC,CAAA,OAC7B,KAAK,MAAM,KAAK,UAAU,KAAK,YAAY,KAAK;IAGlD,8BAA8B;IAC9B,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC;IAC/D,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,CAAC,SAAS,EAAE,KAAK,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,EAAE;IAC1K;IAEA,sCAAsC;IACtC,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,OAAO,CAAC,EAAE,UAAU,eAAe,EAAE,cAAc,QAAQ,MAAM;IAExH,yDAAyD;IACzD,IAAI,aAAa;QACf,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,YAAY,KAAK;QAC7F,gBAAgB,OAAO,CAAC,CAAC,MAAM;YAC7B,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,CAAC,SAAS,EAAE,KAAK,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,EAAE;QAC3L;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,qBAAqB,KAA+B,EAAE,QAAgB,EAAE;IAY/E,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;IAEjF,sBAAsB;IACtB,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,OAC3B,KAAK,IAAI,CAAC,YAAY,KAAK,eAC3B,KAAK,EAAE,KAAK,gBACZ,KAAK,IAAI,CAAC,KAAK,KAAK;IAGtB,IAAI,CAAC,WAAW;QACd,QAAQ,GAAG,CAAC,CAAC,uDAAuD,CAAC;QACrE,OAAO,EAAE;IACX;IAEA,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,UAAU,EAAE,EAAE;IAE3E,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE;QAC1B,QAAQ,GAAG,CAAC,CAAC,mDAAmD,CAAC;QACjE,OAAO,EAAE;IACX;IAEA,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;QAC/C,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC;QAC/E,OAAO,EAAE;IACX;IAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE,UAAU,IAAI,CAAC,MAAM;IAC3E,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB;IAE9G,MAAM,SAWD,EAAE;IAEP,yCAAyC;IACzC,MAAM,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB;IAEzD,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,SAAS,UAAyB;QACjE,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,SAAS,EAAE;QAEjE,kDAAkD;QAClD,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,YAAY,UAAU,UAAU;QAEtC,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,OAAO,aAAa,EAAE,WAAW;QAEvF,8FAA8F;QAC9F,MAAM,aAAa,UAAU,QAAQ,KAAK;QAE1C,8DAA8D;QAC9D,MAAM,sBAAsB,UAAU,kBAAkB,KAAK;QAE7D,wCAAwC;QACxC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAE5C,6DAA6D;QAC7D,MAAM,qBAAqB,MAAM,MAAM,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK;QAEjF,mEAAmE;QACnE,MAAM,wBAAwB,YAAY,QAAQ,WAAW;QAE7D,sDAAsD;QACtD,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EACX,wBACA,SACA,MAAM,MAAM,SAAS,gBACrB,WACA,YACA,UAAU,QAAQ,EAClB,uBAAuB,OACvB,sBAAsB,OACtB,sBAAsB,MAAM,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,WACzE;QAGF,+EAA+E;QAC/E,IAAI,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,uBAAuB,OAAO,sBAAsB,OAAO,wBAAwB;YACpH,0BAA0B;YAC1B,IAAI,WAAW,UAAU,UAAU,IAAI;YACvC,IAAI,aAAmC;YAEvC,IAAI,UAAU,UAAU,EAAE;gBACxB,WAAW,UAAU,UAAU;gBAC/B,QAAQ,GAAG,CAAC,CAAC,6DAA6D,EAAE,UAAU;YACxF,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,UAAU;YACpF;YAEA,IAAI,CAAC,aAAa,UAAU,aAAa,QAAQ,KAAK,UAAU,KAAK,EAAE;gBACrE,IAAI;oBACF,iDAAiD;oBACjD,MAAM,WAAW,OAAO,UAAU,KAAK,KAAK,WAC1C,UAAU,KAAK,GACf,KAAK,KAAK,CAAC,UAAU,KAAK;oBAE5B,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,SAAS,UAAU,CAAC;oBAEnE,gDAAgD;oBAChD,MAAM,kBAAkB,kBAAkB,UAAU,WAAW;oBAC/D,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC,EAAE;oBAEhE,yCAAyC;oBACzC,OAAO,IAAI,CAAC;oBAEZ,8EAA8E;oBAC9E;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS,EAAE,EAAE,MAAM,0BAA0B,CAAC;oBACnG,uCAAuC;oBACvC,WAAW;gBACb;YACF;YAEA,4CAA4C;YAC5C,IAAI,aAAa,QAAQ;gBACvB,IAAI,MAAM,OAAO,CAAC,UAAU,OAAO,GAAG;oBACpC,aAAa,UAAU,OAAO;oBAC9B,QAAQ,GAAG,CAAC,CAAC,2DAA2D,CAAC;gBAC3E;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,UAAU;YAEnE,mDAAmD;YACnD,OAAO,IAAI,CAAC;gBACV,OAAO;gBACP,MAAM;gBACN,GAAI,cAAc;oBAAE,MAAM;gBAAW,CAAC;gBACtC,eAAe;YACjB;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,gEAAgE,CAAC;QAC9H;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC,EAAE;IAC5D,OAAO;AACT;AAEO,eAAe,iBACpB,OAAkC;IAElC,MAAM,WAAW,GAAG,gBAAgB,kBAAkB,CAAC,EAAE,0BAA0B;IAEnF,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,SAAS,cAAc,CAAC,EAAE;IAEvE,IAAI;QACF,4CAA4C;QAC5C,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QACnC;QAEA,sCAAsC;QACtC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,8CAA8C;QAC9C,IAAI,aAAa;YACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;QACpD;QAEA,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;YACT,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,wDAAwD;QACxD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,cAAc;YAClB,IAAI;gBACF,8DAA8D;gBAC9D,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,cAAc,UAAU,MAAM,IAAI,KAAK,SAAS,CAAC;YACnD,EAAE,OAAO,YAAY;gBACnB,wCAAwC;gBACxC,cAAc,SAAS,UAAU;YACnC;YACA,uDAAuD;YACvD,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,aAAa;QACxE;QAEA,0DAA0D;QAC1D,MAAM,aAAa,MAAM,SAAS,IAAI;QAEtC,iEAAiE;QACjE,IAAI,OAAO,WAAW,QAAQ,KAAK,WAAW;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,OAAO,YAAgC,iBAAiB;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,sEAAsE;QACtE,OAAO;YACL,UAAU;YACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF;;AAgCO,eAAe;IACpB,kCAAkC;IAClC,OAAO,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD;AAE1B,mDAAmD;AACnD;;;;;;;;;;;;;;;;;;;;;;;;;EAyBA,GACF;AAGO,eAAe,iBAAiB,UAA4B;IACjE,kCAAkC;IAClC,OAAO,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE;AAE5B,mDAAmD;AACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,GACF;AAGO,eAAe,iBAAiB,YAAoB;IACzD,kCAAkC;IAClC,OAAO,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE;AAE5B,mDAAmD;AACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BA,GACF;AA0BO,eAAe,8BACpB,OAA6C;IAE7C,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC,EAAE;QAEpD,4CAA4C;QAC5C,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QACnC;QAEA,sCAAsC;QACtC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,8CAA8C;QAC9C,IAAI,aAAa;YACf,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa;QACpD;QAEA,6CAA6C;QAC7C,MAAM,WAAW,MAAM,MACrB,uHAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,OAAO,EACxC;YACE,QAAQ;YACR,SAAS;YACT,MAAM,KAAK,SAAS,CAAC;QACvB;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,cAAc;YAClB,IAAI;gBACF,sDAAsD;gBACtD,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,cAAc,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,KAAK,SAAS,CAAC;YACxE,EAAE,OAAO,YAAY;gBACnB,wCAAwC;gBACxC,cAAc,SAAS,UAAU;YACnC;YACA,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,aAAa;QACvE;QAEA,MAAM,aAAa,MAAM,SAAS,IAAI;QACtC,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,OAAO;YACL,SAAS;YACT,eAAe,WAAW,aAAa;YACvC,SAAS,WAAW,OAAO,IAAI;QACjC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF;AAgBO,eAAe,qBACpB,aAAqB,EACrB,QAA8B;IAE9B,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,SAAS,8BAA8B,EAAE,eAAe;QAE/E,iEAAiE;QACjE,MAAM,WAAW,MAAM,MACrB,uHAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,OAAO,EACxC;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,KAAK;YACrD;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;YACF;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,cAAc;YAClB,IAAI;gBACF,sDAAsD;gBACtD,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,cAAc,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,KAAK,SAAS,CAAC;YACxE,EAAE,OAAO,YAAY;gBACnB,wCAAwC;gBACxC,cAAc,SAAS,UAAU;YACnC;YACA,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,aAAa;QACtE;QAEA,MAAM,aAAa,MAAM,SAAS,IAAI;QACtC,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,OAAO;YACL,SAAS;YACT,SAAS,WAAW,OAAO,IAAI,CAAC,SAAS,EAAE,SAAS,cAAc,CAAC;QACrE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF", "debugId": null}}, {"offset": {"line": 3859, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/sseClient.ts"], "sourcesContent": ["// No import needed for standard EventSource - it's built into modern browsers\r\n// Removed: import { EventSourcePolyfill } from 'event-source-polyfill';\r\nimport { API_ENDPOINTS } from \"./apiConfig\";\r\n\r\nexport interface SSEOptions {\r\n  onOpen?: () => void;\r\n  onMessage?: (event: MessageEvent) => void; // For generic messages\r\n  onCustomEvent?: (eventType: string, data: any) => void; // For named events\r\n  onError?: (error: Event | Error) => void; // Allow Error type for internal/connection errors\r\n  onClose?: (wasError?: boolean) => void; // Optional flag: true if closed due to error/reconnect failure\r\n}\r\n\r\nexport interface SSEConfig {\r\n  baseUrl?: string;\r\n  // Note: Standard EventSource doesn't support custom headers directly.\r\n  // Authentication must be handled via other means (cookies, query params - use with caution).\r\n  // apiKey?: string; // Keep if needed for URL construction, etc.\r\n  // keyType?: string; // Keep if needed for URL construction, etc.\r\n}\r\n\r\nexport class SSEClient {\r\n  private eventSource: EventSource | null = null;\r\n  private correlationId: string;\r\n  private options: SSEOptions;\r\n  private config: SSEConfig;\r\n  private reconnectAttempts: number = 0;\r\n  private maxReconnectAttempts: number = 5; // Adjust as needed\r\n  private reconnectDelay: number = 2000; // 2 seconds\r\n  private url: string;\r\n\r\n  // --- State Flags ---\r\n  // Flag prevents reconnect if user explicitly called close()\r\n  private explicitlyClosed: boolean = false;\r\n  // Flag prevents reconnect if a designated server error *event* was received (e.g., event: error)\r\n  private receivedServerErrorEvent: boolean = false;\r\n  // Flag prevents reconnect if a message indicated a terminal workflow status\r\n  private receivedTerminalStatus: boolean = false;\r\n\r\n  constructor(correlationId: string, options: SSEOptions = {}, config: SSEConfig = {}) {\r\n    this.correlationId = correlationId;\r\n    this.options = options;\r\n    // Construct the URL - ensure backend handles auth via cookies or secure URL params if needed\r\n    const baseUrl =\r\n      config.baseUrl || process.env.NEXT_PUBLIC_SSE_URL || API_ENDPOINTS.WORKFLOW_EXECUTION.STREAM;\r\n    this.url = `${baseUrl}/${this.correlationId}`;\r\n\r\n    this.config = {\r\n      ...config,\r\n      baseUrl: baseUrl,\r\n    };\r\n\r\n    if (!this.correlationId) {\r\n      console.error(\"SSEClient: Correlation ID is required.\");\r\n      // Consider throwing an error or handling more robustly\r\n      if (this.options.onError) {\r\n        // Use request time for the error if available, otherwise fallback\r\n        const errorTime = new Date().toISOString(); // Example timestamp\r\n        this.options.onError(new Error(`[${errorTime}] Correlation ID is required`));\r\n      }\r\n    }\r\n  }\r\n\r\n  connect(): void {\r\n    const connectTime = new Date().toISOString(); // Example timestamp\r\n    if (!this.correlationId) {\r\n      console.error(`[${connectTime}] SSEClient: Cannot connect without Correlation ID.`);\r\n      if (this.options.onError) {\r\n        this.options.onError(new Error(`[${connectTime}] Cannot connect without Correlation ID`));\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (this.isConnected()) {\r\n      console.log(`[${connectTime}] SSEClient: Connection attempt skipped. Already connected.`);\r\n      return;\r\n    }\r\n\r\n    // --- Reset flags for the new connection attempt ---\r\n    this.reset();\r\n\r\n    // Close any existing connection cleanly before creating a new one\r\n    this.closeInternal(false); // Don't trigger onClose callback here\r\n\r\n    try {\r\n      console.log(\r\n        `[${connectTime}] SSEClient: Connecting to ${this.url} for correlation ID: ${this.correlationId}`,\r\n      );\r\n\r\n      // --- Use standard EventSource ---\r\n      // Removed polyfill-specific options like headers\r\n      this.eventSource = new EventSource(this.url);\r\n      // Note: If you require authentication and can't use cookies,\r\n      // you might need to include tokens in the URL (less secure)\r\n      // e.g., new EventSource(`${this.url}?token=YOUR_TOKEN`)\r\n      // Ensure your server supports and secures this method.\r\n\r\n      console.log(\r\n        `[${connectTime}] SSEClient: EventSource instance created. Waiting for connection to open...`,\r\n      );\r\n\r\n      // --- Event Handlers ---\r\n\r\n      this.eventSource.onopen = () => {\r\n        const openTime = new Date().toISOString();\r\n        console.log(\r\n          `[${openTime}] SSEClient: Connection opened successfully for ${this.correlationId}.`,\r\n        );\r\n        this.reconnectAttempts = 0; // Reset reconnect attempts on successful open\r\n        if (this.options.onOpen) {\r\n          this.options.onOpen();\r\n        }\r\n      };\r\n\r\n      // --- Generic Message Handler ---\r\n      this.eventSource.onmessage = (event: MessageEvent) => {\r\n        const messageTime = new Date().toISOString();\r\n        // console.log(`[${messageTime}] SSEClient: Received generic message:`, event.data);\r\n        try {\r\n          const data = JSON.parse(event.data);\r\n          // console.log(`[${messageTime}] SSEClient: Parsed generic message data:`, data);\r\n\r\n          // --- Check for Terminal Workflow Status ---\r\n          this.checkAndHandleTerminalStatus(data, \"generic message\");\r\n\r\n          if (this.options.onMessage) {\r\n            this.options.onMessage(event); // Pass the raw event\r\n          }\r\n        } catch (error) {\r\n          console.error(\r\n            `[${messageTime}] SSEClient: Error parsing generic message data:`,\r\n            error,\r\n            event.data,\r\n          );\r\n          // Optionally trigger onError for parsing failures\r\n          // if (this.options.onError) this.options.onError(new Error(`[${messageTime}] Failed to parse message: ${error}`));\r\n        }\r\n      };\r\n\r\n      // --- Connection Error Handler ---\r\n      this.eventSource.onerror = (errorEvent: Event) => {\r\n        const errorTime = new Date().toISOString();\r\n        console.error(`[${errorTime}] SSEClient: Connection error occurred.`, errorEvent);\r\n\r\n        const currentReadyState = this.eventSource?.readyState;\r\n        const readyStateText = this.getReadyStateText(currentReadyState);\r\n        console.log(`[${errorTime}] SSEClient: EventSource readyState on error: ${readyStateText}`);\r\n\r\n        // Store flags before closing, as closeInternal might reset them indirectly\r\n        const wasExplicitlyClosed = this.explicitlyClosed;\r\n        const hadServerErrorEvent = this.receivedServerErrorEvent;\r\n        const hadTerminalStatus = this.receivedTerminalStatus;\r\n\r\n        // Close the faulty EventSource instance\r\n        this.closeInternal(true); // Mark as closed due to error\r\n\r\n        // --- Reconnection Logic ---\r\n        if (wasExplicitlyClosed) {\r\n          console.log(\r\n            `[${errorTime}] SSEClient: Connection error occurred after explicit close. No reconnection attempt.`,\r\n          );\r\n          return;\r\n        }\r\n\r\n        if (hadServerErrorEvent) {\r\n          console.log(\r\n            `[${errorTime}] SSEClient: Connection error occurred after receiving a server error event. No reconnection attempt.`,\r\n          );\r\n          if (this.options.onClose) this.options.onClose(true); // Indicate closed due to error\r\n          return;\r\n        }\r\n\r\n        if (hadTerminalStatus) {\r\n          console.log(\r\n            `[${errorTime}] SSEClient: Connection error occurred after receiving a terminal workflow status. No reconnection attempt.`,\r\n          );\r\n          if (this.options.onClose) this.options.onClose(true); // Indicate closed due to terminal status\r\n          return;\r\n        }\r\n\r\n        // --- Attempt Reconnection ---\r\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\r\n          this.reconnectAttempts++;\r\n          console.log(\r\n            `[${errorTime}] SSEClient: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay / 1000}s...`,\r\n          );\r\n          // Use arrow function to maintain 'this' context\r\n          setTimeout(() => this.connect(), this.reconnectDelay);\r\n        } else {\r\n          console.error(\r\n            `[${errorTime}] SSEClient: Max reconnect attempts (${this.maxReconnectAttempts}) reached for ${this.correlationId}. Giving up.`,\r\n          );\r\n          if (this.options.onError) {\r\n            this.options.onError(\r\n              new Error(\r\n                `[${errorTime}] Failed to reconnect after ${this.maxReconnectAttempts} attempts.`,\r\n              ),\r\n            );\r\n          }\r\n          if (this.options.onClose) this.options.onClose(true); // Indicate closed due to failure\r\n        }\r\n\r\n        // Call user's onError *after* handling reconnection logic attempt or decision\r\n        if (this.options.onError) {\r\n          this.options.onError(\r\n            new Error(`[${errorTime}] SSE connection error (readyState: ${readyStateText})`),\r\n          );\r\n        }\r\n      };\r\n\r\n      // --- Custom Event Listeners ---\r\n      const terminalErrorEventTypes = [\"error\", \"fatal_error\"]; // Events indicating server errors that should stop reconnects\r\n      const listenedEventTypes = [\r\n        // Add all event types your server might send\r\n        \"connected\",\r\n        \"log\",\r\n        \"warning\",\r\n        \"error\",\r\n        \"info\",\r\n        \"success\",\r\n        \"complete\",\r\n        \"fatal_error\",\r\n        // Add other specific events if needed, e.g., 'status_update'\r\n      ];\r\n\r\n      listenedEventTypes.forEach((eventType) => {\r\n        if (!this.eventSource) return; // Type guard\r\n\r\n        this.eventSource.addEventListener(eventType, (event: Event) => {\r\n          const eventTime = new Date().toISOString();\r\n          const messageEvent = event as MessageEvent; // Assume MessageEvent\r\n          // console.log(`[${eventTime}] SSEClient: Received event [${eventType}]:`, messageEvent.data);\r\n\r\n          try {\r\n            const data = messageEvent.data ? JSON.parse(messageEvent.data) : null;\r\n            // console.log(`[${eventTime}] SSEClient: Parsed event [${eventType}] data:`, data);\r\n\r\n            // Check if this event type itself signals a terminal server error\r\n            if (terminalErrorEventTypes.includes(eventType)) {\r\n              console.warn(\r\n                `[${eventTime}] SSEClient: Received terminal server error event [${eventType}]. Flagging to prevent reconnect.`,\r\n              );\r\n              this.receivedServerErrorEvent = true;\r\n              // Optionally close immediately\r\n              // setTimeout(() => this.close(), 50);\r\n            }\r\n\r\n            // --- Check for Terminal Workflow Status within the event data ---\r\n            this.checkAndHandleTerminalStatus(data, `event [${eventType}]`);\r\n\r\n            // Call specific custom event handler\r\n            if (this.options.onCustomEvent) {\r\n              this.options.onCustomEvent(eventType, data);\r\n            }\r\n          } catch (error) {\r\n            console.error(\r\n              `[${eventTime}] SSEClient: Error parsing event [${eventType}] data:`,\r\n              error,\r\n              messageEvent.data,\r\n            );\r\n            // Optionally trigger onError\r\n            // if (this.options.onError) this.options.onError(new Error(`[${eventTime}] Failed to parse ${eventType} event: ${error}`));\r\n          }\r\n        });\r\n      });\r\n    } catch (error) {\r\n      // Catches errors during `new EventSource()` instantiation (e.g., network error, invalid URL immediately)\r\n      const initErrorTime = new Date().toISOString();\r\n      console.error(`[${initErrorTime}] SSEClient: Failed to create EventSource instance:`, error);\r\n      this.eventSource = null; // Ensure it's null\r\n      if (this.options.onError) {\r\n        this.options.onError(\r\n          error instanceof Error\r\n            ? error\r\n            : new Error(`[${initErrorTime}] Failed to initialize SSE connection`),\r\n        );\r\n      }\r\n      // Decide if instantiation errors should trigger reconnect attempts\r\n      // Usually they indicate config issues, but you could add retry logic here too:\r\n      // if (this.reconnectAttempts < this.maxReconnectAttempts) { ... } else { onClose(true); }\r\n    }\r\n  }\r\n\r\n  // --- Helper to check for terminal status ---\r\n  private checkAndHandleTerminalStatus(data: any, context: string): void {\r\n    const checkTime = new Date().toISOString();\r\n    // Check if data is an object and has the workflow_status property\r\n    if (data && typeof data === \"object\" && data.hasOwnProperty(\"workflow_status\")) {\r\n      const status = data.workflow_status;\r\n      const terminalStatuses = [\"completed\", \"cancelled\", \"failed\"]; // Define terminal statuses\r\n\r\n      // Check for approval status and dispatch a custom event\r\n      // Only trigger approval UI when we have the specific criteria:\r\n      // 1. workflow_status is \"waiting_for_approval\"\r\n      // 2. approval_required is true\r\n      // 3. status is \"paused\"\r\n      if (\r\n        status?.toLowerCase() === \"waiting_for_approval\" &&\r\n        data.approval_required === true &&\r\n        data.status === \"paused\"\r\n      ) {\r\n        console.log(\r\n          `[${checkTime}] SSEClient: Received valid approval request in ${context}.`,\r\n          data,\r\n        );\r\n\r\n        // Only proceed if we have a valid node ID\r\n        if (data.node_id && data.node_id !== \"unknown\") {\r\n          // Import dynamically to avoid circular dependencies\r\n          import(\"@/lib/approvalUtils\")\r\n            .then(({ dispatchApprovalNeededEvent }) => {\r\n              // Use the centralized function to dispatch the event\r\n              dispatchApprovalNeededEvent(\r\n                this.correlationId,\r\n                data.node_id,\r\n                data.node_name || data.node_id,\r\n              );\r\n\r\n              // Also set a direct flag on the window for immediate access\r\n              // This helps with race conditions where the event might not be processed\r\n              window._pendingApproval = {\r\n                correlationId: this.correlationId,\r\n                nodeId: data.node_id,\r\n                nodeName: data.node_name || data.node_id,\r\n                timestamp: Date.now(),\r\n              };\r\n\r\n              // Force a UI update by dispatching a direct event\r\n              setTimeout(() => {\r\n                console.log(\r\n                  `[${checkTime}] SSEClient: Dispatching direct approval-ui-update event`,\r\n                );\r\n                window.dispatchEvent(new CustomEvent(\"approval-ui-update\"));\r\n              }, 500);\r\n            })\r\n            .catch((err) => {\r\n              console.error(`[${checkTime}] SSEClient: Error importing approvalUtils:`, err);\r\n\r\n              // Fallback to direct event dispatch if import fails\r\n              const timestamp = Date.now();\r\n              const event = new CustomEvent(\"workflow-approval-needed\", {\r\n                detail: {\r\n                  correlationId: this.correlationId,\r\n                  nodeId: data.node_id,\r\n                  nodeName: data.node_name || data.node_id,\r\n                  timestamp: timestamp,\r\n                  // Add a force flag to ensure this event is processed\r\n                  force: true,\r\n                },\r\n              });\r\n              window.dispatchEvent(event);\r\n\r\n              // Also set the window flag\r\n              window._pendingApproval = {\r\n                correlationId: this.correlationId,\r\n                nodeId: data.node_id,\r\n                nodeName: data.node_name || data.node_id,\r\n                timestamp: timestamp,\r\n              };\r\n            });\r\n        } else {\r\n          console.log(`[${checkTime}] SSEClient: Skipping approval event due to missing node ID`);\r\n        }\r\n\r\n        // Do NOT close the connection - keep streaming\r\n        return;\r\n      } else if (status?.toLowerCase() === \"waiting_for_approval\") {\r\n        // Log that we received a waiting_for_approval status but it didn't meet our criteria\r\n        console.log(\r\n          `[${checkTime}] SSEClient: Received waiting_for_approval status but it didn't meet criteria for approval UI:`,\r\n          data,\r\n        );\r\n      }\r\n\r\n      if (terminalStatuses.includes(status?.toLowerCase())) {\r\n        console.log(\r\n          `[${checkTime}] SSEClient: Received terminal workflow status \"${status}\" in ${context}. Flagging to prevent reconnect.`,\r\n        );\r\n        this.receivedTerminalStatus = true;\r\n\r\n        // Log the status for debugging\r\n        console.log(\r\n          `[${checkTime}] SSEClient: Workflow status: ${status}, Node ID: ${data.node_id || \"unknown\"}, Result: ${data.result || \"none\"}`,\r\n        );\r\n\r\n        // Close the connection proactively after receiving a terminal status\r\n        console.log(\r\n          `[${checkTime}] SSEClient: Closing connection shortly due to terminal status \"${status}\".`,\r\n        );\r\n\r\n        // Dispatch a custom event to notify listeners that a terminal status was received\r\n        // This can be used to update UI elements\r\n        const event = new CustomEvent(\"workflow-terminal-status\", {\r\n          detail: { status, nodeId: data.node_id, result: data.result },\r\n        });\r\n        window.dispatchEvent(event);\r\n\r\n        // Force close the connection immediately for complete status\r\n        if (status?.toLowerCase() === \"complete\") {\r\n          console.log(\r\n            `[${checkTime}] SSEClient: Immediately closing connection for \"complete\" status.`,\r\n          );\r\n          this.close();\r\n        } else {\r\n          // For other terminal statuses, close after a short delay to allow any final messages to be received\r\n          setTimeout(() => this.close(), 500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // --- Public close method ---\r\n  close(): void {\r\n    const closeTime = new Date().toISOString();\r\n    // Only log if we actually have something to close\r\n    if (this.eventSource) {\r\n      console.log(\r\n        `[${closeTime}] SSEClient: Explicitly closing connection for ${this.correlationId}...`,\r\n      );\r\n      this.explicitlyClosed = true; // Set flag to prevent reconnects by onerror\r\n      // Stop any scheduled reconnection attempts\r\n      this.reconnectAttempts = this.maxReconnectAttempts;\r\n      this.closeInternal(false); // Close the connection, not marked as an error closure\r\n    } else {\r\n      console.log(`[${closeTime}] SSEClient: Close called, but no active connection.`);\r\n    }\r\n  }\r\n\r\n  // --- Internal close method ---\r\n  private closeInternal(isDueToError: boolean): void {\r\n    if (this.eventSource) {\r\n      // Close the EventSource\r\n      this.eventSource.close();\r\n      this.eventSource = null;\r\n\r\n      // Call onClose only if:\r\n      // 1. It was an explicit close (isDueToError=false AND explicitlyClosed=true)\r\n      // 2. It was due to an error that won't be retried (handled in onerror logic)\r\n      // Avoid calling onClose here if an error occurred and a retry might happen.\r\n      // The onerror handler will call onClose if max retries are reached or retry is skipped.\r\n      if (!isDueToError && this.explicitlyClosed) {\r\n        if (this.options.onClose) {\r\n          // Pass 'false' because it wasn't an *unexpected* error closure\r\n          this.options.onClose(false);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  isConnected(): boolean {\r\n    // Also check explicitlyClosed flag, connection might be closing but state not yet CLOSED\r\n    return (\r\n      this.eventSource !== null &&\r\n      this.eventSource.readyState === EventSource.OPEN &&\r\n      !this.explicitlyClosed\r\n    );\r\n  }\r\n\r\n  getReadyState(): number | null {\r\n    return this.eventSource?.readyState ?? null;\r\n  }\r\n\r\n  private getReadyStateText(readyState: number | null | undefined): string {\r\n    switch (readyState) {\r\n      case EventSource.CONNECTING:\r\n        return \"CONNECTING (0)\";\r\n      case EventSource.OPEN:\r\n        return \"OPEN (1)\";\r\n      case EventSource.CLOSED:\r\n        return \"CLOSED (2)\";\r\n      case null:\r\n        return \"NULL (Not Initialized)\";\r\n      case undefined:\r\n        return \"UNDEFINED (Error State)\"; // Should ideally not happen\r\n      default:\r\n        return `UNKNOWN (${readyState})`;\r\n    }\r\n  }\r\n\r\n  // Reset all internal state flags to allow reconnection\r\n  reset(): void {\r\n    console.log(`SSEClient: Resetting internal state flags for ${this.correlationId}`);\r\n    this.explicitlyClosed = false;\r\n    this.receivedServerErrorEvent = false;\r\n    this.receivedTerminalStatus = false;\r\n    this.reconnectAttempts = 0;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,8EAA8E;AAC9E,wEAAwE;;;;AACxE;;AAkBO,MAAM;IACH,cAAkC,KAAK;IACvC,cAAsB;IACtB,QAAoB;IACpB,OAAkB;IAClB,oBAA4B,EAAE;IAC9B,uBAA+B,EAAE;IACjC,iBAAyB,KAAK;IAC9B,IAAY;IAEpB,sBAAsB;IACtB,4DAA4D;IACpD,mBAA4B,MAAM;IAC1C,iGAAiG;IACzF,2BAAoC,MAAM;IAClD,4EAA4E;IACpE,yBAAkC,MAAM;IAEhD,YAAY,aAAqB,EAAE,UAAsB,CAAC,CAAC,EAAE,SAAoB,CAAC,CAAC,CAAE;QACnF,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,GAAG;QACf,6FAA6F;QAC7F,MAAM,UACJ,OAAO,OAAO,IAAI,QAAQ,GAAG,CAAC,mBAAmB,IAAI,uHAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,MAAM;QAC9F,IAAI,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE;QAE7C,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,MAAM;YACT,SAAS;QACX;QAEA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,QAAQ,KAAK,CAAC;YACd,uDAAuD;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,kEAAkE;gBAClE,MAAM,YAAY,IAAI,OAAO,WAAW,IAAI,oBAAoB;gBAChE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,UAAU,4BAA4B,CAAC;YAC5E;QACF;IACF;IAEA,UAAgB;QACd,MAAM,cAAc,IAAI,OAAO,WAAW,IAAI,oBAAoB;QAClE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,mDAAmD,CAAC;YAClF,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,YAAY,uCAAuC,CAAC;YACzF;YACA;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,IAAI;YACtB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,2DAA2D,CAAC;YACxF;QACF;QAEA,qDAAqD;QACrD,IAAI,CAAC,KAAK;QAEV,kEAAkE;QAClE,IAAI,CAAC,aAAa,CAAC,QAAQ,sCAAsC;QAEjE,IAAI;YACF,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,YAAY,2BAA2B,EAAE,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,aAAa,EAAE;YAGnG,mCAAmC;YACnC,iDAAiD;YACjD,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,IAAI,CAAC,GAAG;YAC3C,6DAA6D;YAC7D,4DAA4D;YAC5D,wDAAwD;YACxD,uDAAuD;YAEvD,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,YAAY,4EAA4E,CAAC;YAG/F,yBAAyB;YAEzB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;gBACxB,MAAM,WAAW,IAAI,OAAO,WAAW;gBACvC,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,SAAS,gDAAgD,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBAEtF,IAAI,CAAC,iBAAiB,GAAG,GAAG,8CAA8C;gBAC1E,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,OAAO,CAAC,MAAM;gBACrB;YACF;YAEA,kCAAkC;YAClC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,CAAC;gBAC5B,MAAM,cAAc,IAAI,OAAO,WAAW;gBAC1C,oFAAoF;gBACpF,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;oBAClC,iFAAiF;oBAEjF,6CAA6C;oBAC7C,IAAI,CAAC,4BAA4B,CAAC,MAAM;oBAExC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;wBAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,qBAAqB;oBACtD;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CACX,CAAC,CAAC,EAAE,YAAY,gDAAgD,CAAC,EACjE,OACA,MAAM,IAAI;gBAEZ,kDAAkD;gBAClD,mHAAmH;gBACrH;YACF;YAEA,mCAAmC;YACnC,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC;gBAC1B,MAAM,YAAY,IAAI,OAAO,WAAW;gBACxC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,uCAAuC,CAAC,EAAE;gBAEtE,MAAM,oBAAoB,IAAI,CAAC,WAAW,EAAE;gBAC5C,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC;gBAC9C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,8CAA8C,EAAE,gBAAgB;gBAE1F,2EAA2E;gBAC3E,MAAM,sBAAsB,IAAI,CAAC,gBAAgB;gBACjD,MAAM,sBAAsB,IAAI,CAAC,wBAAwB;gBACzD,MAAM,oBAAoB,IAAI,CAAC,sBAAsB;gBAErD,wCAAwC;gBACxC,IAAI,CAAC,aAAa,CAAC,OAAO,8BAA8B;gBAExD,6BAA6B;gBAC7B,IAAI,qBAAqB;oBACvB,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,qFAAqF,CAAC;oBAEtG;gBACF;gBAEA,IAAI,qBAAqB;oBACvB,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,qGAAqG,CAAC;oBAEtH,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,+BAA+B;oBACrF;gBACF;gBAEA,IAAI,mBAAmB;oBACrB,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,2GAA2G,CAAC;oBAE5H,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,yCAAyC;oBAC/F;gBACF;gBAEA,+BAA+B;gBAC/B,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;oBACtD,IAAI,CAAC,iBAAiB;oBACtB,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,sCAAsC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,IAAI,CAAC;oBAEnJ,gDAAgD;oBAChD,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc;gBACtD,OAAO;oBACL,QAAQ,KAAK,CACX,CAAC,CAAC,EAAE,UAAU,qCAAqC,EAAE,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;oBAEjI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAClB,IAAI,MACF,CAAC,CAAC,EAAE,UAAU,4BAA4B,EAAE,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBAGvF;oBACA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,iCAAiC;gBACzF;gBAEA,8EAA8E;gBAC9E,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAClB,IAAI,MAAM,CAAC,CAAC,EAAE,UAAU,oCAAoC,EAAE,eAAe,CAAC,CAAC;gBAEnF;YACF;YAEA,iCAAiC;YACjC,MAAM,0BAA0B;gBAAC;gBAAS;aAAc,EAAE,8DAA8D;YACxH,MAAM,qBAAqB;gBACzB,6CAA6C;gBAC7C;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aAED;YAED,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,aAAa;gBAE5C,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,CAAC;oBAC5C,MAAM,YAAY,IAAI,OAAO,WAAW;oBACxC,MAAM,eAAe,OAAuB,sBAAsB;oBAClE,8FAA8F;oBAE9F,IAAI;wBACF,MAAM,OAAO,aAAa,IAAI,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,IAAI;wBACjE,oFAAoF;wBAEpF,kEAAkE;wBAClE,IAAI,wBAAwB,QAAQ,CAAC,YAAY;4BAC/C,QAAQ,IAAI,CACV,CAAC,CAAC,EAAE,UAAU,mDAAmD,EAAE,UAAU,iCAAiC,CAAC;4BAEjH,IAAI,CAAC,wBAAwB,GAAG;wBAChC,+BAA+B;wBAC/B,sCAAsC;wBACxC;wBAEA,mEAAmE;wBACnE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;wBAE9D,qCAAqC;wBACrC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;4BAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW;wBACxC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CACX,CAAC,CAAC,EAAE,UAAU,kCAAkC,EAAE,UAAU,OAAO,CAAC,EACpE,OACA,aAAa,IAAI;oBAEnB,6BAA6B;oBAC7B,4HAA4H;oBAC9H;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,yGAAyG;YACzG,MAAM,gBAAgB,IAAI,OAAO,WAAW;YAC5C,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,mDAAmD,CAAC,EAAE;YACtF,IAAI,CAAC,WAAW,GAAG,MAAM,mBAAmB;YAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAClB,iBAAiB,QACb,QACA,IAAI,MAAM,CAAC,CAAC,EAAE,cAAc,qCAAqC,CAAC;YAE1E;QACA,mEAAmE;QACnE,+EAA+E;QAC/E,0FAA0F;QAC5F;IACF;IAEA,8CAA8C;IACtC,6BAA6B,IAAS,EAAE,OAAe,EAAQ;QACrE,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,kEAAkE;QAClE,IAAI,QAAQ,OAAO,SAAS,YAAY,KAAK,cAAc,CAAC,oBAAoB;YAC9E,MAAM,SAAS,KAAK,eAAe;YACnC,MAAM,mBAAmB;gBAAC;gBAAa;gBAAa;aAAS,EAAE,2BAA2B;YAE1F,wDAAwD;YACxD,+DAA+D;YAC/D,+CAA+C;YAC/C,+BAA+B;YAC/B,wBAAwB;YACxB,IACE,QAAQ,kBAAkB,0BAC1B,KAAK,iBAAiB,KAAK,QAC3B,KAAK,MAAM,KAAK,UAChB;gBACA,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,gDAAgD,EAAE,QAAQ,CAAC,CAAC,EAC1E;gBAGF,0CAA0C;gBAC1C,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,KAAK,WAAW;oBAC9C,oDAAoD;oBACpD,4HACG,IAAI,CAAC,CAAC,EAAE,2BAA2B,EAAE;wBACpC,qDAAqD;wBACrD,4BACE,IAAI,CAAC,aAAa,EAClB,KAAK,OAAO,EACZ,KAAK,SAAS,IAAI,KAAK,OAAO;wBAGhC,4DAA4D;wBAC5D,yEAAyE;wBACzE,OAAO,gBAAgB,GAAG;4BACxB,eAAe,IAAI,CAAC,aAAa;4BACjC,QAAQ,KAAK,OAAO;4BACpB,UAAU,KAAK,SAAS,IAAI,KAAK,OAAO;4BACxC,WAAW,KAAK,GAAG;wBACrB;wBAEA,kDAAkD;wBAClD,WAAW;4BACT,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,wDAAwD,CAAC;4BAEzE,OAAO,aAAa,CAAC,IAAI,YAAY;wBACvC,GAAG;oBACL,GACC,KAAK,CAAC,CAAC;wBACN,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,2CAA2C,CAAC,EAAE;wBAE1E,oDAAoD;wBACpD,MAAM,YAAY,KAAK,GAAG;wBAC1B,MAAM,QAAQ,IAAI,YAAY,4BAA4B;4BACxD,QAAQ;gCACN,eAAe,IAAI,CAAC,aAAa;gCACjC,QAAQ,KAAK,OAAO;gCACpB,UAAU,KAAK,SAAS,IAAI,KAAK,OAAO;gCACxC,WAAW;gCACX,qDAAqD;gCACrD,OAAO;4BACT;wBACF;wBACA,OAAO,aAAa,CAAC;wBAErB,2BAA2B;wBAC3B,OAAO,gBAAgB,GAAG;4BACxB,eAAe,IAAI,CAAC,aAAa;4BACjC,QAAQ,KAAK,OAAO;4BACpB,UAAU,KAAK,SAAS,IAAI,KAAK,OAAO;4BACxC,WAAW;wBACb;oBACF;gBACJ,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,2DAA2D,CAAC;gBACxF;gBAEA,+CAA+C;gBAC/C;YACF,OAAO,IAAI,QAAQ,kBAAkB,wBAAwB;gBAC3D,qFAAqF;gBACrF,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,8FAA8F,CAAC,EAC7G;YAEJ;YAEA,IAAI,iBAAiB,QAAQ,CAAC,QAAQ,gBAAgB;gBACpD,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,gDAAgD,EAAE,OAAO,KAAK,EAAE,QAAQ,gCAAgC,CAAC;gBAEzH,IAAI,CAAC,sBAAsB,GAAG;gBAE9B,+BAA+B;gBAC/B,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,8BAA8B,EAAE,OAAO,WAAW,EAAE,KAAK,OAAO,IAAI,UAAU,UAAU,EAAE,KAAK,MAAM,IAAI,QAAQ;gBAGjI,qEAAqE;gBACrE,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,gEAAgE,EAAE,OAAO,EAAE,CAAC;gBAG5F,kFAAkF;gBAClF,yCAAyC;gBACzC,MAAM,QAAQ,IAAI,YAAY,4BAA4B;oBACxD,QAAQ;wBAAE;wBAAQ,QAAQ,KAAK,OAAO;wBAAE,QAAQ,KAAK,MAAM;oBAAC;gBAC9D;gBACA,OAAO,aAAa,CAAC;gBAErB,6DAA6D;gBAC7D,IAAI,QAAQ,kBAAkB,YAAY;oBACxC,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,kEAAkE,CAAC;oBAEnF,IAAI,CAAC,KAAK;gBACZ,OAAO;oBACL,oGAAoG;oBACpG,WAAW,IAAM,IAAI,CAAC,KAAK,IAAI;gBACjC;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,QAAc;QACZ,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,kDAAkD;QAClD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,UAAU,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YAExF,IAAI,CAAC,gBAAgB,GAAG,MAAM,4CAA4C;YAC1E,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB;YAClD,IAAI,CAAC,aAAa,CAAC,QAAQ,uDAAuD;QACpF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,oDAAoD,CAAC;QACjF;IACF;IAEA,gCAAgC;IACxB,cAAc,YAAqB,EAAQ;QACjD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,wBAAwB;YACxB,IAAI,CAAC,WAAW,CAAC,KAAK;YACtB,IAAI,CAAC,WAAW,GAAG;YAEnB,wBAAwB;YACxB,6EAA6E;YAC7E,6EAA6E;YAC7E,4EAA4E;YAC5E,wFAAwF;YACxF,IAAI,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,EAAE;gBAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACxB,+DAA+D;oBAC/D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACvB;YACF;QACF;IACF;IAEA,cAAuB;QACrB,yFAAyF;QACzF,OACE,IAAI,CAAC,WAAW,KAAK,QACrB,IAAI,CAAC,WAAW,CAAC,UAAU,KAAK,YAAY,IAAI,IAChD,CAAC,IAAI,CAAC,gBAAgB;IAE1B;IAEA,gBAA+B;QAC7B,OAAO,IAAI,CAAC,WAAW,EAAE,cAAc;IACzC;IAEQ,kBAAkB,UAAqC,EAAU;QACvE,OAAQ;YACN,KAAK,YAAY,UAAU;gBACzB,OAAO;YACT,KAAK,YAAY,IAAI;gBACnB,OAAO;YACT,KAAK,YAAY,MAAM;gBACrB,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,2BAA2B,4BAA4B;YAChE;gBACE,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACpC;IACF;IAEA,uDAAuD;IACvD,QAAc;QACZ,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,IAAI,CAAC,aAAa,EAAE;QACjF,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,wBAAwB,GAAG;QAChC,IAAI,CAAC,sBAAsB,GAAG;QAC9B,IAAI,CAAC,iBAAiB,GAAG;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 4228, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/approvalMigration.ts"], "sourcesContent": ["/**\r\n * Utility functions for migrating approval flags from config to definition\r\n */\r\nimport { Node } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\n\r\n/**\r\n * Migrates approval flags from config to definition for all nodes in a workflow\r\n *\r\n * @param nodes The workflow nodes to migrate\r\n * @returns The migrated workflow nodes\r\n */\r\nexport function migrateApprovalFlagToDefinition(\r\n  nodes: Node<WorkflowNodeData>[],\r\n): Node<WorkflowNodeData>[] {\r\n  return nodes.map((node: Node<WorkflowNodeData>) => {\r\n    // If the node has requires_approval in config, move it to definition\r\n    if (node.data?.config?.requires_approval !== undefined) {\r\n      console.log(`Migrating requires_approval flag for node ${node.id} from config to definition`);\r\n\r\n      const newNode: Node<WorkflowNodeData> = {\r\n        ...node,\r\n        data: {\r\n          ...node.data,\r\n          definition: node.data.definition\r\n            ? {\r\n                ...node.data.definition,\r\n                requires_approval: node.data.config.requires_approval,\r\n                // Ensure required properties are present with non-undefined values\r\n                name: node.data.definition.name || \"\",\r\n                display_name: node.data.definition.display_name || \"\",\r\n                description: node.data.definition.description || \"\",\r\n                category: node.data.definition.category || \"\",\r\n                icon: node.data.definition.icon || \"\",\r\n                beta: node.data.definition.beta || false,\r\n                inputs: node.data.definition.inputs || [],\r\n                outputs: node.data.definition.outputs || [],\r\n                is_valid: node.data.definition.is_valid || false,\r\n                path: node.data.definition.path || \"\",\r\n              }\r\n            : undefined,\r\n        },\r\n      };\r\n\r\n      // Remove from config\r\n      const newConfig = { ...node.data.config };\r\n      delete newConfig.requires_approval;\r\n      newNode.data.config = newConfig;\r\n\r\n      return newNode;\r\n    }\r\n\r\n    return node;\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAUM,SAAS,gCACd,KAA+B;IAE/B,OAAO,MAAM,GAAG,CAAC,CAAC;QAChB,qEAAqE;QACrE,IAAI,KAAK,IAAI,EAAE,QAAQ,sBAAsB,WAAW;YACtD,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,KAAK,EAAE,CAAC,0BAA0B,CAAC;YAE5F,MAAM,UAAkC;gBACtC,GAAG,IAAI;gBACP,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,IAAI,CAAC,UAAU,GAC5B;wBACE,GAAG,KAAK,IAAI,CAAC,UAAU;wBACvB,mBAAmB,KAAK,IAAI,CAAC,MAAM,CAAC,iBAAiB;wBACrD,mEAAmE;wBACnE,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;wBACnC,cAAc,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI;wBACnD,aAAa,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI;wBACjD,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI;wBAC3C,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;wBACnC,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;wBACnC,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,EAAE;wBACzC,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE;wBAC3C,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI;wBAC3C,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;oBACrC,IACA;gBACN;YACF;YAEA,qBAAqB;YACrB,MAAM,YAAY;gBAAE,GAAG,KAAK,IAAI,CAAC,MAAM;YAAC;YACxC,OAAO,UAAU,iBAAiB;YAClC,QAAQ,IAAI,CAAC,MAAM,GAAG;YAEtB,OAAO;QACT;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 4276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/workflowUtils.ts"], "sourcesContent": ["// src/lib/workflowUtils.ts\r\nimport { Node, Edge } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\"; // Adjust path if needed\r\nimport { migrateApprovalFlagToDefinition } from \"./approvalMigration\";\r\n\r\n/**\r\n * Interface for validation result\r\n */\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  error?: string;\r\n  data?: any;\r\n}\r\n\r\n/**\r\n * Formats the current workflow state (nodes and edges) into a savable JSON structure\r\n * and triggers a file download.\r\n *\r\n * @param nodes - The current array of nodes from React Flow.\r\n * @param edges - The current array of edges from React Flow.\r\n * @param filename - Optional filename for the downloaded file (defaults to 'workflow.json').\r\n * @param workflowName - Optional name for the workflow (defaults to 'Untitled Workflow').\r\n */\r\nexport function saveWorkflowToFile(\r\n  nodes: Node<WorkflowNodeData>[],\r\n  edges: Edge[],\r\n  filename: string = \"workflow.json\",\r\n  workflowName: string = \"Untitled Workflow\",\r\n): void {\r\n  // Map nodes to desired structure - preserve all node data for proper workflow restoration\r\n  const mappedNodes = nodes.map((node) => {\r\n    // Create a clean copy of the node with all its properties\r\n    return {\r\n      id: node.id,\r\n      type: node.type,\r\n      position: node.position,\r\n      data: node.data,\r\n    };\r\n  });\r\n\r\n  // Map edges - preserve all edge data for proper workflow restoration\r\n  const mappedEdges = edges.map((edge) => {\r\n    return {\r\n      id: edge.id,\r\n      source: edge.source,\r\n      sourceHandle: edge.sourceHandle,\r\n      target: edge.target,\r\n      targetHandle: edge.targetHandle,\r\n    };\r\n  });\r\n\r\n  // Create a simple JSON structure with workflow name, nodes and edges as the main keys\r\n  const workflowToSave = {\r\n    workflow_name: workflowName,\r\n    nodes: mappedNodes,\r\n    edges: mappedEdges,\r\n  };\r\n\r\n  try {\r\n    const jsonString = JSON.stringify(workflowToSave, null, 2); // Pretty print\r\n    const blob = new Blob([jsonString], { type: \"application/json\" });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement(\"a\");\r\n    link.href = url;\r\n    link.download = filename; // Use the provided filename\r\n    document.body.appendChild(link); // Required for Firefox\r\n    link.click();\r\n    // Clean up\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n    console.log(`Workflow saved successfully as ${filename}.`);\r\n  } catch (error) {\r\n    console.error(\"Failed to save workflow:\", error);\r\n  }\r\n}\r\n\r\n/**\r\n * Validates a workflow JSON structure to ensure it has the required fields and format.\r\n *\r\n * @param workflowData - The workflow data to validate.\r\n * @returns A ValidationResult object indicating if the data is valid and any error messages.\r\n */\r\nexport function validateWorkflowJson(workflowData: any): ValidationResult {\r\n  // Check if the data is an object\r\n  if (!workflowData || typeof workflowData !== \"object\") {\r\n    return {\r\n      isValid: false,\r\n      error: \"Workflow data must be a valid JSON object\",\r\n    };\r\n  }\r\n\r\n  // Check workflow_name if present (not required)\r\n  if (workflowData.workflow_name && typeof workflowData.workflow_name !== \"string\") {\r\n    return {\r\n      isValid: false,\r\n      error: \"Workflow name must be a string\",\r\n    };\r\n  }\r\n\r\n  // Check for required top-level properties\r\n  if (!Array.isArray(workflowData.nodes)) {\r\n    return {\r\n      isValid: false,\r\n      error: 'Workflow must contain a \"nodes\" array',\r\n    };\r\n  }\r\n\r\n  // Check for edges array (previously called connections)\r\n  if (!Array.isArray(workflowData.edges) && !Array.isArray(workflowData.connections)) {\r\n    return {\r\n      isValid: false,\r\n      error: 'Workflow must contain an \"edges\" array',\r\n    };\r\n  }\r\n\r\n  // For backward compatibility, use edges if available, otherwise use connections\r\n  const edgesArray = workflowData.edges || workflowData.connections;\r\n\r\n  // Validate each node\r\n  for (let i = 0; i < workflowData.nodes.length; i++) {\r\n    const node = workflowData.nodes[i];\r\n\r\n    // Check for required node properties\r\n    if (!node.id) {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} is missing an \"id\" property`,\r\n      };\r\n    }\r\n\r\n    if (!node.type) {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} is missing a \"type\" property`,\r\n      };\r\n    }\r\n\r\n    if (\r\n      !node.position ||\r\n      typeof node.position !== \"object\" ||\r\n      typeof node.position.x !== \"number\" ||\r\n      typeof node.position.y !== \"number\"\r\n    ) {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} has an invalid \"position\" property`,\r\n      };\r\n    }\r\n\r\n    if (!node.data || typeof node.data !== \"object\") {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} is missing a valid \"data\" property`,\r\n      };\r\n    }\r\n\r\n    // Check for required data properties\r\n    if (!node.data.type) {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} is missing a \"data.type\" property`,\r\n      };\r\n    }\r\n\r\n    if (!node.data.label) {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} is missing a \"data.label\" property`,\r\n      };\r\n    }\r\n\r\n    if (!node.data.definition || typeof node.data.definition !== \"object\") {\r\n      return {\r\n        isValid: false,\r\n        error: `Node at index ${i} is missing a valid \"data.definition\" property`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Validate each edge (previously called connection)\r\n  for (let i = 0; i < edgesArray.length; i++) {\r\n    const edge = edgesArray[i];\r\n\r\n    // Check for required edge properties\r\n    if (!edge.id) {\r\n      return {\r\n        isValid: false,\r\n        error: `Edge at index ${i} is missing an \"id\" property`,\r\n      };\r\n    }\r\n\r\n    if (!edge.source) {\r\n      return {\r\n        isValid: false,\r\n        error: `Edge at index ${i} is missing a \"source\" property`,\r\n      };\r\n    }\r\n\r\n    if (!edge.target) {\r\n      return {\r\n        isValid: false,\r\n        error: `Edge at index ${i} is missing a \"target\" property`,\r\n      };\r\n    }\r\n\r\n    // Check that source and target nodes exist\r\n    const sourceExists = workflowData.nodes.some((node: any) => node.id === edge.source);\r\n    if (!sourceExists) {\r\n      return {\r\n        isValid: false,\r\n        error: `Edge at index ${i} references a non-existent source node: ${edge.source}`,\r\n      };\r\n    }\r\n\r\n    const targetExists = workflowData.nodes.some((node: any) => node.id === edge.target);\r\n    if (!targetExists) {\r\n      return {\r\n        isValid: false,\r\n        error: `Edge at index ${i} references a non-existent target node: ${edge.target}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // If we've made it this far, the workflow is valid\r\n  return {\r\n    isValid: true,\r\n    data: workflowData,\r\n  };\r\n}\r\n\r\n/**\r\n * Converts a validated workflow JSON structure to ReactFlow format.\r\n *\r\n * @param workflowData - The validated workflow data.\r\n * @returns An object containing nodes and edges arrays for ReactFlow.\r\n */\r\nexport function convertWorkflowJsonToReactFlow(workflowData: any): {\r\n  nodes: Node<WorkflowNodeData>[];\r\n  edges: Edge[];\r\n} {\r\n  console.log(\"Converting workflow data to ReactFlow format:\", workflowData);\r\n\r\n  // Check if we're dealing with a wrapped workflow structure\r\n  // This happens when loading from the builder URL where the data is in workflow_data\r\n  if (workflowData.workflow_data && workflowData.workflow_data.nodes) {\r\n    console.log(\"Detected wrapped workflow structure, unwrapping...\");\r\n    workflowData = workflowData.workflow_data;\r\n  }\r\n\r\n  // Map nodes directly - they should already be in the correct format\r\n  const nodes: Node<WorkflowNodeData>[] = workflowData.nodes.map((node: any) => {\r\n    console.log(\"Processing node:\", node);\r\n\r\n    // Special handling for StartNode to ensure its configuration is preserved\r\n    if (node.data && node.data.originalType === \"StartNode\") {\r\n      console.log(\"Found StartNode, ensuring config is preserved:\", node);\r\n\r\n      // Ensure the node has a config object\r\n      if (!node.data.config) {\r\n        node.data.config = {};\r\n      }\r\n\r\n      // Ensure the config has a collected_parameters object\r\n      if (!node.data.config.collected_parameters) {\r\n        node.data.config.collected_parameters = {};\r\n      }\r\n\r\n      // Ensure all parameters in collected_parameters have the required property set\r\n      // This is critical for pre-built workflows where the required property might not be set\r\n      if (node.data.config.collected_parameters) {\r\n        Object.keys(node.data.config.collected_parameters).forEach((paramId) => {\r\n          const param = node.data.config.collected_parameters[paramId];\r\n          // If required is undefined, set it to true (consider required unless explicitly false)\r\n          if (param.required === undefined) {\r\n            console.log(`Setting required=true for parameter ${paramId} in StartNode`);\r\n            param.required = true;\r\n          }\r\n        });\r\n      }\r\n\r\n      console.log(\"StartNode config after ensuring structure:\", node.data.config);\r\n    }\r\n\r\n    return {\r\n      id: node.id,\r\n      type: node.type || \"WorkflowNode\", // Ensure we have a type, default to WorkflowNode\r\n      position: node.position,\r\n      data: node.data as WorkflowNodeData,\r\n      // Include any other properties that might be needed\r\n      width: node.width,\r\n      height: node.height,\r\n      selected: false, // Reset selection state\r\n      dragging: false, // Reset dragging state\r\n    };\r\n  });\r\n\r\n  // Map edges (previously called connections)\r\n  // For backward compatibility, use edges if available, otherwise use connections\r\n  const edgesArray = workflowData.edges || workflowData.connections;\r\n\r\n  const edges: Edge[] = edgesArray.map((edge: any) => {\r\n    console.log(\"Processing edge:\", edge);\r\n    return {\r\n      id: edge.id,\r\n      source: edge.source,\r\n      sourceHandle: edge.sourceHandle,\r\n      target: edge.target,\r\n      targetHandle: edge.targetHandle,\r\n      type: edge.type || \"default\", // Use custom edge type\r\n      // Include any other properties that might be needed\r\n      animated: true, // Add animation to edges\r\n    };\r\n  });\r\n\r\n  console.log(\"Converted nodes:\", nodes);\r\n  console.log(\"Converted edges:\", edges);\r\n\r\n  // Migrate approval flags from config to definition\r\n  const migratedNodes = migrateApprovalFlagToDefinition(nodes);\r\n  console.log(\"Migrated nodes:\", migratedNodes);\r\n\r\n  return { nodes: migratedNodes, edges };\r\n}\r\n\r\n/**\r\n * Loads a workflow from JSON data, validates it, and returns it in ReactFlow format.\r\n *\r\n * @param workflowData - The workflow data to load.\r\n * @returns A ValidationResult containing the converted workflow data if valid.\r\n */\r\nexport function loadWorkflowFromJson(workflowData: any): ValidationResult {\r\n  // First validate the workflow data\r\n  const validationResult = validateWorkflowJson(workflowData);\r\n\r\n  if (!validationResult.isValid) {\r\n    return validationResult;\r\n  }\r\n\r\n  try {\r\n    // Convert the validated data to ReactFlow format\r\n    const reactFlowData = convertWorkflowJsonToReactFlow(workflowData);\r\n\r\n    return {\r\n      isValid: true,\r\n      data: reactFlowData,\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      isValid: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Failed to convert workflow data to ReactFlow format\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;AAG3B;;AAoBO,SAAS,mBACd,KAA+B,EAC/B,KAAa,EACb,WAAmB,eAAe,EAClC,eAAuB,mBAAmB;IAE1C,0FAA0F;IAC1F,MAAM,cAAc,MAAM,GAAG,CAAC,CAAC;QAC7B,0DAA0D;QAC1D,OAAO;YACL,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;QACjB;IACF;IAEA,qEAAqE;IACrE,MAAM,cAAc,MAAM,GAAG,CAAC,CAAC;QAC7B,OAAO;YACL,IAAI,KAAK,EAAE;YACX,QAAQ,KAAK,MAAM;YACnB,cAAc,KAAK,YAAY;YAC/B,QAAQ,KAAK,MAAM;YACnB,cAAc,KAAK,YAAY;QACjC;IACF;IAEA,sFAAsF;IACtF,MAAM,iBAAiB;QACrB,eAAe;QACf,OAAO;QACP,OAAO;IACT;IAEA,IAAI;QACF,MAAM,aAAa,KAAK,SAAS,CAAC,gBAAgB,MAAM,IAAI,eAAe;QAC3E,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAmB;QAC/D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,UAAU,4BAA4B;QACtD,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,uBAAuB;QACxD,KAAK,KAAK;QACV,WAAW;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;QACpB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF;AAQO,SAAS,qBAAqB,YAAiB;IACpD,iCAAiC;IACjC,IAAI,CAAC,gBAAgB,OAAO,iBAAiB,UAAU;QACrD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,gDAAgD;IAChD,IAAI,aAAa,aAAa,IAAI,OAAO,aAAa,aAAa,KAAK,UAAU;QAChF,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,0CAA0C;IAC1C,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,KAAK,GAAG;QACtC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,wDAAwD;IACxD,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,aAAa,WAAW,GAAG;QAClF,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,gFAAgF;IAChF,MAAM,aAAa,aAAa,KAAK,IAAI,aAAa,WAAW;IAEjE,qBAAqB;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,KAAK,CAAC,MAAM,EAAE,IAAK;QAClD,MAAM,OAAO,aAAa,KAAK,CAAC,EAAE;QAElC,qCAAqC;QACrC,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,4BAA4B,CAAC;YACzD;QACF;QAEA,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,6BAA6B,CAAC;YAC1D;QACF;QAEA,IACE,CAAC,KAAK,QAAQ,IACd,OAAO,KAAK,QAAQ,KAAK,YACzB,OAAO,KAAK,QAAQ,CAAC,CAAC,KAAK,YAC3B,OAAO,KAAK,QAAQ,CAAC,CAAC,KAAK,UAC3B;YACA,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,mCAAmC,CAAC;YAChE;QACF;QAEA,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,UAAU;YAC/C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,mCAAmC,CAAC;YAChE;QACF;QAEA,qCAAqC;QACrC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,kCAAkC,CAAC;YAC/D;QACF;QAEA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YACpB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,mCAAmC,CAAC;YAChE;QACF;QAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,KAAK,UAAU;YACrE,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,8CAA8C,CAAC;YAC3E;QACF;IACF;IAEA,oDAAoD;IACpD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,MAAM,OAAO,UAAU,CAAC,EAAE;QAE1B,qCAAqC;QACrC,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,4BAA4B,CAAC;YACzD;QACF;QAEA,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,+BAA+B,CAAC;YAC5D;QACF;QAEA,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,+BAA+B,CAAC;YAC5D;QACF;QAEA,2CAA2C;QAC3C,MAAM,eAAe,aAAa,KAAK,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,EAAE,KAAK,KAAK,MAAM;QACnF,IAAI,CAAC,cAAc;YACjB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,wCAAwC,EAAE,KAAK,MAAM,EAAE;YACnF;QACF;QAEA,MAAM,eAAe,aAAa,KAAK,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,EAAE,KAAK,KAAK,MAAM;QACnF,IAAI,CAAC,cAAc;YACjB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,EAAE,wCAAwC,EAAE,KAAK,MAAM,EAAE;YACnF;QACF;IACF;IAEA,mDAAmD;IACnD,OAAO;QACL,SAAS;QACT,MAAM;IACR;AACF;AAQO,SAAS,+BAA+B,YAAiB;IAI9D,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,2DAA2D;IAC3D,oFAAoF;IACpF,IAAI,aAAa,aAAa,IAAI,aAAa,aAAa,CAAC,KAAK,EAAE;QAClE,QAAQ,GAAG,CAAC;QACZ,eAAe,aAAa,aAAa;IAC3C;IAEA,oEAAoE;IACpE,MAAM,QAAkC,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9D,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,0EAA0E;QAC1E,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,YAAY,KAAK,aAAa;YACvD,QAAQ,GAAG,CAAC,kDAAkD;YAE9D,sCAAsC;YACtC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;gBACrB,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC;YACtB;YAEA,sDAAsD;YACtD,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBAC1C,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC;YAC3C;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACzC,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;oBAC1D,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ;oBAC5D,uFAAuF;oBACvF,IAAI,MAAM,QAAQ,KAAK,WAAW;wBAChC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,aAAa,CAAC;wBACzE,MAAM,QAAQ,GAAG;oBACnB;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,8CAA8C,KAAK,IAAI,CAAC,MAAM;QAC5E;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI,IAAI;YACnB,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;YACf,oDAAoD;YACpD,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,UAAU;YACV,UAAU;QACZ;IACF;IAEA,4CAA4C;IAC5C,gFAAgF;IAChF,MAAM,aAAa,aAAa,KAAK,IAAI,aAAa,WAAW;IAEjE,MAAM,QAAgB,WAAW,GAAG,CAAC,CAAC;QACpC,QAAQ,GAAG,CAAC,oBAAoB;QAChC,OAAO;YACL,IAAI,KAAK,EAAE;YACX,QAAQ,KAAK,MAAM;YACnB,cAAc,KAAK,YAAY;YAC/B,QAAQ,KAAK,MAAM;YACnB,cAAc,KAAK,YAAY;YAC/B,MAAM,KAAK,IAAI,IAAI;YACnB,oDAAoD;YACpD,UAAU;QACZ;IACF;IAEA,QAAQ,GAAG,CAAC,oBAAoB;IAChC,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,mDAAmD;IACnD,MAAM,gBAAgB,CAAA,GAAA,+HAAA,CAAA,kCAA+B,AAAD,EAAE;IACtD,QAAQ,GAAG,CAAC,mBAAmB;IAE/B,OAAO;QAAE,OAAO;QAAe;IAAM;AACvC;AAQO,SAAS,qBAAqB,YAAiB;IACpD,mCAAmC;IACnC,MAAM,mBAAmB,qBAAqB;IAE9C,IAAI,CAAC,iBAAiB,OAAO,EAAE;QAC7B,OAAO;IACT;IAEA,IAAI;QACF,iDAAiD;QACjD,MAAM,gBAAgB,+BAA+B;QAErD,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 4556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/workflowApi.ts"], "sourcesContent": ["/**\r\n * Workflow API Module\r\n *\r\n * This module re-exports functions from the feature-specific API module\r\n * to maintain backward compatibility.\r\n *\r\n * IMPORTANT: This file is deprecated. Please import directly from:\r\n * @/app/(features)/workflows/api\r\n */\r\n\r\n// Re-export everything from the feature-specific API module\r\nexport * from \"@/app/(features)/workflows/api\";\r\n\r\n// Also export a default object for easier imports\r\nimport {\r\n  fetchWorkflowsByUser,\r\n  createEmptyWorkflow,\r\n  fetchWorkflowById,\r\n} from \"@/app/(features)/workflows/api\";\r\n\r\n// Add the missing fetchWorkflowFromBuilderUrl function that's not in the feature API\r\nimport { externalApi } from \"@/utils/axios\";\r\n\r\n/**\r\n * Fetches workflow data from a builder URL\r\n * @param url The URL to fetch the workflow data from\r\n * @returns The workflow data\r\n */\r\nexport async function fetchWorkflowFromBuilderUrl(url: string): Promise<any> {\r\n  try {\r\n    // For external URLs, we need to use the external API instance (no auth)\r\n    const response = await externalApi.get(url, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Export default object for easier imports\r\nexport default {\r\n  fetchWorkflowsByUser,\r\n  createEmptyWorkflow,\r\n  fetchWorkflowFromBuilderUrl,\r\n  fetchWorkflowById,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED,4DAA4D;;;;;AAG5D,kDAAkD;AAClD;AAMA,qFAAqF;AACrF;;;;AAOO,eAAe,4BAA4B,GAAW;IAC3D,IAAI;QACF,wEAAwE;QACxE,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,KAAK;YAC1C,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAE1F;QACA,MAAM;IACR;AACF;uCAGe;IACb,sBAAA,8IAAA,CAAA,uBAAoB;IACpB,qBAAA,8IAAA,CAAA,sBAAmB;IACnB;IACA,mBAAA,8IAAA,CAAA,oBAAiB;AACnB", "debugId": null}}]}
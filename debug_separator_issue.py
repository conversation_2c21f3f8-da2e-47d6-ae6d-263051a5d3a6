#!/usr/bin/env python3
"""
Debug script to test different separator values and see how they're processed.
"""

def test_separator_processing():
    """Test how different separator values are processed."""
    
    # Test cases for different separator inputs
    test_cases = [
        ("empty_string", ""),
        ("single_space", " "),
        ("quoted_space", '" "'),
        ("escaped_space", "\\ "),
        ("newline_literal", "\\n"),
        ("actual_newline", "\n"),
        ("tab_literal", "\\t"),
        ("actual_tab", "\t"),
        ("comma_space", ", "),
        ("dash", "-"),
        ("pipe", "|"),
    ]
    
    print("Testing separator processing...")
    print("=" * 60)
    
    for test_name, separator in test_cases:
        # Simulate the processing logic from the combine text component
        processed_separator = separator
        
        # Apply the same processing as in the component
        if isinstance(separator, str):
            if separator == "\\n":
                processed_separator = "\n"
            elif separator == "\\t":
                processed_separator = "\t"
            elif separator == "\\r":
                processed_separator = "\r"
            elif separator == "\\r\\n":
                processed_separator = "\r\n"
            # For other cases, use the separator as is
        
        # Test with sample data
        test_strings = ["Hello", "Pratham"]
        result = processed_separator.join(test_strings)
        
        # Display results
        print(f"Test: {test_name}")
        print(f"  Input separator: '{separator}' (length: {len(separator)})")
        print(f"  Processed separator: '{processed_separator}' (length: {len(processed_separator)})")
        print(f"  Result: '{result}'")
        print(f"  Expected for space: 'Hello Pratham'")
        print(f"  Match: {'✅' if result == 'Hello Pratham' else '❌'}")
        print()

def test_empty_vs_space():
    """Test the difference between empty string and space."""
    
    print("Testing empty string vs space...")
    print("=" * 40)
    
    # Test empty string
    empty_separator = ""
    result_empty = empty_separator.join(["Hello", "Pratham"])
    print(f"Empty string separator: '{empty_separator}' -> '{result_empty}'")
    
    # Test space
    space_separator = " "
    result_space = space_separator.join(["Hello", "Pratham"])
    print(f"Space separator: '{space_separator}' -> '{result_space}'")
    
    # Test what happens with None (should use default)
    print(f"\nDefault behavior (None/missing separator):")
    print(f"Should use default: ' ' -> 'Hello Pratham'")

def test_validation_logic():
    """Test the validation logic for separator field."""
    
    print("Testing validation logic...")
    print("=" * 30)
    
    # Simulate the frontend validation logic
    def validate_separator(value, required=False):
        # This mimics the frontend validation
        if not required and (value is None or value == ""):
            return {"isValid": True, "message": ""}
        
        if isinstance(value, str):
            return {"isValid": True, "message": "Valid input"}
        
        return {"isValid": False, "message": "Must be a string"}
    
    test_values = [
        None,
        "",
        " ",
        "  ",
        "\\n",
        "\n",
        "abc"
    ]
    
    for value in test_values:
        result = validate_separator(value, required=False)
        print(f"Value: {repr(value)} -> Valid: {result['isValid']}, Message: '{result['message']}'")

if __name__ == "__main__":
    test_separator_processing()
    print("\n" + "="*60 + "\n")
    test_empty_vs_space()
    print("\n" + "="*60 + "\n")
    test_validation_logic()
    
    print("\n" + "="*60)
    print("RECOMMENDATIONS:")
    print("1. To get 'Hello Pratham', use a single space: ' '")
    print("2. Make sure the separator field contains exactly one space character")
    print("3. If the field appears empty, it might actually contain a space")
    print("4. The default should now be space instead of newline")
    print("5. If you're still having issues, try typing the space and then another character,")
    print("   then delete the other character to ensure the space is registered")

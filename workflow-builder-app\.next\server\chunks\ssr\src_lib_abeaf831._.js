module.exports = {

"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "debounce": (()=>debounce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function debounce(func, wait) {
    let timeout = null;
    return function(...args) {
        const later = ()=>{
            timeout = null;
            func(...args);
        };
        if (timeout !== null) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(later, wait);
    };
}
}}),
"[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ValidationErrorCode": (()=>ValidationErrorCode)
});
var ValidationErrorCode = /*#__PURE__*/ function(ValidationErrorCode) {
    // Workflow structure errors
    ValidationErrorCode["WORKFLOW_INVALID_JSON"] = "WF001";
    ValidationErrorCode["WORKFLOW_MISSING_NODES"] = "WF002";
    ValidationErrorCode["WORKFLOW_MISSING_EDGES"] = "WF003";
    ValidationErrorCode["WORKFLOW_MISSING_START_NODE"] = "WF004";
    ValidationErrorCode["WORKFLOW_DISCONNECTED_NODES"] = "WF005";
    ValidationErrorCode["WORKFLOW_CYCLE_DETECTED"] = "WF006";
    ValidationErrorCode["WORKFLOW_INVALID_NAME"] = "WF007";
    ValidationErrorCode["WORKFLOW_EMPTY"] = "WF008";
    ValidationErrorCode["WORKFLOW_USING_FALLBACK_START_NODE"] = "WF009";
    // Node validation errors
    ValidationErrorCode["NODE_MISSING_ID"] = "ND001";
    ValidationErrorCode["NODE_MISSING_TYPE"] = "ND002";
    ValidationErrorCode["NODE_MISSING_POSITION"] = "ND003";
    ValidationErrorCode["NODE_MISSING_DATA"] = "ND004";
    ValidationErrorCode["NODE_MISSING_DATA_TYPE"] = "ND005";
    ValidationErrorCode["NODE_MISSING_DATA_LABEL"] = "ND006";
    ValidationErrorCode["NODE_MISSING_DATA_DEFINITION"] = "ND007";
    ValidationErrorCode["NODE_DUPLICATE_ID"] = "ND008";
    ValidationErrorCode["NODE_INVALID_POSITION"] = "ND009";
    // Edge validation errors
    ValidationErrorCode["EDGE_MISSING_ID"] = "ED001";
    ValidationErrorCode["EDGE_MISSING_SOURCE"] = "ED002";
    ValidationErrorCode["EDGE_MISSING_TARGET"] = "ED003";
    ValidationErrorCode["EDGE_SOURCE_NOT_FOUND"] = "ED004";
    ValidationErrorCode["EDGE_TARGET_NOT_FOUND"] = "ED005";
    ValidationErrorCode["EDGE_DUPLICATE_ID"] = "ED006";
    ValidationErrorCode["EDGE_SELF_REFERENCE"] = "ED007";
    // Field validation errors
    ValidationErrorCode["FIELD_REQUIRED"] = "FD001";
    ValidationErrorCode["FIELD_STRING_LENGTH"] = "FD002";
    ValidationErrorCode["FIELD_NUMBER_RANGE"] = "FD003";
    ValidationErrorCode["FIELD_PATTERN_MISMATCH"] = "FD004";
    ValidationErrorCode["FIELD_MISSING_REQUIRED_KEYS"] = "FD005";
    ValidationErrorCode["FIELD_CONNECTED_INPUT"] = "FD006";
    return ValidationErrorCode;
}({});
}}),
"[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createValidationError": (()=>createValidationError)
});
function createValidationError(code, message, severity = "error", nodeId, fieldId) {
    return {
        code,
        message,
        severity,
        nodeId,
        fieldId
    };
}
}}),
"[project]/src/lib/validation/nodeValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "validateNode": (()=>validateNode),
    "validateNodeUniqueness": (()=>validateNodeUniqueness),
    "validateNodes": (()=>validateNodes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)");
;
;
function validateNode(node, index) {
    const errors = [];
    // Check for required properties
    if (!node.id) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_ID, `Node at index ${index} is missing an ID`));
    }
    if (!node.type) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_TYPE, `Node ${node.id || `at index ${index}`} is missing a type`, "error", node.id));
    }
    if (!node.position) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_POSITION, `Node ${node.id || `at index ${index}`} is missing a position`, "error", node.id));
    } else if (typeof node.position !== "object" || typeof node.position.x !== "number" || typeof node.position.y !== "number") {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_INVALID_POSITION, `Node ${node.id || `at index ${index}`} has an invalid position`, "error", node.id));
    }
    if (!node.data) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_DATA, `Node ${node.id || `at index ${index}`} is missing data`, "error", node.id));
        return errors; // Stop validation if data is missing
    }
    // Validate data properties
    if (!node.data.type) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_DATA_TYPE, `Node ${node.id || `at index ${index}`} is missing data.type`, "error", node.id));
    }
    if (!node.data.label) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_DATA_LABEL, `Node ${node.id || `at index ${index}`} is missing data.label`, "error", node.id));
    }
    if (!node.data.definition) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_MISSING_DATA_DEFINITION, `Node ${node.id || `at index ${index}`} is missing data.definition`, "error", node.id));
    }
    return errors;
}
function validateNodeUniqueness(nodes) {
    const errors = [];
    const nodeIds = new Set();
    const duplicateIds = new Set();
    // Find duplicate IDs
    nodes.forEach((node)=>{
        if (node.id) {
            if (nodeIds.has(node.id)) {
                duplicateIds.add(node.id);
            } else {
                nodeIds.add(node.id);
            }
        }
    });
    // Create errors for duplicate IDs
    duplicateIds.forEach((id)=>{
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].NODE_DUPLICATE_ID, `Duplicate node ID: ${id}`, "error", id));
    });
    return errors;
}
function validateNodes(nodes) {
    const errors = [];
    const warnings = [];
    const infos = [];
    // Check if nodes is an array
    if (!Array.isArray(nodes)) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_NODES, 'Workflow must contain a "nodes" array'));
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    // Validate each node
    nodes.forEach((node, index)=>{
        const nodeErrors = validateNode(node, index);
        errors.push(...nodeErrors);
    });
    // Validate node uniqueness
    const uniquenessErrors = validateNodeUniqueness(nodes);
    errors.push(...uniquenessErrors);
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        infos
    };
}
}}),
"[project]/src/lib/validation/edgeValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "validateEdge": (()=>validateEdge),
    "validateEdgeUniqueness": (()=>validateEdgeUniqueness),
    "validateEdges": (()=>validateEdges)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)");
;
;
function validateEdge(edge, nodes, index) {
    const errors = [];
    // Check for required properties
    if (!edge.id) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_MISSING_ID, `Edge at index ${index} is missing an ID`));
    }
    if (!edge.source) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_MISSING_SOURCE, `Edge ${edge.id || `at index ${index}`} is missing a source`, "error", undefined, edge.id));
    }
    if (!edge.target) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_MISSING_TARGET, `Edge ${edge.id || `at index ${index}`} is missing a target`, "error", undefined, edge.id));
    }
    // Skip further validation if any required properties are missing
    if (!edge.source || !edge.target) {
        return errors;
    }
    // Check if source and target nodes exist
    const sourceNode = nodes.find((node)=>node.id === edge.source);
    if (!sourceNode) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_SOURCE_NOT_FOUND, `Edge ${edge.id || `at index ${index}`} has a non-existent source node: ${edge.source}`, "error", undefined, edge.id));
    }
    const targetNode = nodes.find((node)=>node.id === edge.target);
    if (!targetNode) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_TARGET_NOT_FOUND, `Edge ${edge.id || `at index ${index}`} has a non-existent target node: ${edge.target}`, "error", undefined, edge.id));
    }
    // Check for self-referencing edges
    if (edge.source === edge.target) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_SELF_REFERENCE, `Edge ${edge.id || `at index ${index}`} is self-referencing: ${edge.source} -> ${edge.target}`, "error", undefined, edge.id));
    }
    return errors;
}
function validateEdgeUniqueness(edges) {
    const errors = [];
    const edgeIds = new Set();
    const duplicateIds = new Set();
    // Find duplicate IDs
    edges.forEach((edge)=>{
        if (edge.id) {
            if (edgeIds.has(edge.id)) {
                duplicateIds.add(edge.id);
            } else {
                edgeIds.add(edge.id);
            }
        }
    });
    // Create errors for duplicate IDs
    duplicateIds.forEach((id)=>{
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].EDGE_DUPLICATE_ID, `Duplicate edge ID: ${id}`, "error", undefined, id));
    });
    return errors;
}
function validateEdges(edges, nodes) {
    const errors = [];
    const warnings = [];
    const infos = [];
    // Check if edges is an array
    if (!Array.isArray(edges)) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_EDGES, 'Workflow must contain an "edges" array'));
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    // Validate each edge
    edges.forEach((edge, index)=>{
        const edgeErrors = validateEdge(edge, nodes, index);
        errors.push(...edgeErrors);
    });
    // Validate edge uniqueness
    const uniquenessErrors = validateEdgeUniqueness(edges);
    errors.push(...uniquenessErrors);
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        infos
    };
}
}}),
"[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "detectCycles": (()=>detectCycles),
    "findStartNode": (()=>findStartNode),
    "getConnectedNodes": (()=>getConnectedNodes),
    "getDirectlyConnectedFields": (()=>getDirectlyConnectedFields),
    "getDirectlyConnectedNodes": (()=>getDirectlyConnectedNodes),
    "isNodeConnected": (()=>isNodeConnected),
    "isStartNode": (()=>isStartNode)
});
function isStartNode(node) {
    // Safety check for null/undefined node or data
    if (!node || !node.data) return false;
    // Log detailed node information for debugging
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [isStartNode] Checking if node is a StartNode:`, {
        id: node.id,
        type: node.type,
        dataType: node.data.type,
        originalType: node.data.originalType,
        definitionName: node.data.definition?.name,
        label: node.data.label
    });
    // Method 1: Check by originalType (primary method)
    if (node.data.originalType === "StartNode") {
        console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by originalType`);
        return true;
    }
    // Method 2: Check by definition name
    if (node.data.definition?.name === "StartNode") {
        console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by definition name`);
        return true;
    }
    // Method 3: Check by node type
    if (node.type === "StartNode" || node.data.type === "StartNode") {
        console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by node type`);
        return true;
    }
    // Method 4: Check by label (as a fallback)
    if (node.data.label === "Start" || node.data.label === "StartNode") {
        console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by label`);
        return true;
    }
    // Method 5: Check by component type and name pattern
    if (node.data.type === "component" && node.data.definition?.name && node.data.definition.name.toLowerCase().includes("start")) {
        console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by component name pattern`);
        return true;
    }
    return false;
}
function findStartNode(nodes) {
    // Safety check for null/undefined nodes array
    if (!nodes || !Array.isArray(nodes)) {
        console.warn('[findStartNode] Nodes array is null, undefined, or not an array');
        return undefined;
    }
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [findStartNode] Searching for StartNode in ${nodes.length} nodes`);
    // Log all node types for debugging
    console.log(`[${timestamp}] [findStartNode] Node types in workflow:`, nodes.map((node)=>({
            id: node.id,
            type: node.type,
            dataType: node.data?.type,
            originalType: node.data?.originalType,
            definitionName: node.data?.definition?.name,
            label: node.data?.label
        })));
    // Find the first node that passes the isStartNode check
    const startNode = nodes.find(isStartNode);
    if (startNode) {
        console.log(`[${timestamp}] [findStartNode] Found StartNode with ID: ${startNode.id}`);
    } else {
        console.warn(`[${timestamp}] [findStartNode] No StartNode found in workflow with ${nodes.length} nodes`);
    }
    return startNode;
}
function isNodeConnected(nodeId, connectedNodes) {
    return connectedNodes.has(nodeId);
}
function getDirectlyConnectedNodes(nodes, edges, startNodeId) {
    const directlyConnectedNodes = new Set();
    // Find all edges where the start node is the source
    edges.forEach((edge)=>{
        if (edge.source === startNodeId) {
            directlyConnectedNodes.add(edge.target);
        }
    });
    return directlyConnectedNodes;
}
function getDirectlyConnectedFields(nodes, edges, startNodeId) {
    const directlyConnectedFields = new Map();
    // Find all edges where the start node is the source
    edges.forEach((edge)=>{
        if (edge.source === startNodeId && edge.target && edge.targetHandle) {
            // Initialize the set if it doesn't exist
            if (!directlyConnectedFields.has(edge.target)) {
                directlyConnectedFields.set(edge.target, new Set());
            }
            // Add the field name to the set
            const fieldName = edge.targetHandle;
            directlyConnectedFields.get(edge.target)?.add(fieldName);
        }
    });
    return directlyConnectedFields;
}
function getConnectedNodes(nodes, edges, startNodeId) {
    const connectedNodes = new Set([
        startNodeId
    ]);
    const queue = [
        startNodeId
    ];
    // Create an adjacency list for faster lookups
    const adjacencyList = new Map();
    // Initialize the adjacency list
    nodes.forEach((node)=>{
        adjacencyList.set(node.id, []);
    });
    // Populate the adjacency list
    edges.forEach((edge)=>{
        const sourceNeighbors = adjacencyList.get(edge.source) || [];
        sourceNeighbors.push(edge.target);
        adjacencyList.set(edge.source, sourceNeighbors);
    });
    // BFS to find all connected nodes
    while(queue.length > 0){
        const currentNodeId = queue.shift();
        const neighbors = adjacencyList.get(currentNodeId) || [];
        for (const neighborId of neighbors){
            if (!connectedNodes.has(neighborId)) {
                connectedNodes.add(neighborId);
                queue.push(neighborId);
            }
        }
    }
    return connectedNodes;
}
function detectCycles(nodes, edges) {
    const adjacencyList = new Map();
    // Initialize the adjacency list
    nodes.forEach((node)=>{
        adjacencyList.set(node.id, []);
    });
    // Populate the adjacency list
    edges.forEach((edge)=>{
        const sourceNeighbors = adjacencyList.get(edge.source) || [];
        sourceNeighbors.push(edge.target);
        adjacencyList.set(edge.source, sourceNeighbors);
    });
    const visited = new Set();
    const recursionStack = new Set();
    const nodesInCycle = new Set();
    // DFS function to detect cycles
    function dfs(nodeId) {
        if (recursionStack.has(nodeId)) {
            nodesInCycle.add(nodeId);
            return true;
        }
        if (visited.has(nodeId)) {
            return false;
        }
        visited.add(nodeId);
        recursionStack.add(nodeId);
        const neighbors = adjacencyList.get(nodeId) || [];
        for (const neighborId of neighbors){
            if (dfs(neighborId)) {
                nodesInCycle.add(nodeId);
                return true;
            }
        }
        recursionStack.delete(nodeId);
        return false;
    }
    // Run DFS from each node
    nodes.forEach((node)=>{
        if (!visited.has(node.id)) {
            dfs(node.id);
        }
    });
    return Array.from(nodesInCycle);
}
}}),
"[project]/src/lib/validation/connectivityValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "detectCyclesInWorkflow": (()=>detectCyclesInWorkflow),
    "validateConnectivity": (()=>validateConnectivity),
    "validateStartNode": (()=>validateStartNode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript)");
;
;
;
function validateStartNode(nodes) {
    const errors = [];
    const warnings = [];
    const infos = [];
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [validateStartNode] Starting StartNode validation with ${nodes.length} nodes`);
    // Check if nodes array is valid
    if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
        console.warn(`[${timestamp}] [validateStartNode] Nodes array is empty or invalid`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_EMPTY, "Workflow is empty or not properly initialized"));
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    // Find the StartNode using our enhanced detection
    const startNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findStartNode"])(nodes);
    if (!startNode) {
        console.warn(`[${timestamp}] [validateStartNode] No StartNode found using standard detection methods`);
        // Try to find a node that might be a Start node based on additional heuristics
        const potentialStartNodes = nodes.filter((node)=>{
            // Check for any node with "start" in its label or type (case insensitive)
            const label = (node.data?.label || "").toLowerCase();
            const type = (node.data?.type || "").toLowerCase();
            const originalType = (node.data?.originalType || "").toLowerCase();
            return label.includes("start") || type.includes("start") || originalType.includes("start");
        });
        if (potentialStartNodes.length > 0) {
            const fallbackNode = potentialStartNodes[0];
            console.log(`[${timestamp}] [validateStartNode] Found potential StartNode using fallback detection: ${fallbackNode.id}`);
            // Add a warning but don't fail validation
            warnings.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_USING_FALLBACK_START_NODE, `Using ${fallbackNode.data?.label || fallbackNode.id} as a fallback Start node`, "warning"));
            return {
                isValid: true,
                errors,
                warnings,
                infos,
                startNodeId: fallbackNode.id
            };
        }
        // No StartNode found, even with fallback detection
        console.error(`[${timestamp}] [validateStartNode] No StartNode found, validation failed`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_START_NODE, "Workflow must have a Start node"));
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    console.log(`[${timestamp}] [validateStartNode] StartNode validation successful, found node: ${startNode.id}`);
    return {
        isValid: true,
        errors,
        warnings,
        infos,
        startNodeId: startNode.id
    };
}
function validateConnectivity(nodes, edges, startNodeId) {
    const errors = [];
    const warnings = [];
    const infos = [];
    // Get all nodes connected to the StartNode
    const connectedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getConnectedNodes"])(nodes, edges, startNodeId);
    // Find disconnected nodes
    const disconnectedNodes = nodes.filter((node)=>!connectedNodes.has(node.id));
    if (disconnectedNodes.length > 0) {
        const disconnectedNodeIds = disconnectedNodes.map((node)=>node.id).join(", ");
        const disconnectedNodeLabels = disconnectedNodes.map((node)=>node.data?.label || node.id).join(", ");
        warnings.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_DISCONNECTED_NODES, `The following nodes are not connected to the Start node and will not be saved or executed: ${disconnectedNodeLabels}`, "warning"));
        // Add info about what will happen with these nodes
        infos.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_INFO, `Only nodes connected to the Start node will be included in the workflow execution and saving.`, "info"));
    }
    // Always return isValid=true since disconnected nodes are now just warnings
    return {
        isValid: true,
        errors,
        warnings,
        infos,
        connectedNodes
    };
}
function detectCyclesInWorkflow(nodes, edges) {
    const errors = [];
    const warnings = [];
    const infos = [];
    // Detect cycles
    const nodesInCycle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["detectCycles"])(nodes, edges);
    if (nodesInCycle.length > 0) {
        warnings.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_CYCLE_DETECTED, `Workflow contains cycles involving the following nodes: ${nodesInCycle.join(", ")}`, "warning"));
    }
    return {
        isValid: nodesInCycle.length === 0,
        errors,
        warnings,
        infos
    };
}
}}),
"[project]/src/lib/validation/fieldValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "collectAllFields": (()=>collectAllFields),
    "collectMissingRequiredFields": (()=>collectMissingRequiredFields),
    "isFieldRequired": (()=>isFieldRequired),
    "validateField": (()=>validateField),
    "validateFields": (()=>validateFields)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)");
;
;
/**
 * Unwraps dual-purpose input values that may be wrapped in {value: ...} objects
 * @param value The value to unwrap
 * @returns The unwrapped value
 */ function unwrapDualPurposeValue(value) {
    console.log(`[unwrapDualPurposeValue] Input value:`, value, `Type: ${typeof value}`);
    // Check if the value is wrapped in a dual-purpose input structure
    if (typeof value === 'object' && value !== null && 'value' in value) {
        // Check if this looks like a dual-purpose input wrapper
        // It should have a 'value' property and optionally other metadata properties
        const keys = Object.keys(value);
        const hasValueProperty = keys.includes('value');
        const hasOnlyValueProperty = keys.length === 1 && hasValueProperty;
        const hasValueAndMetadata = hasValueProperty && keys.every((key)=>key === 'value' || key === 'transition_id' || key === 'metadata' || key === 'type');
        console.log(`[unwrapDualPurposeValue] Analysis:`, {
            hasValueProperty,
            hasOnlyValueProperty,
            hasValueAndMetadata,
            keys,
            valueType: typeof value.value
        });
        if (hasOnlyValueProperty || hasValueAndMetadata) {
            console.log(`[unwrapDualPurposeValue] Detected dual-purpose wrapper, unwrapping:`, value);
            console.log(`[unwrapDualPurposeValue] Wrapper keys:`, keys);
            console.log(`[unwrapDualPurposeValue] Extracted value:`, value.value);
            // If the extracted value is a JSON string for object types, parse it
            if (typeof value.value === 'string' && value.value.trim().startsWith('{')) {
                try {
                    const parsed = JSON.parse(value.value);
                    console.log(`[unwrapDualPurposeValue] Parsed JSON string to object:`, parsed);
                    return parsed;
                } catch (e) {
                    console.log(`[unwrapDualPurposeValue] Failed to parse JSON string, returning as-is:`, e);
                    return value.value;
                }
            }
            return value.value;
        }
    }
    // If it's a JSON string that looks like an object, try to parse it
    if (typeof value === 'string' && value.trim().startsWith('{')) {
        try {
            const parsed = JSON.parse(value);
            console.log(`[unwrapDualPurposeValue] Parsed standalone JSON string to object:`, parsed);
            return parsed;
        } catch (e) {
            console.log(`[unwrapDualPurposeValue] Failed to parse standalone JSON string, returning as-is:`, e);
        }
    }
    console.log(`[unwrapDualPurposeValue] No unwrapping needed, returning as-is:`, value);
    return value;
}
function validateField(inputDef, value, nodeId, node) {
    console.log(`[${getTimestamp()}] [validateField] ========== VALIDATING FIELD ==========`);
    console.log(`[${getTimestamp()}] [validateField] Field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
    console.log(`[${getTimestamp()}] [validateField] Node ID: ${nodeId}`);
    console.log(`[${getTimestamp()}] [validateField] Node label: ${node?.data?.label || "Unknown"}`);
    console.log(`[${getTimestamp()}] [validateField] Input type: ${inputDef.input_type}`);
    console.log(`[${getTimestamp()}] [validateField] Value: ${JSON.stringify(value)}`);
    const errors = [];
    // Skip validation for handle inputs
    if (inputDef.is_handle) {
        console.log(`[${getTimestamp()}] [validateField] Skipping validation for handle input: ${inputDef.name}`);
        return errors;
    }
    // Determine if field is required
    const isRequired = node ? isFieldRequired(node, inputDef) : !!inputDef.required;
    console.log(`[${getTimestamp()}] [validateField] Is required: ${isRequired ? "YES" : "NO"} (node provided: ${node ? "YES" : "NO"})`);
    // Check if field is empty
    const isEmpty = isValueEmpty(value, inputDef.input_type);
    console.log(`[${getTimestamp()}] [validateField] Is empty: ${isEmpty ? "YES" : "NO"}`);
    // Check if required field is missing
    if (isRequired && isEmpty) {
        console.log(`[${getTimestamp()}] [validateField] VALIDATION ERROR: Required field "${inputDef.display_name || inputDef.name}" is missing`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_REQUIRED, `Field "${inputDef.display_name}" is required`, "error", nodeId, inputDef.name));
        console.log(`[${getTimestamp()}] [validateField] Stopping validation for this field due to missing required value`);
        return errors; // Stop validation if required field is missing
    }
    // Skip validation for empty optional fields
    if (!isRequired && isEmpty) {
        console.log(`[${getTimestamp()}] [validateField] Skipping validation for empty optional field: ${inputDef.name}`);
        return errors;
    }
    // Validate based on input type
    switch(inputDef.input_type){
        case "string":
            validateStringField(inputDef, value, nodeId, errors);
            break;
        case "int":
        case "float":
        case "number":
            validateNumberField(inputDef, value, nodeId, errors);
            break;
        case "dict":
        case "json":
        case "object":
            validateObjectField(inputDef, value, nodeId, errors);
            break;
    }
    console.log(`[${getTimestamp()}] [validateField] Field validation complete. Errors found: ${errors.length}`);
    return errors;
}
/**
 * Validates a string field
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param errors The array of errors to append to
 */ function validateStringField(inputDef, value, nodeId, errors) {
    console.log(`[${getTimestamp()}] [validateStringField] Validating string field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
    console.log(`[${getTimestamp()}] [validateStringField] Value type: ${typeof value}, Value: ${JSON.stringify(value)}`);
    // Check type
    if (typeof value !== "string") {
        console.log(`[${getTimestamp()}] [validateStringField] VALIDATION ERROR: Value is not a string: ${typeof value}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_STRING_LENGTH, `Field "${inputDef.display_name}" must be a string`, "error", nodeId, inputDef.name));
        return;
    }
    // Check min length
    if (inputDef.min_length !== undefined && value.length < inputDef.min_length) {
        console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String length ${value.length} is less than minimum ${inputDef.min_length}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_STRING_LENGTH, `Field "${inputDef.display_name}" must be at least ${inputDef.min_length} characters`, "warning", nodeId, inputDef.name));
    }
    // Check max length
    if (inputDef.max_length !== undefined && value.length > inputDef.max_length) {
        console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String length ${value.length} exceeds maximum ${inputDef.max_length}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_STRING_LENGTH, `Field "${inputDef.display_name}" must be at most ${inputDef.max_length} characters`, "warning", nodeId, inputDef.name));
    }
    // Check pattern
    if (inputDef.pattern && !new RegExp(inputDef.pattern).test(value)) {
        console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String does not match pattern: ${inputDef.pattern}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_PATTERN_MISMATCH, inputDef.pattern_error || `Field "${inputDef.display_name}" does not match the required pattern`, "warning", nodeId, inputDef.name));
    }
    console.log(`[${getTimestamp()}] [validateStringField] String validation complete. Errors found: ${errors.length}`);
}
/**
 * Validates a number field
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param errors The array of errors to append to
 */ function validateNumberField(inputDef, value, nodeId, errors) {
    console.log(`[${getTimestamp()}] [validateNumberField] Validating number field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
    console.log(`[${getTimestamp()}] [validateNumberField] Original value type: ${typeof value}, Value: ${JSON.stringify(value)}`);
    // Convert to number if string
    const numValue = typeof value === "string" ? Number(value) : value;
    console.log(`[${getTimestamp()}] [validateNumberField] Converted value: ${numValue}, isNaN: ${isNaN(numValue)}`);
    // Check type
    if (typeof numValue !== "number" || isNaN(numValue)) {
        console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION ERROR: Value is not a valid number: ${value}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_NUMBER_RANGE, `Field "${inputDef.display_name}" must be a number`, "error", nodeId, inputDef.name));
        return;
    }
    // Check min value
    if (inputDef.min_value !== undefined && numValue < Number(inputDef.min_value)) {
        console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION WARNING: Number ${numValue} is less than minimum ${inputDef.min_value}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_NUMBER_RANGE, `Field "${inputDef.display_name}" must be at least ${inputDef.min_value}`, "warning", nodeId, inputDef.name));
    }
    // Check max value
    if (inputDef.max_value !== undefined && numValue > Number(inputDef.max_value)) {
        console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION WARNING: Number ${numValue} exceeds maximum ${inputDef.max_value}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_NUMBER_RANGE, `Field "${inputDef.display_name}" must be at most ${inputDef.max_value}`, "warning", nodeId, inputDef.name));
    }
    console.log(`[${getTimestamp()}] [validateNumberField] Number validation complete. Errors found: ${errors.length}`);
}
/**
 * Validates an object field
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param errors The array of errors to append to
 */ function validateObjectField(inputDef, value, nodeId, errors) {
    console.log(`[${getTimestamp()}] [validateObjectField] Validating object field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
    console.log(`[${getTimestamp()}] [validateObjectField] Value type: ${typeof value}, Value: ${JSON.stringify(value)}`);
    // Handle string representation of objects
    if (typeof value === "string") {
        console.log(`[${getTimestamp()}] [validateObjectField] Value is a string, attempting to parse as JSON`);
        try {
            value = JSON.parse(value);
            console.log(`[${getTimestamp()}] [validateObjectField] Successfully parsed string as JSON: ${JSON.stringify(value)}`);
        } catch (e) {
            const errorMessage = e instanceof Error ? e.message : String(e);
            console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Failed to parse string as JSON: ${errorMessage}`);
            errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])("INVALID_JSON", `Field "${inputDef.display_name}" must be valid JSON`, "error", nodeId, inputDef.name));
            return;
        }
    }
    // Check type
    if (typeof value !== "object" || value === null) {
        console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Value is not an object: ${typeof value}, null: ${value === null}`);
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_MISSING_REQUIRED_KEYS, `Field "${inputDef.display_name}" must be an object`, "error", nodeId, inputDef.name));
        return;
    }
    // Check required keys
    if (inputDef.required_keys && inputDef.required_keys.length > 0) {
        console.log(`[${getTimestamp()}] [validateObjectField] Checking for required keys: ${inputDef.required_keys.join(', ')}`);
        const missingKeys = inputDef.required_keys.filter((key)=>!(key in value));
        if (missingKeys.length > 0) {
            console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Missing required keys: ${missingKeys.join(', ')}`);
            errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].FIELD_MISSING_REQUIRED_KEYS, `Field "${inputDef.display_name}" is missing required keys: ${missingKeys.join(", ")}`, "error", nodeId, inputDef.name));
        } else {
            console.log(`[${getTimestamp()}] [validateObjectField] All required keys are present`);
        }
    } else {
        console.log(`[${getTimestamp()}] [validateObjectField] No required keys specified for this object`);
    }
    console.log(`[${getTimestamp()}] [validateObjectField] Object validation complete. Errors found: ${errors.length}`);
}
function validateFields(node) {
    console.log(`[${getTimestamp()}] [validateFields] ========== STARTING FIELD VALIDATION FOR NODE ==========`);
    console.log(`[${getTimestamp()}] [validateFields] Node: ${node.id} (${node.data.label || "Unnamed"})`);
    console.log(`[${getTimestamp()}] [validateFields] Type: ${node.data.type}, Original Type: ${node.data.originalType}`);
    const errors = [];
    const warnings = [];
    const infos = [];
    // Skip validation if node has no definition or inputs
    if (!node.data?.definition?.inputs) {
        console.log(`[${getTimestamp()}] [validateFields] Skipping validation - node has no definition or inputs`);
        return {
            isValid: true,
            errors,
            warnings,
            infos
        };
    }
    const inputs = node.data.definition.inputs;
    const config = node.data.config || {};
    // Log detailed node information
    console.log(`[${getTimestamp()}] [validateFields] Node details:
    - ID: ${node.id}
    - Label: ${node.data.label || "Unnamed"}
    - Type: ${node.data.type}
    - Original Type: ${node.data.originalType}
    - Position: (${node.position.x}, ${node.position.y})
    - Total inputs: ${inputs.length}
    - Config keys: ${Object.keys(config).join(', ') || "none"}
    - Is MCP component: ${isMCPMarketplaceComponent(node) ? "YES" : "NO"}`);
    // Validate each input
    inputs.forEach((input)=>{
        console.log(`[${getTimestamp()}] [validateFields] Validating input: ${input.name} (${input.display_name || input.name})`);
        // Pass the node to validateField so it can determine if the field is required
        const fieldErrors = validateField(input, config[input.name], node.id, node);
        // Log validation results
        if (fieldErrors.length > 0) {
            console.log(`[${getTimestamp()}] [validateFields] Found ${fieldErrors.length} validation issues for field ${input.name}:`);
            fieldErrors.forEach((error, index)=>{
                console.log(`[${getTimestamp()}] [validateFields]   ${index + 1}. ${error.severity.toUpperCase()}: ${error.message}`);
            });
        } else {
            console.log(`[${getTimestamp()}] [validateFields] Field ${input.name} passed validation`);
        }
        // Separate errors by severity
        fieldErrors.forEach((error)=>{
            if (error.severity === "error") {
                errors.push(error);
            } else if (error.severity === "warning") {
                warnings.push(error);
            } else if (error.severity === "info") {
                infos.push(error);
            }
        });
    });
    // Log validation summary
    console.log(`[${getTimestamp()}] [validateFields] ========== FIELD VALIDATION COMPLETE ==========`);
    console.log(`[${getTimestamp()}] [validateFields] Validation results for node ${node.id} (${node.data.label || "Unnamed"}):`);
    console.log(`[${getTimestamp()}] [validateFields] - Errors: ${errors.length}`);
    console.log(`[${getTimestamp()}] [validateFields] - Warnings: ${warnings.length}`);
    console.log(`[${getTimestamp()}] [validateFields] - Infos: ${infos.length}`);
    console.log(`[${getTimestamp()}] [validateFields] - Is valid: ${errors.length === 0 ? "YES" : "NO"}`);
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        infos
    };
}
/**
 * Helper function to get a timestamp for logging
 * @returns Formatted timestamp string
 */ function getTimestamp() {
    return new Date().toISOString().replace('T', ' ').substring(0, 19);
}
/**
 * Evaluates requirement rules to determine if a field should be required
 *
 * @param requirementRules The requirement rules to evaluate
 * @param requirementLogic The logic to use when combining rules (OR, AND)
 * @param config The current node configuration
 * @returns True if the requirement rules are satisfied
 */ function evaluateRequirementRules(requirementRules, requirementLogic, config) {
    if (!requirementRules || requirementRules.length === 0) {
        return false;
    }
    console.log(`[${getTimestamp()}] [evaluateRequirementRules] Evaluating ${requirementRules.length} requirement rules with logic: ${requirementLogic || "OR"}`);
    const logic = requirementLogic || "OR";
    const results = requirementRules.map((rule)=>{
        const targetValue = config[rule.field_name];
        const operator = rule.operator || "equals";
        let ruleMatches = false;
        switch(operator){
            case "equals":
                ruleMatches = targetValue === rule.field_value;
                break;
            case "not_equals":
                ruleMatches = targetValue !== rule.field_value;
                break;
            case "contains":
                ruleMatches = typeof targetValue === "string" && targetValue.includes(rule.field_value);
                break;
            case "exists":
                ruleMatches = targetValue !== undefined && targetValue !== null;
                break;
            case "not_exists":
                ruleMatches = targetValue === undefined || targetValue === null;
                break;
            default:
                ruleMatches = targetValue === rule.field_value;
        }
        console.log(`[${getTimestamp()}] [evaluateRequirementRules] Rule: ${rule.field_name} ${operator} ${rule.field_value} | Target: ${targetValue} | Result: ${ruleMatches}`);
        return ruleMatches;
    });
    const finalResult = logic === "AND" ? results.every((r)=>r) : results.some((r)=>r);
    console.log(`[${getTimestamp()}] [evaluateRequirementRules] Final result with ${logic} logic: ${finalResult}`);
    return finalResult;
}
/**
 * Determines if a node is an MCP Marketplace component
 *
 * @param node The node to check
 * @returns True if the node is an MCP Marketplace component
 */ function isMCPMarketplaceComponent(node) {
    if (!node || !node.data) return false;
    // Check various indicators that this is an MCP component
    const isMCP = node.data.type === "mcp" || node.data.originalType === "MCPMarketplaceComponent" || node.data.type === "MCPMarketplaceComponent" || node.data.definition && node.data.definition.type === "MCPMarketplaceComponent" || node.data.definition && node.data.definition.mcp_info || node.data.definition && node.data.definition.path && (node.data.definition.path.includes("mcp_marketplace") || node.data.definition.path.includes("components.mcp"));
    console.log(`[${getTimestamp()}] [isMCPMarketplaceComponent] Node ${node.id} (${node.data.label || "Unnamed"}):
    - type: ${node.data.type}
    - originalType: ${node.data.originalType}
    - definition.type: ${node.data?.definition?.type}
    - has mcp_info: ${!!node.data?.definition?.mcp_info}
    - path: ${node.data?.definition?.path}
    - RESULT: ${isMCP ? "IS MCP COMPONENT" : "NOT MCP COMPONENT"}`);
    return !!isMCP; // Convert to boolean
}
function isFieldRequired(node, input, isHandleConnected = false) {
    console.log(`[${getTimestamp()}] [isFieldRequired] Checking if field is required:
    - Node: ${node.id} (${node.data.label || "Unnamed"})
    - Field: ${input.name} (${input.display_name || input.name})
    - Input type: ${input.input_type}
    - Explicitly required: ${input.required === true ? "YES" : "NO"}
    - Is handle: ${input.is_handle ? "YES" : "NO"}
    - Is handle connected: ${isHandleConnected ? "YES" : "NO"}
    - Ends with _handle: ${input.name.endsWith("_handle") ? "YES" : "NO"}
    - Has requirement rules: ${input.requirement_rules && input.requirement_rules.length > 0 ? "YES" : "NO"}`);
    // Check requirement rules first - if they exist and are satisfied, the field is required
    if (input.requirement_rules && input.requirement_rules.length > 0) {
        const config = node.data.config || {};
        const isRequiredByRules = evaluateRequirementRules(input.requirement_rules, input.requirement_logic, config);
        if (isRequiredByRules) {
            console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is required by requirement rules`);
            // For handle inputs, they're only required if not connected
            if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
                if (isHandleConnected) {
                    console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and is connected, not required for direct input despite requirement rules`);
                    return false;
                } else {
                    console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input but NOT connected, required for direct input due to requirement rules`);
                    return true;
                }
            }
            return true;
        }
    }
    // If explicitly marked as required, it's required
    if (input.required === true) {
        // For handle inputs, they're only required if not connected
        if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
            if (isHandleConnected) {
                console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and is connected, not required for direct input`);
                return false;
            } else {
                console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input but NOT connected, required for direct input`);
                return true;
            }
        }
        console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is explicitly marked as required`);
        return true;
    }
    // For handle inputs that aren't explicitly required, they're never required for direct input
    if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
        console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and not explicitly required, not required for direct input`);
        return false;
    }
    // For MCP components, we need special handling
    const isMCP = isMCPMarketplaceComponent(node);
    if (isMCP) {
        console.log(`[${getTimestamp()}] [isFieldRequired] Node is an MCP component, applying special rules for field ${input.name}`);
        // Common optional fields in MCP components
        const commonOptionalFields = [
            "link",
            "api_key",
            "base_url"
        ];
        if (commonOptionalFields.includes(input.name)) {
            console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is in the common optional fields list for MCP components`);
            return false;
        }
        // For MCP components, consider fields required if they are explicitly marked as required: true
        // OR if they are not explicitly marked as optional (required: false)
        const isRequired = input.required !== false;
        console.log(`[${getTimestamp()}] [isFieldRequired] MCP component field ${input.name} required status: ${isRequired ? "REQUIRED" : "OPTIONAL"} (required !== false: ${input.required !== false})`);
        return isRequired;
    }
    // For standard components, consider fields required if they are explicitly marked as required: true
    // OR if they are not explicitly marked as optional (required: false)
    const isRequired = input.required !== false;
    console.log(`[${getTimestamp()}] [isFieldRequired] Standard component field ${input.name} required status: ${isRequired ? "REQUIRED" : "OPTIONAL"} (required !== false: ${input.required !== false})`);
    return isRequired;
}
/**
 * Determines if a field value should be considered empty
 *
 * @param value The field value
 * @param inputType The type of the input
 * @returns True if the value should be considered empty
 */ function isValueEmpty(value, inputType) {
    console.log(`[${getTimestamp()}] [isValueEmpty] Checking if value is empty:
    - Input type: ${inputType}
    - Value type: ${typeof value}
    - Value: ${JSON.stringify(value)}
    - Is undefined: ${value === undefined ? "YES" : "NO"}
    - Is null: ${value === null ? "YES" : "NO"}`);
    // Undefined or null values are always empty
    if (value === undefined || value === null) {
        console.log(`[${getTimestamp()}] [isValueEmpty] Value is undefined or null, considered EMPTY`);
        return true;
    }
    // For boolean values, they're never empty (false is a valid value)
    if (inputType === "boolean" || inputType === "bool") {
        console.log(`[${getTimestamp()}] [isValueEmpty] Boolean value ${value}, considered NOT EMPTY (false is a valid value)`);
        return false;
    }
    // For numeric values, 0 is a valid value
    if (inputType === "number" || inputType === "int" || inputType === "float") {
        console.log(`[${getTimestamp()}] [isValueEmpty] Numeric value ${value}, considered NOT EMPTY (0 is a valid value)`);
        return false;
    }
    // For strings, empty string is empty
    if (inputType === "string" || inputType === "text") {
        const isEmpty = value === "";
        console.log(`[${getTimestamp()}] [isValueEmpty] String value "${value}", considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
        return isEmpty;
    }
    // For objects and arrays, empty objects/arrays are considered empty
    if ((inputType === "object" || inputType === "dict" || inputType === "json") && typeof value === "object") {
        const isEmpty = Object.keys(value).length === 0;
        console.log(`[${getTimestamp()}] [isValueEmpty] Object value with ${Object.keys(value).length} keys, considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
        return isEmpty;
    }
    if ((inputType === "array" || inputType === "list") && Array.isArray(value)) {
        const isEmpty = value.length === 0;
        console.log(`[${getTimestamp()}] [isValueEmpty] Array value with ${value.length} items, considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
        return isEmpty;
    }
    // For string representations of objects/arrays, check if they're empty
    if ((inputType === "object" || inputType === "dict" || inputType === "json" || inputType === "array" || inputType === "list") && typeof value === "string") {
        const trimmed = value.trim();
        const isEmpty = trimmed === "" || trimmed === "{}" || trimmed === "[]";
        console.log(`[${getTimestamp()}] [isValueEmpty] String representation of object/array: "${trimmed}", considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
        return isEmpty;
    }
    // Default case - empty string is empty
    const isEmpty = value === "";
    console.log(`[${getTimestamp()}] [isValueEmpty] Default case, value: "${value}", considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
    return isEmpty;
}
/**
 * Checks if a handle input is connected to another node
 *
 * @param nodeId The ID of the node containing the handle
 * @param inputName The name of the handle input
 * @param edges The array of edges in the workflow
 * @returns True if the handle is connected to another node
 */ function isHandleConnected(nodeId, inputName, edges) {
    // For target handles, the connection would be to a target handle with the input name
    const isConnected = edges.some((edge)=>edge.target === nodeId && edge.targetHandle === inputName);
    console.log(`[${getTimestamp()}] [isHandleConnected] Checking if handle ${nodeId}.${inputName} is connected: ${isConnected ? "YES" : "NO"}`);
    return isConnected;
}
function collectAllFields(nodes, connectedNodes, edges = []) {
    console.log(`[${getTimestamp()}] [collectAllFields] ========== STARTING ALL FIELDS COLLECTION ==========`);
    console.log(`[${getTimestamp()}] [collectAllFields] Total nodes: ${nodes.length}, Connected nodes: ${connectedNodes.size}, Edges: ${edges.length}`);
    console.log(`[${getTimestamp()}] [collectAllFields] Connected node IDs: ${Array.from(connectedNodes).join(', ')}`);
    // Find the StartNode
    const startNode = nodes.find((node)=>node.data.originalType === "StartNode");
    // Get directly connected nodes and fields if StartNode exists
    let directlyConnectedNodes = new Set();
    let directlyConnectedFields = new Map();
    if (startNode) {
        // Import the functions from utils
        const { getDirectlyConnectedNodes, getDirectlyConnectedFields } = __turbopack_context__.r("[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript)");
        directlyConnectedNodes = getDirectlyConnectedNodes(nodes, edges, startNode.id);
        directlyConnectedFields = getDirectlyConnectedFields(nodes, edges, startNode.id);
        console.log(`[${getTimestamp()}] [collectAllFields] Directly connected node IDs: ${Array.from(directlyConnectedNodes).join(', ')}`);
        // Log directly connected fields
        directlyConnectedFields.forEach((fieldNames, nodeId)=>{
            console.log(`[${getTimestamp()}] [collectAllFields] Node ${nodeId} has directly connected fields: ${Array.from(fieldNames).join(', ')}`);
        });
    }
    const allFields = [];
    // Check each node
    nodes.forEach((node)=>{
        console.log(`[${getTimestamp()}] [collectAllFields] Examining node: ${node.id} (${node.data.label || "Unnamed"})`);
        // Skip nodes that are not connected to the start node
        if (!connectedNodes.has(node.id)) {
            console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - not connected to start node`);
            return;
        }
        // Skip nodes with no definition or inputs
        if (!node.data?.definition?.inputs) {
            console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - no definition or inputs`);
            return;
        }
        const inputs = node.data.definition.inputs;
        const config = node.data.config || {};
        // Log detailed node information
        console.log(`[${getTimestamp()}] [collectAllFields] Node details:
      - ID: ${node.id}
      - Label: ${node.data.label || "Unnamed"}
      - Type: ${node.data.type}
      - Original Type: ${node.data.originalType}
      - Position: (${node.position.x}, ${node.position.y})
      - Total inputs: ${inputs.length}
      - Config keys: ${Object.keys(config).join(', ') || "none"}
      - Is MCP component: ${isMCPMarketplaceComponent(node) ? "YES" : "NO"}`);
        // Check each input
        inputs.forEach((input)=>{
            console.log(`[${getTimestamp()}] [collectAllFields] Examining input: ${input.name} (${input.display_name || input.name})`);
            // For handle inputs, check if they're connected to other nodes
            let isConnected = false;
            if (input.is_handle) {
                isConnected = isHandleConnected(node.id, input.name, edges);
                // We no longer skip connected handles - we need to include them in the field list
                // but mark them as connected so they don't show up in the execution dialog
                if (isConnected) {
                    console.log(`[${getTimestamp()}] [collectAllFields] Handle input ${input.name} is connected, including in field list`);
                } else {
                    console.log(`[${getTimestamp()}] [collectAllFields] Handle input ${input.name} is not connected, including in field list`);
                }
            }
            // Log detailed input information
            console.log(`[${getTimestamp()}] [collectAllFields] Input details:
        - Name: ${input.name}
        - Display Name: ${input.display_name || input.name}
        - Type: ${input.input_type}
        - Required flag: ${input.required === undefined ? "undefined" : input.required}
        - Is handle: ${input.is_handle ? "YES" : "NO"}
        - Is connected: ${isConnected ? "YES" : "NO"}
        - Has info: ${input.info ? "YES" : "NO"}
        - Current config value: ${JSON.stringify(config[input.name])}`);
            // Determine if this field is required
            const required = isFieldRequired(node, input, isConnected);
            // Check if the field has a value
            const isEmpty = isValueEmpty(config[input.name], input.input_type);
            console.log(`[${getTimestamp()}] [collectAllFields] Field ${input.name} details:
        - Required: ${required ? "YES" : "NO"}
        - Is Empty: ${isEmpty ? "YES" : "NO"}
        - Value: ${JSON.stringify(config[input.name])}`);
            // Check if this specific field is directly connected to the Start node
            const isNodeDirectlyConnected = directlyConnectedNodes.has(node.id);
            const isFieldDirectlyConnected = directlyConnectedFields.get(node.id)?.has(input.name) || false;
            const isRequired = input.required !== false; // Consider required unless explicitly marked as optional
            // Log detailed connection information for debugging
            console.log(`[${getTimestamp()}] [collectAllFields] Field ${node.data.label || ""}.${input.name} direct connection check:
        - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
        - Field handle: ${input.name}
        - Field directly connected fields map has node: ${directlyConnectedFields.has(node.id) ? "YES" : "NO"}
        - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}
        - All directly connected fields for this node: ${directlyConnectedFields.get(node.id) ? Array.from(directlyConnectedFields.get(node.id) || []).join(', ') : "none"}`);
            // FIXED: Always include directly connected fields, regardless of whether they're required
            // For other fields, include them if they're required
            if (isRequired || isFieldDirectlyConnected) {
                // Ensure we have valid values for all fields
                const nodeName = node.data.label || "Unknown Node";
                const fieldName = input.name || "unnamed_field";
                const displayName = input.display_name || input.name || "Unnamed Field";
                const inputType = input.input_type || "string";
                console.log(`[${getTimestamp()}] [collectAllFields] Adding field with validated properties:
          - Node Name: ${nodeName}
          - Field Name: ${fieldName}
          - Display Name: ${displayName}
          - Input Type: ${inputType}
          - Required: ${isRequired ? "YES" : "NO"}
          - Directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}
          - Is Empty: ${isEmpty ? "YES" : "NO"}`);
                // Extract schema information for JSON objects
                let schema = undefined;
                if (input.input_type === 'json' || input.input_type === 'object' || input.input_type === 'dict') {
                    // Check if this is an MCP component with schema information
                    if (node.data.definition?.mcp_info?.input_schema?.properties?.[input.name]) {
                        // Get schema from MCP input schema
                        const mcpSchema = node.data.definition.mcp_info.input_schema.properties[input.name];
                        schema = {
                            type: mcpSchema.type,
                            properties: mcpSchema.properties || {},
                            required: mcpSchema.required || []
                        };
                        console.log(`[${getTimestamp()}] [collectAllFields] Found MCP schema for field ${input.name}:`, schema);
                    } else if (input.properties) {
                        // Get schema from input properties
                        schema = {
                            type: 'object',
                            properties: input.properties,
                            required: input.required_keys || []
                        };
                        console.log(`[${getTimestamp()}] [collectAllFields] Using input properties as schema for field ${input.name}:`, schema);
                    } else if (input.name === 'keywords') {
                        // Special case for keywords field
                        schema = {
                            type: 'object',
                            properties: {
                                time: {
                                    type: 'string',
                                    description: 'Time for the script'
                                },
                                objective: {
                                    type: 'string',
                                    description: 'Objective of the script'
                                },
                                audience: {
                                    type: 'string',
                                    description: 'Audience for the script'
                                },
                                gender: {
                                    type: 'string',
                                    description: 'Gender for the script'
                                },
                                tone: {
                                    type: 'string',
                                    description: 'Tone of the script'
                                },
                                speakers: {
                                    type: 'string',
                                    description: 'Speaker in the script'
                                }
                            },
                            required: []
                        };
                        console.log(`[${getTimestamp()}] [collectAllFields] Using predefined schema for keywords field:`, schema);
                    }
                }
                console.log(`[${getTimestamp()}] [collectAllFields] Field ${nodeName}.${fieldName} connection status:
          - Node connected to Start: ${connectedNodes.has(node.id) ? "YES" : "NO"}
          - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
          - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}`);
                allFields.push({
                    nodeId: node.id,
                    nodeName: nodeName,
                    name: fieldName,
                    displayName: displayName,
                    info: input.info || undefined,
                    inputType: inputType,
                    connected_to_start: connectedNodes.has(node.id),
                    directly_connected_to_start: isFieldDirectlyConnected,
                    required: isRequired || isFieldDirectlyConnected,
                    isEmpty: isEmpty,
                    currentValue: isEmpty ? undefined : unwrapDualPurposeValue(config[input.name]),
                    options: input.options,
                    schema: schema,
                    // Add handle connection properties
                    is_handle: input.is_handle || false,
                    is_connected: isConnected
                });
                console.log(`[${getTimestamp()}] [collectAllFields] Added field ${node.data.label || ""}.${input.name} to all fields list`);
            } else {
                console.log(`[${getTimestamp()}] [collectAllFields] Skipping field ${input.name} - not required and not directly connected to Start node`);
            }
        });
    });
    console.log(`[${getTimestamp()}] [collectAllFields] ========== ALL FIELDS COLLECTION COMPLETE ==========`);
    console.log(`[${getTimestamp()}] [collectAllFields] Total fields found: ${allFields.length}`);
    return allFields;
}
function collectMissingRequiredFields(nodes, connectedNodes, edges = []) {
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] ========== STARTING MISSING FIELDS COLLECTION ==========`);
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Total nodes: ${nodes.length}, Connected nodes: ${connectedNodes.size}, Edges: ${edges.length}`);
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Connected node IDs: ${Array.from(connectedNodes).join(', ')}`);
    // Find the StartNode
    const startNode = nodes.find((node)=>node.data.originalType === "StartNode");
    // Get directly connected nodes and fields if StartNode exists
    let directlyConnectedNodes = new Set();
    let directlyConnectedFields = new Map();
    if (startNode) {
        // Import the functions from utils
        const { getDirectlyConnectedNodes, getDirectlyConnectedFields } = __turbopack_context__.r("[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript)");
        directlyConnectedNodes = getDirectlyConnectedNodes(nodes, edges, startNode.id);
        directlyConnectedFields = getDirectlyConnectedFields(nodes, edges, startNode.id);
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Directly connected node IDs: ${Array.from(directlyConnectedNodes).join(', ')}`);
        // Log directly connected fields
        directlyConnectedFields.forEach((fieldNames, nodeId)=>{
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Node ${nodeId} has directly connected fields: ${Array.from(fieldNames).join(', ')}`);
        });
    }
    const missingFields = [];
    // Check each node
    nodes.forEach((node)=>{
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Examining node: ${node.id} (${node.data.label || "Unnamed"})`);
        // Skip nodes that are not connected to the start node
        if (!connectedNodes.has(node.id)) {
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - not connected to start node`);
            return;
        }
        // Skip nodes with no definition or inputs
        if (!node.data?.definition?.inputs) {
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - no definition or inputs`);
            return;
        }
        const inputs = node.data.definition.inputs;
        const config = node.data.config || {};
        // Log detailed node information
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Node details:
      - ID: ${node.id}
      - Label: ${node.data.label || "Unnamed"}
      - Type: ${node.data.type}
      - Original Type: ${node.data.originalType}
      - Position: (${node.position.x}, ${node.position.y})
      - Total inputs: ${inputs.length}
      - Config keys: ${Object.keys(config).join(', ') || "none"}
      - Is MCP component: ${isMCPMarketplaceComponent(node) ? "YES" : "NO"}`);
        // Check each input
        inputs.forEach((input)=>{
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Examining input: ${input.name} (${input.display_name || input.name})`);
            // Check if this is a handle input and if it's connected
            let isConnected = false;
            if (input.is_handle) {
                isConnected = isHandleConnected(node.id, input.name, edges);
                if (isConnected) {
                    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Handle input ${input.name} is connected`);
                } else {
                    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Handle input ${input.name} is not connected, checking if it needs a value`);
                }
            }
            // Log detailed input information
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Input details:
        - Name: ${input.name}
        - Display Name: ${input.display_name || input.name}
        - Type: ${input.input_type}
        - Required flag: ${input.required === undefined ? "undefined" : input.required}
        - Is handle: ${input.is_handle ? "YES" : "NO"}
        - Is connected: ${isConnected ? "YES" : "NO"}
        - Has info: ${input.info ? "YES" : "NO"}
        - Current config value: ${JSON.stringify(config[input.name])}`);
            // Determine if this field is required, passing the connection status for handle inputs
            const required = isFieldRequired(node, input, isConnected);
            // Check if the field has a value
            const isEmpty = isValueEmpty(config[input.name], input.input_type);
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} validation result:
        - Required: ${required ? "YES" : "NO"}
        - Is Empty: ${isEmpty ? "YES" : "NO"}
        - Value: ${JSON.stringify(config[input.name])}`);
            // Check if this specific field is directly connected to the Start node
            const isNodeDirectlyConnected = directlyConnectedNodes.has(node.id);
            const isFieldDirectlyConnected = directlyConnectedFields.get(node.id)?.has(input.name) || false;
            // Log detailed connection information for debugging
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${node.data.label || ""}.${input.name} direct connection check:
        - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
        - Field handle: ${input.name}
        - Field directly connected fields map has node: ${directlyConnectedFields.has(node.id) ? "YES" : "NO"}
        - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}
        - All directly connected fields for this node: ${directlyConnectedFields.get(node.id) ? Array.from(directlyConnectedFields.get(node.id) || []).join(', ') : "none"}`);
            // FIXED: Add fields that are either (required AND empty) OR directly connected to the Start node
            // This ensures that fields with direct connections from Start node are always included
            if (required && isEmpty || isFieldDirectlyConnected) {
                // Ensure we have valid values for all fields
                const nodeName = node.data.label || "Unknown Node";
                const fieldName = input.name || "unnamed_field";
                const displayName = input.display_name || input.name || "Unnamed Field";
                const inputType = input.input_type || "string";
                if (required && isEmpty) {
                    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] FOUND MISSING REQUIRED FIELD: ${node.data.label || ""}.${input.name}`);
                } else if (isFieldDirectlyConnected) {
                    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] FOUND DIRECTLY CONNECTED FIELD: ${node.data.label || ""}.${input.name}`);
                }
                console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Adding field with validated properties:
          - Node Name: ${nodeName}
          - Field Name: ${fieldName}
          - Display Name: ${displayName}
          - Input Type: ${inputType}
          - Is Empty: ${isEmpty ? "YES" : "NO"}
          - Current Value: ${JSON.stringify(config[input.name])}
          - Directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}`);
                // Extract schema information for JSON objects
                let schema = undefined;
                if (input.input_type === 'json' || input.input_type === 'object' || input.input_type === 'dict') {
                    // Check if this is an MCP component with schema information
                    if (node.data.definition?.mcp_info?.input_schema?.properties?.[input.name]) {
                        // Get schema from MCP input schema
                        const mcpSchema = node.data.definition.mcp_info.input_schema.properties[input.name];
                        schema = {
                            type: mcpSchema.type,
                            properties: mcpSchema.properties || {},
                            required: mcpSchema.required || []
                        };
                        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Found MCP schema for field ${input.name}:`, schema);
                    } else if (input.properties) {
                        // Get schema from input properties
                        schema = {
                            type: 'object',
                            properties: input.properties,
                            required: input.required_keys || []
                        };
                        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Using input properties as schema for field ${input.name}:`, schema);
                    } else if (input.name === 'keywords') {
                        // Special case for keywords field
                        schema = {
                            type: 'object',
                            properties: {
                                time: {
                                    type: 'string',
                                    description: 'Time for the script'
                                },
                                objective: {
                                    type: 'string',
                                    description: 'Objective of the script'
                                },
                                audience: {
                                    type: 'string',
                                    description: 'Audience for the script'
                                },
                                gender: {
                                    type: 'string',
                                    description: 'Gender for the script'
                                },
                                tone: {
                                    type: 'string',
                                    description: 'Tone of the script'
                                },
                                speakers: {
                                    type: 'string',
                                    description: 'Speaker in the script'
                                }
                            },
                            required: []
                        };
                        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Using predefined schema for keywords field:`, schema);
                    }
                }
                console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${nodeName}.${fieldName} connection status:
          - Node connected to Start: ${connectedNodes.has(node.id) ? "YES" : "NO"}
          - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
          - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}`);
                missingFields.push({
                    nodeId: node.id,
                    nodeName: nodeName,
                    name: fieldName,
                    displayName: displayName,
                    info: input.info || undefined,
                    inputType: inputType,
                    // Add handle connection properties
                    is_handle: input.is_handle || false,
                    is_connected: isConnected,
                    connected_to_start: connectedNodes.has(node.id),
                    directly_connected_to_start: isFieldDirectlyConnected,
                    required: required || isFieldDirectlyConnected,
                    isEmpty: isEmpty,
                    currentValue: isEmpty ? undefined : unwrapDualPurposeValue(config[input.name]),
                    schema: schema,
                    options: input.options
                });
            } else if (required && !isEmpty) {
                console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} is required but already has a value, skipping`);
            } else {
                console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} is not required, skipping`);
            }
        });
    });
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] ========== MISSING FIELDS COLLECTION COMPLETE ==========`);
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Total missing fields found: ${missingFields.length}`);
    if (missingFields.length > 0) {
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Missing fields summary:`);
        missingFields.forEach((field, index)=>{
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);
        });
    }
    return missingFields;
}
}}),
"[project]/src/lib/validation/workflowValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>validate),
    "validateWorkflow": (()=>validateWorkflow),
    "validateWorkflowStructure": (()=>validateWorkflowStructure)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$nodeValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/nodeValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$edgeValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/edgeValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$connectivityValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/connectivityValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$fieldValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/fieldValidation.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
function validateWorkflowStructure(workflowData) {
    const errors = [];
    const warnings = [];
    const infos = [];
    // Check if workflow is a valid object
    if (!workflowData || typeof workflowData !== "object") {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_INVALID_JSON, "Workflow must be a valid JSON object"));
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    // Check if nodes array exists
    if (!workflowData.nodes || !Array.isArray(workflowData.nodes)) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_NODES, 'Workflow must contain a "nodes" array'));
    }
    // Check if edges array exists
    if (!workflowData.edges || !Array.isArray(workflowData.edges)) {
        errors.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_EDGES, 'Workflow must contain an "edges" array'));
    }
    // Check workflow_name if present
    if (workflowData.workflow_name && typeof workflowData.workflow_name !== "string") {
        warnings.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_INVALID_NAME, "Workflow name must be a string", "warning"));
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        infos
    };
}
function validateWorkflow(nodes, edges, options = {
    validateConnectivity: true,
    collectMissingFields: true,
    validateFieldTypes: true,
    validateCycles: true
}) {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [validateWorkflow] Starting workflow validation with ${nodes?.length || 0} nodes and ${edges?.length || 0} edges`);
    // Check if nodes array is valid
    if (!nodes || !Array.isArray(nodes)) {
        console.error(`[${timestamp}] [validateWorkflow] Nodes array is invalid or undefined`);
        return {
            isValid: false,
            errors: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_NODES, "Workflow nodes array is invalid or undefined")
            ],
            warnings: [],
            infos: []
        };
    }
    // Log the nodes array for debugging
    console.log(`[${timestamp}] [validateWorkflow] Nodes array:`, nodes.map((node)=>({
            id: node.id,
            type: node.type,
            dataType: node.data?.type,
            originalType: node.data?.originalType,
            label: node.data?.label
        })));
    // Check if edges array is valid
    if (!edges || !Array.isArray(edges)) {
        console.error(`[${timestamp}] [validateWorkflow] Edges array is invalid or undefined`);
        return {
            isValid: false,
            errors: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_MISSING_EDGES, "Workflow edges array is invalid or undefined")
            ],
            warnings: [],
            infos: []
        };
    }
    // Check if workflow is empty
    if (nodes.length === 0) {
        console.warn(`[${timestamp}] [validateWorkflow] Workflow is empty (no nodes)`);
        return {
            isValid: false,
            errors: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createValidationError"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationErrorCode"].WORKFLOW_EMPTY, "Workflow is empty. Please add at least a Start node.")
            ],
            warnings: [],
            infos: []
        };
    }
    // Log node types for debugging
    console.log(`[${timestamp}] [validateWorkflow] Node types in workflow:`, nodes.map((node)=>({
            id: node.id,
            type: node.type,
            dataType: node.data?.type,
            originalType: node.data?.originalType,
            definitionName: node.data?.definition?.name,
            label: node.data?.label
        })));
    // Step 1: Validate nodes structure
    console.log(`[${timestamp}] [validateWorkflow] Step 1: Validating node structure`);
    const nodeValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$nodeValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateNodes"])(nodes);
    // Step 2: Validate edges structure
    console.log(`[${timestamp}] [validateWorkflow] Step 2: Validating edge structure`);
    const edgeValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$edgeValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateEdges"])(edges, nodes);
    // Combine validation results
    const errors = [
        ...nodeValidation.errors,
        ...edgeValidation.errors
    ];
    const warnings = [
        ...nodeValidation.warnings,
        ...edgeValidation.warnings
    ];
    const infos = [
        ...nodeValidation.infos,
        ...edgeValidation.infos
    ];
    // If there are structural errors, return early
    if (errors.length > 0) {
        console.warn(`[${timestamp}] [validateWorkflow] Found ${errors.length} structural errors, returning early`);
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    // Step 3: Validate StartNode presence
    console.log(`[${timestamp}] [validateWorkflow] Step 3: Validating StartNode presence`);
    const startNodeValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$connectivityValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateStartNode"])(nodes);
    errors.push(...startNodeValidation.errors);
    warnings.push(...startNodeValidation.warnings);
    infos.push(...startNodeValidation.infos);
    // If there's no StartNode, return early
    if (!startNodeValidation.isValid) {
        console.warn(`[${timestamp}] [validateWorkflow] No StartNode found, validation failed`);
        return {
            isValid: false,
            errors,
            warnings,
            infos
        };
    }
    console.log(`[${timestamp}] [validateWorkflow] Found StartNode with ID: ${startNodeValidation.startNodeId}`);
    // Step 4: Validate connectivity if requested
    let connectedNodes;
    if (options.validateConnectivity) {
        console.log(`[${timestamp}] [validateWorkflow] Step 4: Validating node connectivity from StartNode`);
        const connectivityValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$connectivityValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateConnectivity"])(nodes, edges, startNodeValidation.startNodeId);
        errors.push(...connectivityValidation.errors);
        warnings.push(...connectivityValidation.warnings);
        infos.push(...connectivityValidation.infos);
        connectedNodes = connectivityValidation.connectedNodes;
        if (connectedNodes) {
            console.log(`[${timestamp}] [validateWorkflow] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);
        } else {
            console.warn(`[${timestamp}] [validateWorkflow] No connected nodes found from StartNode`);
        }
    } else {
        console.log(`[${timestamp}] [validateWorkflow] Skipping connectivity validation (disabled in options)`);
    }
    // Step 5: Detect cycles if requested
    if (options.validateCycles) {
        console.log(`[${timestamp}] [validateWorkflow] Step 5: Detecting cycles in workflow`);
        const cycleValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$connectivityValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["detectCyclesInWorkflow"])(nodes, edges);
        errors.push(...cycleValidation.errors);
        warnings.push(...cycleValidation.warnings);
        infos.push(...cycleValidation.infos);
        if (cycleValidation.warnings.length > 0) {
            console.warn(`[${timestamp}] [validateWorkflow] Found cycles in workflow`);
        } else {
            console.log(`[${timestamp}] [validateWorkflow] No cycles detected in workflow`);
        }
    } else {
        console.log(`[${timestamp}] [validateWorkflow] Skipping cycle detection (disabled in options)`);
    }
    // Step 6: Collect missing required fields if requested
    let missingFields = [];
    if (options.collectMissingFields && connectedNodes) {
        console.log(`[${timestamp}] [validateWorkflow] Step 6: Collecting missing required fields from ${connectedNodes.size} connected nodes`);
        // Pass the edges to the collectMissingRequiredFields function to check if handle inputs are connected
        missingFields = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$fieldValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectMissingRequiredFields"])(nodes, connectedNodes, edges);
        if (missingFields.length > 0) {
            console.log(`[${timestamp}] [validateWorkflow] Found ${missingFields.length} missing required fields`);
            // Log details of missing fields for debugging
            missingFields.forEach((field, index)=>{
                console.log(`[${timestamp}] [validateWorkflow] Missing field ${index + 1}: Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);
            });
        } else {
            console.log(`[${timestamp}] [validateWorkflow] No missing required fields found`);
        }
    } else {
        console.log(`[${timestamp}] [validateWorkflow] Skipping missing fields collection (disabled in options or no connected nodes)`);
    }
    // Final validation result
    const isValid = errors.length === 0;
    console.log(`[${timestamp}] [validateWorkflow] Validation complete. Result: ${isValid ? "VALID" : "INVALID"}`);
    console.log(`[${timestamp}] [validateWorkflow] Errors: ${errors.length}, Warnings: ${warnings.length}, Missing Fields: ${missingFields.length}`);
    return {
        isValid,
        errors,
        warnings,
        infos,
        missingFields,
        startNodeId: startNodeValidation.startNodeId,
        connectedNodes
    };
}
function validate(workflowData, options) {
    // First validate the basic structure
    const structureValidation = validateWorkflowStructure(workflowData);
    if (!structureValidation.isValid) {
        return structureValidation;
    }
    // Extract nodes and edges
    const nodes = workflowData.nodes || [];
    const edges = workflowData.edges || [];
    // Validate the workflow
    return validateWorkflow(nodes, edges, options);
}
}}),
"[project]/src/lib/validation/smartValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "convertToBackendFormat": (()=>convertToBackendFormat),
    "validateWorkflowBeforeExecution": (()=>validateWorkflowBeforeExecution),
    "validateWorkflowBeforeSave": (()=>validateWorkflowBeforeSave),
    "validateWorkflowDuringEditing": (()=>validateWorkflowDuringEditing),
    "validateWorkflowSmart": (()=>validateWorkflowSmart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$workflowValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/workflowValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$features$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/features.ts [app-ssr] (ecmascript)");
;
;
function convertToBackendFormat(result) {
    // Convert missing fields to backend format
    const missingFields = result.missingFields?.map((field)=>({
            node_id: field.nodeId,
            node_name: field.nodeName,
            name: field.name,
            display_name: field.displayName,
            info: field.info,
            input_type: field.inputType
        }));
    // If there are errors, combine them into a single error message
    let error;
    if (result.errors.length > 0) {
        error = result.errors.map((err)=>err.message).join("; ");
    }
    return {
        is_valid: result.isValid,
        missing_fields: missingFields,
        error
    };
}
async function validateWorkflowSmart(nodes, edges, options) {
    // Always use frontend validation since backend validation is disabled
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$workflowValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflow"])(nodes, edges, options);
}
async function validateWorkflowBeforeSave(nodes, edges) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$features$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FEATURES"].VALIDATE_ON_SAVE) {
        // If validation on save is disabled, return a valid result
        return {
            isValid: true,
            errors: [],
            warnings: [],
            infos: []
        };
    }
    const options = {
        validateConnectivity: true,
        collectMissingFields: false,
        validateFieldTypes: true,
        validateCycles: true
    };
    return validateWorkflowSmart(nodes, edges, options);
}
async function validateWorkflowBeforeExecution(nodes, edges) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$features$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FEATURES"].VALIDATE_ON_EXECUTE) {
        // If validation on execute is disabled, return a valid result
        return {
            isValid: true,
            errors: [],
            warnings: [],
            infos: []
        };
    }
    const options = {
        validateConnectivity: true,
        collectMissingFields: true,
        validateFieldTypes: true,
        validateCycles: true
    };
    return validateWorkflowSmart(nodes, edges, options);
}
async function validateWorkflowDuringEditing(nodes, edges) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$features$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FEATURES"].VALIDATE_ON_EDIT) {
        // If validation during editing is disabled, return a valid result
        return {
            isValid: true,
            errors: [],
            warnings: [],
            infos: []
        };
    }
    const options = {
        validateConnectivity: false,
        collectMissingFields: false,
        validateFieldTypes: true,
        validateCycles: false
    };
    return validateWorkflowSmart(nodes, edges, options);
}
}}),
"[project]/src/lib/validation/frontendValidationAdapter.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "validateWorkflowFrontend": (()=>validateWorkflowFrontend)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/smartValidation.ts [app-ssr] (ecmascript)");
;
;
async function validateWorkflowFrontend(nodes, edges) {
    try {
        // Use our frontend validation implementation
        const validationResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflowBeforeExecution"])(nodes, edges);
        // Convert to backend format
        const backendFormat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertToBackendFormat"])(validationResult);
        return {
            is_valid: backendFormat.is_valid,
            missing_fields: backendFormat.missing_fields,
            error: backendFormat.error
        };
    } catch (error) {
        console.error("Error in frontend validation:", error);
        return {
            is_valid: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
}}),
"[project]/src/lib/workflow-api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/workflow-api.ts
__turbopack_context__.s({
    "API_BASE_URL": (()=>API_BASE_URL),
    "executeWorkflowWithValues": (()=>executeWorkflowWithValues),
    "validateWorkflow": (()=>validateWorkflow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$frontendValidationAdapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/frontendValidationAdapter.ts [app-ssr] (ecmascript)");
;
const API_BASE_URL = ("TURBOPACK compile-time value", "https://app-dev.rapidinnovation.dev/api/v1");
async function validateWorkflow(nodes, edges) {
    // Use frontend validation instead of backend API
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$frontendValidationAdapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateWorkflowFrontend"])(nodes, edges);
}
async function executeWorkflowWithValues(nodes, edges, fieldValues, workflow_id) {
    try {
        // Find the StartNode first
        const startNode = nodes.find((node)=>node.data.originalType === "StartNode");
        // Ensure the StartNode has a config object with collected_parameters
        if (startNode) {
            console.log("Found StartNode before updating:", startNode);
            // Ensure the StartNode has a config object
            if (!startNode.data.config) {
                startNode.data.config = {};
            }
            // Ensure the config has a collected_parameters object
            if (!startNode.data.config.collected_parameters) {
                startNode.data.config.collected_parameters = {};
            }
            // Ensure all parameters in collected_parameters have the required property set
            // This is critical for pre-built workflows where the required property might not be set
            if (startNode.data.config.collected_parameters) {
                Object.keys(startNode.data.config.collected_parameters).forEach((paramId)=>{
                    const param = startNode.data.config.collected_parameters[paramId];
                    // If required is undefined, set it to true (consider required unless explicitly false)
                    if (param.required === undefined) {
                        console.log(`Setting required=true for parameter ${paramId} in StartNode`);
                        param.required = true;
                    }
                });
            }
            console.log("StartNode config after ensuring structure:", startNode.data.config);
        } else {
            console.warn("No StartNode found in the workflow, this may cause issues");
        }
        // If fieldValues is provided, update the node configs
        if (fieldValues) {
            // Process field values to update individual nodes
            const nodeFieldValues = {};
            // Group field values by node ID
            Object.entries(fieldValues).forEach(([fieldId, value])=>{
                // Extract node ID and field name from the field ID
                const [nodeId, ...fieldNameParts] = fieldId.split("_");
                const fieldName = fieldNameParts.join("_");
                // Initialize node entry if it doesn't exist
                if (!nodeFieldValues[nodeId]) {
                    nodeFieldValues[nodeId] = {};
                }
                // Add field value to the node
                nodeFieldValues[nodeId][fieldName] = value;
            });
            // Update nodes with their respective field values
            nodes = nodes.map((node)=>{
                if (nodeFieldValues[node.id]) {
                    console.log(`Updating node ${node.id} config with values:`, nodeFieldValues[node.id]);
                    // Create a deep copy of the node to avoid reference issues
                    const updatedNode = JSON.parse(JSON.stringify(node));
                    // Ensure config exists
                    if (!updatedNode.data.config) {
                        updatedNode.data.config = {};
                    }
                    // Update the config with the new values
                    Object.entries(nodeFieldValues[node.id]).forEach(([key, value])=>{
                        updatedNode.data.config[key] = value;
                    });
                    console.log(`Node ${node.id} config after update:`, updatedNode.data.config);
                    return updatedNode;
                }
                return node;
            });
            // Update the StartNode with collected parameters
            if (startNode) {
                // Create a deep copy of the collected parameters or initialize if not exists
                const collectedParameters = {
                    ...startNode.data.config?.collected_parameters || {}
                };
                // Process each field value directly
                Object.entries(fieldValues).forEach(([fieldId, fieldValue])=>{
                    // Skip if this is not a valid field ID format
                    if (!fieldId.includes("_")) return;
                    // Extract node ID and field name
                    const [nodeId, ...fieldNameParts] = fieldId.split("_");
                    const fieldName = fieldNameParts.join("_");
                    // Get the node for additional metadata
                    const node = nodes.find((n)=>n.id === nodeId);
                    if (!node) return;
                    // Process value if it's a JSON string
                    let processedValue = fieldValue;
                    if (typeof fieldValue === "string") {
                        // Check if the value looks like JSON
                        if (fieldValue.trim().startsWith("{") || fieldValue.trim().startsWith("[")) {
                            try {
                                // Parse the JSON string to an object or array
                                processedValue = JSON.parse(fieldValue);
                                console.log(`Successfully parsed JSON for ${fieldName}:`, processedValue);
                            } catch (e) {
                                console.error(`Failed to parse JSON for ${fieldName} field:`, e);
                            }
                        }
                    }
                    // Check if this parameter is connected to the StartNode
                    const isConnectedToStartNode = edges.some((edge)=>{
                        // Check if this edge connects the StartNode to this node
                        const isFromStartNode = edge.source === startNode.id;
                        const isToThisNode = edge.target === nodeId;
                        // Check if the target handle matches this field
                        let matchesField = false;
                        if (edge.targetHandle) {
                            // Handle different formats of target handles
                            if (edge.targetHandle === fieldName) {
                                matchesField = true;
                            } else if (edge.targetHandle === `input_${fieldName}`) {
                                matchesField = true;
                            } else if (edge.targetHandle.includes(fieldName)) {
                                matchesField = true;
                            }
                        }
                        return isFromStartNode && isToThisNode && matchesField;
                    });
                    // Store the parameter with full metadata
                    collectedParameters[fieldId] = {
                        node_id: nodeId,
                        node_name: node.data.label || "Unknown Node",
                        input_name: fieldName,
                        value: processedValue,
                        connected_to_start: isConnectedToStartNode
                    };
                });
                // Update the StartNode config
                startNode.data.config = {
                    ...startNode.data.config,
                    collected_parameters: collectedParameters
                };
                console.log("StartNode config after updating collected parameters:", startNode.data.config);
                // Also update the global window.startNodeCollectedParameters for future reference
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
            }
        }
        // Filter out nodes that are not connected to the StartNode
        let filteredNodes = nodes;
        let filteredEdges = edges;
        if (startNode) {
            console.log("[WORKFLOW EXECUTE] Filtering out unconnected nodes before execution");
            // Import the getConnectedNodes function from validation utils
            const { getConnectedNodes } = await __turbopack_context__.r("[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            // Find all nodes connected to the StartNode
            const connectedNodes = getConnectedNodes(nodes, edges, startNode.id);
            console.log(`[WORKFLOW EXECUTE] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);
            // Check if there are any disconnected nodes
            const disconnectedNodesCount = nodes.length - connectedNodes.size;
            if (disconnectedNodesCount > 0) {
                console.log(`[WORKFLOW EXECUTE] Found ${disconnectedNodesCount} disconnected nodes that will be excluded from execution`);
                // Filter nodes to include only those connected to the StartNode
                filteredNodes = nodes.filter((node)=>connectedNodes.has(node.id));
                // Filter edges to include only those connecting filtered nodes
                filteredEdges = edges.filter((edge)=>connectedNodes.has(edge.source) && connectedNodes.has(edge.target));
                console.log(`[WORKFLOW EXECUTE] Filtered workflow contains ${filteredNodes.length} nodes and ${filteredEdges.length} edges`);
                // Import toast dynamically to avoid server-side rendering issues
                try {
                    // Use dynamic import for toast
                    const { toast } = await __turbopack_context__.r("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                    // Show a warning toast notification
                    toast.warning(`${disconnectedNodesCount} unconnected ${disconnectedNodesCount === 1 ? 'node has' : 'nodes have'} been excluded from execution.`, {
                        description: "Only nodes connected to the Start node are included in the workflow execution.",
                        duration: 5000
                    });
                } catch (error) {
                    console.error("Failed to show toast notification:", error);
                }
            }
        }
        // Log the node configs to help with debugging
        console.log("Executing workflow with filtered nodes:", filteredNodes);
        console.log("StartNode config before execution:", startNode?.data.config);
        // Log specific node configs that might have updated values
        filteredNodes.forEach((node)=>{
            if (node.data.originalType === "ScriptGenerateNode" || node.id.includes("generate-script")) {
                console.log(`Script Generate Node ${node.id} config:`, node.data.config);
            }
        });
        const response = await fetch(`${API_BASE_URL}/execute`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                nodes: filteredNodes,
                edges: filteredEdges,
                workflow_id
            })
        });
        if (!response.ok) {
            throw new Error(`Error executing workflow: ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error("Error executing workflow:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
}}),
"[project]/src/lib/validation/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Export all validation types and functions
__turbopack_context__.s({});
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/src/lib/validation/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/errors.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$nodeValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/nodeValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$edgeValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/edgeValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$connectivityValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/connectivityValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$fieldValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/fieldValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$workflowValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/workflowValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$smartValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/smartValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$frontendValidationAdapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/frontendValidationAdapter.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/validation/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/approvalUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions for handling workflow approval events
 * This centralizes the approval event dispatching and tracking to prevent duplicate events
 */ // Add global type definitions for our debugging variables
__turbopack_context__.s({
    "clearAllApprovalEvents": (()=>clearAllApprovalEvents),
    "clearApprovalEvent": (()=>clearApprovalEvent),
    "dispatchApprovalNeededEvent": (()=>dispatchApprovalNeededEvent),
    "hasApprovalEventBeenDispatched": (()=>hasApprovalEventBeenDispatched)
});
// Initialize the approval event history array
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
// Track already dispatched approval events to prevent duplicates
const dispatchedApprovalEvents = new Set();
function dispatchApprovalNeededEvent(correlationId, nodeId, nodeName) {
    // Skip if any parameter is missing or invalid
    if (!correlationId || !nodeId || nodeId === "unknown") {
        console.log(`Skipping approval event due to invalid parameters: correlationId=${correlationId}, nodeId=${nodeId}`);
        return;
    }
    // Create a unique key for this approval request
    const approvalKey = `${correlationId}_${nodeId}`;
    // Get the current timestamp
    const currentTimestamp = Date.now();
    // Add to approval history for debugging
    if (window._approvalEventHistory) {
        window._approvalEventHistory.push({
            correlationId,
            nodeId,
            nodeName: nodeName || nodeId,
            timestamp: currentTimestamp,
            status: 'requested'
        });
        // Keep only the last 10 events
        if (window._approvalEventHistory.length > 10) {
            window._approvalEventHistory = window._approvalEventHistory.slice(-10);
        }
    }
    // Check if we've already dispatched this approval
    // Only dispatch if:
    // 1. We haven't seen this exact approval key before, OR
    // 2. It's been more than 5 seconds since the last dispatch (to handle UI issues)
    const shouldDispatch = !dispatchedApprovalEvents.has(approvalKey) || currentTimestamp - (window._lastApprovalTimestamp || 0) > 5000;
    if (shouldDispatch) {
        console.log(`Dispatching approval needed event for node ${nodeName || nodeId} (${nodeId})`);
        // Add to tracking set
        dispatchedApprovalEvents.add(approvalKey);
        // Store the last approval timestamp in a global variable for debugging
        window._lastApprovalTimestamp = currentTimestamp;
        // Dispatch the event with additional metadata
        const approvalEvent = new CustomEvent("workflow-approval-needed", {
            detail: {
                correlationId,
                nodeId,
                nodeName: nodeName || nodeId,
                timestamp: currentTimestamp,
                approvalKey
            }
        });
        window.dispatchEvent(approvalEvent);
        // Also set a flag on the window object that can be checked for debugging
        window._pendingApproval = {
            correlationId,
            nodeId,
            nodeName: nodeName || nodeId,
            timestamp: currentTimestamp
        };
        // Force a UI update
        setTimeout(()=>{
            window.dispatchEvent(new CustomEvent('approval-ui-update'));
        }, 200);
    } else {
        console.log(`Skipping duplicate approval event for node ${nodeName || nodeId} (${nodeId})`);
    }
}
function clearApprovalEvent(correlationId, nodeId) {
    const approvalKey = `${correlationId}_${nodeId}`;
    dispatchedApprovalEvents.delete(approvalKey);
    // Add to approval history for debugging
    if (window._approvalEventHistory) {
        window._approvalEventHistory.push({
            correlationId,
            nodeId,
            nodeName: nodeId,
            timestamp: Date.now(),
            status: 'cleared'
        });
        // Keep only the last 10 events
        if (window._approvalEventHistory.length > 10) {
            window._approvalEventHistory = window._approvalEventHistory.slice(-10);
        }
    }
    console.log(`Cleared approval event for node ${nodeId} with correlation ID ${correlationId}`);
}
function clearAllApprovalEvents() {
    dispatchedApprovalEvents.clear();
    // Add to approval history for debugging
    if (window._approvalEventHistory) {
        window._approvalEventHistory.push({
            correlationId: 'all',
            nodeId: 'all',
            nodeName: 'all',
            timestamp: Date.now(),
            status: 'cleared_all'
        });
        // Keep only the last 10 events
        if (window._approvalEventHistory.length > 10) {
            window._approvalEventHistory = window._approvalEventHistory.slice(-10);
        }
    }
    // Clear the pending approval flag
    window._pendingApproval = undefined;
    console.log("Cleared all approval events");
}
function hasApprovalEventBeenDispatched(correlationId, nodeId) {
    const approvalKey = `${correlationId}_${nodeId}`;
    return dispatchedApprovalEvents.has(approvalKey);
}
}}),
"[project]/src/lib/mcp_marketplace_transform.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions to transform MCP Marketplace components for the orchestration service
 */ __turbopack_context__.s({
    "fixMCPMarketplaceNode": (()=>fixMCPMarketplaceNode),
    "transformWorkflowForOrchestration": (()=>transformWorkflowForOrchestration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$mcpToolsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/mcpToolsStore.ts [app-ssr] (ecmascript)");
;
function transformWorkflowForOrchestration(nodes, edges) {
    // Create deep copies to avoid modifying the originals
    const transformedNodes = JSON.parse(JSON.stringify(nodes));
    const transformedEdges = JSON.parse(JSON.stringify(edges));
    // Fix each node
    for(let i = 0; i < transformedNodes.length; i++){
        transformedNodes[i] = fixMCPMarketplaceNode(transformedNodes[i]);
    }
    return {
        nodes: transformedNodes,
        edges: transformedEdges
    };
}
function fixMCPMarketplaceNode(node) {
    // Create a deep copy to avoid modifying the original
    const fixedNode = JSON.parse(JSON.stringify(node));
    // Check if this is an MCP Marketplace component
    const nodeData = fixedNode.data;
    const nodeType = nodeData?.type;
    const originalType = nodeData?.originalType;
    const isMCPMarketplace = nodeType === "mcp" || nodeType === "MCPMarketplaceComponent" || originalType && originalType.includes("mcp_");
    if (!isMCPMarketplace) {
        return fixedNode;
    }
    // Get the node definition and config
    const definition = nodeData?.definition;
    const config = nodeData?.config || {};
    // Extract input values
    const inputValues = {};
    // PRIORITY 1: Get values from the component state store (highest priority)
    // These are the most up-to-date values from the UI
    const stateStore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$mcpToolsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useComponentStateStore"].getState();
    const nodeState = stateStore.nodes[node.id] || {};
    // Check if there are any values in the component state
    if ("TURBOPACK compile-time truthy", 1) {
        // Get values from the config in the component state
        const stateConfig = nodeState.config || {};
        for (const [key, value] of Object.entries(stateConfig)){
            if (value !== null && value !== undefined && value !== "") {
                inputValues[key] = value;
            }
        }
        // Also check for direct values in the component state
        for (const [key, value] of Object.entries(nodeState)){
            if (key !== "config" && value !== null && value !== undefined && value !== "") {
                inputValues[key] = value;
            }
        }
    }
    // PRIORITY 2: Get values from the node data config
    // These are values that might have been set programmatically
    if (node.data.config) {
        for (const [key, value] of Object.entries(node.data.config)){
            // Skip special keys that are not user inputs and skip the inputs array
            if (![
                "mode",
                "selected_tool_name",
                "stdio_command",
                "tool_args",
                "node_id",
                "_internal_state",
                "inputs"
            ].includes(key) && value !== null && value !== undefined && value !== "") {
                // Only add if not already set from component state
                if (!(key in inputValues)) {
                    inputValues[key] = value;
                }
            }
        }
    }
    // PRIORITY 3: Get values from the inputs in the definition
    // These are the default values defined in the component
    if (definition && definition.inputs) {
        const inputs = definition.inputs;
        for (const input of inputs){
            const name = input.name;
            const value = input.value;
            // Skip empty values and special handle inputs
            if (value === null || value === undefined || value === "" || typeof value === "object" && Object.keys(value).length === 0 || name.endsWith("_handle")) {
                continue;
            }
            // Only add if not already set from higher priority sources
            if (!(name in inputValues)) {
                inputValues[name] = value;
            }
        }
    }
    // PRIORITY 4: Get values from tool_args if it exists
    // These are values that might have been set for MCP Tools
    if (config.tool_args) {
        const toolArgs = config.tool_args;
        for (const [name, value] of Object.entries(toolArgs)){
            if (value !== null && value !== undefined && value !== "") {
                // Only add if not already set from higher priority sources
                if (!(name in inputValues)) {
                    inputValues[name] = value;
                }
            }
        }
    }
    // Log the values for debugging
    console.log(`[MCP Transform] Node ID: ${node.id}`);
    console.log(`[MCP Transform] Original type: ${originalType}`);
    console.log(`[MCP Transform] Node type: ${nodeType}`);
    console.log(`[MCP Transform] Is MCP Marketplace: ${isMCPMarketplace}`);
    console.log(`[MCP Transform] Final input values:`, inputValues);
    // Special handling for API request nodes to ensure method is preserved
    if (nodeData.originalType === "ApiRequestNode" || nodeData.type === "ApiRequestNode") {
        console.log(`[MCP Transform] Preserving API request node configuration`);
        // Make sure to preserve the method value from the original config
        if (nodeData.config && nodeData.config.method) {
            console.log(`[MCP Transform] Preserving method ${nodeData.config.method} for API request node`);
            inputValues.method = nodeData.config.method;
        }
    }
    // Create a new config with the input values directly
    nodeData.config = inputValues;
    return fixedNode;
}
}}),
"[project]/src/lib/field-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions for field handling in the workflow builder
 */ /**
 * Determines whether a field should be included in the execution dialog or start node data
 * based on its configuration and connection status.
 *
 * @param isRequired Whether the field is required
 * @param isDirectlyConnected Whether the field is directly connected to the Start node
 * @param hasConfiguredValue Whether the field already has a value configured in the inspector panel
 * @param hasIncomingConnection Whether the field has an incoming connection from another node
 * @returns Boolean indicating whether the field should be included
 */ __turbopack_context__.s({
    "logFieldStatus": (()=>logFieldStatus),
    "shouldIncludeField": (()=>shouldIncludeField)
});
function shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection = false) {
    // Fields directly connected to the Start node should ALWAYS be included,
    // regardless of whether they have pre-configured values or incoming connections
    if (isDirectlyConnected) {
        return true;
    }
    // Fields with incoming connections from other nodes should be excluded
    // (unless they're directly connected to the Start node, which we already checked)
    if (hasIncomingConnection) {
        return false;
    }
    // For required fields, only include them if they don't have a configured value
    return isRequired && !hasConfiguredValue;
}
function logFieldStatus(context, fieldId, nodeName, fieldName, isRequired, requiredValue, isDirectlyConnected, hasConfiguredValue, configuredValue, hasIncomingConnection = false) {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const shouldInclude = shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection);
    console.log(`[${timestamp}] [${context}] Field ${nodeName}.${fieldName} status check:
    - required property value: ${requiredValue === undefined ? "undefined" : requiredValue}
    - required !== false: ${isRequired ? "YES" : "NO"}
    - directly connected to Start: ${isDirectlyConnected ? "YES" : "NO"}
    - has configured value: ${hasConfiguredValue ? "YES" : "NO"}
    - has incoming connection: ${hasIncomingConnection ? "YES" : "NO"}
    - configured value: ${hasConfiguredValue ? JSON.stringify(configuredValue) : "undefined"}
    - Should include: ${shouldInclude ? "YES" : "NO"}`);
}
}}),
"[project]/src/lib/mock-credential-service.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Mock credential service that uses localStorage for credential management.
 * This is a temporary solution until the backend credential API is implemented.
 */ __turbopack_context__.s({
    "mockCreateCredential": (()=>mockCreateCredential),
    "mockDeleteCredential": (()=>mockDeleteCredential),
    "mockFetchCredentials": (()=>mockFetchCredentials),
    "mockGetCredentialValue": (()=>mockGetCredentialValue)
});
const STORAGE_KEY = "workflow_builder_credentials";
/**
 * Get all credentials from localStorage
 */ function getStoredCredentials() {
    try {
        const storedData = localStorage.getItem(STORAGE_KEY);
        if (!storedData) return [];
        return JSON.parse(storedData);
    } catch (error) {
        console.error("Error reading credentials from localStorage:", error);
        return [];
    }
}
/**
 * Save credentials to localStorage
 */ function saveCredentials(credentials) {
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(credentials));
    } catch (error) {
        console.error("Error saving credentials to localStorage:", error);
    }
}
async function mockFetchCredentials() {
    // Simulate network delay
    await new Promise((resolve)=>setTimeout(resolve, 300));
    const credentials = getStoredCredentials();
    console.log("Mock: Fetched credentials", credentials);
    return {
        credentials
    };
}
async function mockCreateCredential(credential) {
    // Simulate network delay
    await new Promise((resolve)=>setTimeout(resolve, 500));
    // Generate ID from name
    const id = credential.name.toLowerCase().replace(/\s+/g, "_");
    // Check if credential with this ID already exists
    const credentials = getStoredCredentials();
    if (credentials.some((c)=>c.id === id)) {
        throw new Error(`Credential with ID '${id}' already exists`);
    }
    // Create new credential
    const newCredential = {
        id,
        name: credential.name,
        type: credential.type
    };
    // Save to localStorage
    saveCredentials([
        ...credentials,
        newCredential
    ]);
    // Also save the value in a separate item for security (in a real app, this would be on the server)
    try {
        localStorage.setItem(`${STORAGE_KEY}_${id}_value`, credential.value);
    } catch (error) {
        console.error(`Error saving credential value for ${id}:`, error);
    }
    console.log("Mock: Created credential", newCredential);
    return newCredential;
}
async function mockDeleteCredential(credentialId) {
    // Simulate network delay
    await new Promise((resolve)=>setTimeout(resolve, 400));
    // Get current credentials
    const credentials = getStoredCredentials();
    // Check if credential exists
    if (!credentials.some((c)=>c.id === credentialId)) {
        throw new Error(`Credential with ID '${credentialId}' not found`);
    }
    // Filter out the credential to delete
    const updatedCredentials = credentials.filter((c)=>c.id !== credentialId);
    // Save updated list
    saveCredentials(updatedCredentials);
    // Remove the value
    try {
        localStorage.removeItem(`${STORAGE_KEY}_${credentialId}_value`);
    } catch (error) {
        console.error(`Error removing credential value for ${credentialId}:`, error);
    }
    console.log("Mock: Deleted credential", credentialId);
    return {
        success: true,
        message: `Credential '${credentialId}' deleted successfully`
    };
}
function mockGetCredentialValue(credentialId) {
    try {
        return localStorage.getItem(`${STORAGE_KEY}_${credentialId}_value`);
    } catch (error) {
        console.error(`Error getting credential value for ${credentialId}:`, error);
        return null;
    }
}
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/api.ts
__turbopack_context__.s({
    "API_BASE_URL": (()=>API_BASE_URL),
    "BACKEND_API_URL": (()=>BACKEND_API_URL),
    "createCredential": (()=>createCredential),
    "debugMCPTools": (()=>debugMCPTools),
    "deleteCredential": (()=>deleteCredential),
    "executeWorkflow": (()=>executeWorkflow),
    "executeWorkflowWithUserInputs": (()=>executeWorkflowWithUserInputs),
    "fetchComponents": (()=>fetchComponents),
    "fetchCredentials": (()=>fetchCredentials),
    "fetchMCPComponents": (()=>fetchMCPComponents),
    "fetchMCPTools": (()=>fetchMCPTools),
    "processJsonObject": (()=>processJsonObject),
    "saveWorkflowToServer": (()=>saveWorkflowToServer),
    "sendApprovalDecision": (()=>sendApprovalDecision),
    "validateWorkflow": (()=>validateWorkflow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mcp_marketplace_transform$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mcp_marketplace_transform.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$mcpToolsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/mcpToolsStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/clientCookies.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$fieldValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/fieldValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$field$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/field-utils.ts [app-ssr] (ecmascript)");
// Import mock implementations
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$credential$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mock-credential-service.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const API_BASE_URL = ("TURBOPACK compile-time value", "https://app-dev.rapidinnovation.dev/api/v1");
const BACKEND_API_URL = API_BASE_URL;
async function debugMCPTools(data) {
    try {
        // Make sure the selected_tool_name is included in the node_config if available
        if (data.node_config && !data.node_config.selected_tool_name && data.node_config.inputs) {
            // Try to find the selected_tool_name in the inputs
            const toolInput = data.node_config.inputs.find((input)=>input.name === "selected_tool_name");
            if (toolInput && toolInput.value) {
                data.node_config.selected_tool_name = toolInput.value;
                console.log(`Added selected_tool_name to debug payload: ${data.node_config.selected_tool_name}`);
            }
        }
        console.log(`Sending debug MCP tools request with data:`, data);
        const response = await fetch(`${BACKEND_API_URL}/debug_mcp_tools`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        console.log(`Debug MCP tools response:`, result);
        return result;
    } catch (error) {
        console.error("Failed to debug MCP Tools:", error);
        return {
            success: false,
            error: String(error)
        };
    }
}
async function fetchMCPTools(nodeConfig, buttonName) {
    try {
        // Create a clean config with only essential properties to avoid duplicating inputs
        const cleanConfig = {
            // Extract only the essential configuration values
            mode: nodeConfig.mode || "Stdio",
            command: nodeConfig.command || "",
            sse_url: nodeConfig.sse_url || "",
            selected_tool_name: nodeConfig.selected_tool_name || "",
            connection_status: nodeConfig.connection_status || "Not Connected"
        };
        // Save the original mode for all operations
        const originalMode = cleanConfig.mode;
        console.log(`Original mode before operation: ${originalMode}`);
        // Ensure SSE URL is properly included for SSE mode
        if (buttonName === "fetch_sse_tools") {
            console.log(`SSE URL from config: ${cleanConfig.sse_url}`);
            // Force mode to SSE for this button
            cleanConfig.mode = "SSE";
            console.log(`Forced mode to SSE for fetch_sse_tools button`);
            // If SSE URL is not in the config, log a warning
            if (!cleanConfig.sse_url) {
                console.warn("SSE URL is missing in the config!");
            }
        } else if (buttonName === "fetch_stdio_tools") {
            // Force mode to Stdio for this button
            cleanConfig.mode = "Stdio";
            console.log(`Forced mode to Stdio for fetch_stdio_tools button`);
        } else if (buttonName === "selected_tool_name") {
            // For tool selection, preserve the current mode and connection status
            console.log(`Tool selection action: ${buttonName}`);
            console.log(`Preserving current mode: ${cleanConfig.mode}`);
            console.log(`Preserving connection status: ${cleanConfig.connection_status}`);
            // Make sure connection_status is set to Connected for tool selection
            if (cleanConfig.connection_status !== "Connected") {
                console.log(`Setting connection_status to Connected for tool selection`);
                cleanConfig.connection_status = "Connected";
            }
        }
        // Don't change the mode for other buttons
        // Ensure connection_status is included
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Create a payload that simulates what the backend expects
        const payload = {
            config: cleanConfig
        };
        // Set the button value based on the button name
        if (buttonName === "selected_tool_name") {
            payload.selected_tool_name = cleanConfig.selected_tool_name;
            console.log(`Setting selected_tool_name in payload: ${cleanConfig.selected_tool_name}`);
            // Make sure connection_status is set to Connected
            if (cleanConfig.connection_status !== "Connected") {
                console.log(`Ensuring connection_status is Connected for tool selection`);
                cleanConfig.connection_status = "Connected";
                payload.config.connection_status = "Connected";
            }
        } else {
            payload[buttonName] = true; // Set the button value to true
        }
        // IMPORTANT: Always preserve the mode from the config for all operations
        // This ensures we don't reset to Stdio when selecting a tool or performing other operations
        console.log(`Preserving mode in payload: ${originalMode}`);
        payload.config.mode = originalMode;
        // Always make sure we include the selected_tool_name if available
        console.log(`Selected tool name from config: ${cleanConfig.selected_tool_name}`);
        if (!cleanConfig.selected_tool_name && cleanConfig.inputs) {
            // Try to find the selected_tool_name in the inputs
            const toolInput = cleanConfig.inputs.find((input)=>input.name === "selected_tool_name");
            if (toolInput && toolInput.value) {
                cleanConfig.selected_tool_name = toolInput.value;
                console.log(`Found selected_tool_name in inputs: ${cleanConfig.selected_tool_name}`);
            }
        }
        // Make sure the selected_tool_name is included in the payload
        if (cleanConfig.selected_tool_name) {
            payload.config.selected_tool_name = cleanConfig.selected_tool_name;
            console.log(`Added selected_tool_name to payload: ${payload.config.selected_tool_name}`);
        }
        console.log(`Sending direct MCP tools fetch request with payload:`, payload);
        const response = await fetch(`${BACKEND_API_URL}/fetch_mcp_tools`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(payload)
        });
        if (!response.ok) {
            throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }
        const responseData = await response.json();
        console.log(`MCP tools fetch response:`, responseData);
        // Extract the result from the response
        const result = responseData.result || responseData;
        // Process the result to ensure it's properly structured
        if (result && result.inputs) {
            console.log(`Processing result with ${result.inputs.length} inputs`);
            // Ensure mode is preserved in the result
            if (buttonName === "fetch_sse_tools") {
                // For SSE mode, ensure the mode is set to SSE
                result.mode = "SSE";
                // Also fix the mode in the inputs array
                if (Array.isArray(result.inputs)) {
                    const modeInput = result.inputs.find((input)=>input.name === "mode");
                    if (modeInput) {
                        console.log(`Setting mode input value to SSE`);
                        modeInput.value = "SSE";
                    }
                }
            } else if (buttonName === "fetch_stdio_tools") {
                // For Stdio mode, ensure the mode is set to Stdio
                result.mode = "Stdio";
                // Also fix the mode in the inputs array
                if (Array.isArray(result.inputs)) {
                    const modeInput = result.inputs.find((input)=>input.name === "mode");
                    if (modeInput) {
                        console.log(`Setting mode input value to Stdio`);
                        modeInput.value = "Stdio";
                    }
                }
            }
            // Store the tool schemas if available
            if (result._internal_state && result._internal_state.tool_schemas) {
                console.log(`Storing ${Object.keys(result._internal_state.tool_schemas).length} tool schemas`);
            // You could store these in a global state if needed
            }
        }
        return responseData.success ? result : responseData;
    } catch (error) {
        console.error("Failed to fetch MCP tools:", error);
        return {
            success: false,
            error: String(error)
        };
    }
}
async function fetchComponents() {
    try {
        const response = await fetch(`http://localhost:8000/api/v1/components`);
        console.log("Sent the request to fetch components with the url as :", response.url);
        if (!response.ok) {
            throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log("Fetched components:", data); // For debugging
        return data;
    } catch (error) {
        console.error("Failed to fetch components:", error);
        // Return empty object or re-throw error based on how you want to handle failures
        return {};
    }
}
async function fetchMCPComponents() {
    try {
        // Get the access token based on environment
        let accessToken;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        // Prepare headers with authentication
        const headers = {
            "Content-Type": "application/json"
        };
        // Add Authorization header if we have a token
        if (accessToken) {
            headers["Authorization"] = `Bearer ${accessToken}`;
        }
        const response = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].MCPS.LIST}`, {
            method: "GET",
            headers: headers
        });
        console.log("Sent the request to fetch MCP components with the url as:", response.url);
        if (!response.ok) {
            throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }
        // The API returns an array of MCP components, but we need to transform it to match ComponentsApiResponse
        const responseData = await response.json();
        console.log("Fetched MCP response:", responseData); // For debugging
        // Check if the response is in the expected format (array or data property containing array)
        let mcpData = [];
        if (Array.isArray(responseData)) {
            // If the response is already an array, use it directly
            console.log("MCP response is an array with", responseData.length, "items");
            mcpData = responseData;
        } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
            // If the response has a data property that is an array, use that
            console.log("MCP response has data property with", responseData.data.length, "items");
            mcpData = responseData.data;
        } else {
            // Handle the case where the response is an object with the data we need
            console.error("MCP response is not in expected format");
            console.log("MCP response type:", typeof responseData);
            console.log("MCP response structure:", Object.keys(responseData || {}));
            // If the response is the example data provided by the user, use it directly
            if (responseData && typeof responseData === 'object' && responseData.data) {
                console.log("Using data property from response");
                mcpData = Array.isArray(responseData.data) ? responseData.data : [
                    responseData.data
                ];
            } else if (responseData && typeof responseData === 'object') {
                // If it's an object with MCP properties, wrap it in an array
                console.log("Wrapping response object in array");
                mcpData = [
                    responseData
                ];
            } else {
                // Return empty object if we can't process the response
                console.error("Cannot process MCP response, returning empty MCP category");
                return {
                    "MCP": {}
                };
            }
        }
        console.log("Processed MCP data:", mcpData);
        // Transform the MCP array into the ComponentsApiResponse format
        // Create a "MCP" category in the components object
        const mcpComponents = {
            "MCP": {}
        };
        // Process each MCP component
        mcpData.forEach((mcp)=>{
            // For each tool in the MCP, create a component
            if (mcp.mcp_tools_config && mcp.mcp_tools_config.tools) {
                mcp.mcp_tools_config.tools.forEach((tool)=>{
                    // Create a unique component name
                    const componentName = `MCP_${mcp.name}_${tool.name}`.replace(/\s+/g, '_');
                    // Create the component definition
                    mcpComponents["MCP"][componentName] = {
                        name: componentName,
                        display_name: `${mcp.name} - ${tool.name}`,
                        description: tool.description || mcp.description,
                        category: "MCP",
                        icon: "Cloud",
                        beta: true,
                        inputs: [],
                        outputs: [],
                        is_valid: true,
                        path: `mcp.${componentName.toLowerCase()}`,
                        type: "MCP",
                        mcp_info: {
                            server_id: mcp.id || "",
                            server_path: mcp.url || "",
                            tool_name: tool.name,
                            input_schema: tool.input_schema,
                            output_schema: tool.output_schema
                        }
                    };
                    // Convert input schema to input definitions
                    if (tool.input_schema && tool.input_schema.properties) {
                        const requiredFields = tool.input_schema.required || [];
                        Object.entries(tool.input_schema.properties).forEach(([propName, propSchema])=>{
                            const inputDef = {
                                name: propName,
                                display_name: propSchema.title || propName.replace(/_/g, ' '),
                                info: propSchema.description || "",
                                input_type: mapJsonSchemaTypeToInputType(propSchema.type, propSchema),
                                input_types: null,
                                required: requiredFields.includes(propName),
                                is_handle: true,
                                is_list: propSchema.type === "array",
                                real_time_refresh: false,
                                advanced: false,
                                value: propSchema.default || null,
                                options: propSchema.enum ? propSchema.enum : null,
                                visibility_rules: null,
                                visibility_logic: "OR"
                            };
                            mcpComponents["MCP"][componentName].inputs.push(inputDef);
                        });
                    }
                    // Add output definitions based on output schema
                    if (tool.output_schema && tool.output_schema.properties) {
                        Object.entries(tool.output_schema.properties).forEach(([propName, propSchema])=>{
                            const outputDef = {
                                name: propName,
                                display_name: propSchema.title || propName.replace(/_/g, ' '),
                                output_type: mapJsonSchemaTypeToOutputType(propSchema.type)
                            };
                            mcpComponents["MCP"][componentName].outputs.push(outputDef);
                        });
                    }
                    // If no outputs were defined, add a default output
                    if (mcpComponents["MCP"][componentName].outputs.length === 0) {
                        mcpComponents["MCP"][componentName].outputs.push({
                            name: "result",
                            display_name: "Result",
                            output_type: "any"
                        });
                    }
                });
            }
        });
        console.log("Transformed MCP components:", mcpComponents);
        return mcpComponents;
    } catch (error) {
        console.error("Failed to fetch MCP components:", error);
        // Return empty object with just the MCP category
        return {
            "MCP": {}
        };
    }
}
/**
 * Helper function to map JSON Schema types to input types
 */ function mapJsonSchemaTypeToInputType(schemaType, schema) {
    switch(schemaType){
        case "string":
            if (schema.format === "uri") return "url";
            if (schema.enum) return "dropdown";
            return "string";
        case "number":
        case "integer":
            return "number";
        case "boolean":
            return "boolean";
        case "object":
            return "object"; // Changed from "json" to "object" for better representation
        case "array":
            return "list";
        default:
            return "string";
    }
}
/**
 * Helper function to map JSON Schema types to output types
 */ function mapJsonSchemaTypeToOutputType(schemaType) {
    switch(schemaType){
        case "string":
            return "string";
        case "number":
        case "integer":
            return "number";
        case "boolean":
            return "boolean";
        case "object":
            return "object";
        case "array":
            return "list";
        default:
            return "any";
    }
}
async function executeWorkflow(payload) {
    // Get the component state store
    const componentState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$mcpToolsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useComponentStateStore"].getState();
    // Add component state to the nodes
    const nodesWithState = payload.nodes.map((node)=>{
        // Create a deep copy of the node
        const nodeCopy = JSON.parse(JSON.stringify(node));
        // Get the component state for this node
        const nodeState = componentState.nodes[node.id];
        // Log the original node data
        console.log(`Original node ${node.id} data:`, {
            config: node.data.config,
            definition: node.data.definition ? {
                inputs: node.data.definition.inputs
            } : null
        });
        // If there's state for this node, add it to the node data
        if (nodeState) {
            // Add the component state to the node data
            nodeCopy.data.component_state = nodeState;
            // If there's a config in the component state, add it to the node config
            if (nodeState.config) {
                // Merge the component state config with the node config
                nodeCopy.data.config = {
                    ...nodeCopy.data.config,
                    ...nodeState.config
                };
                // Log the component state config
                console.log(`Component state config for node ${node.id}:`, nodeState.config);
            }
            // Also add any direct values from the component state to the node config
            for (const [key, value] of Object.entries(nodeState)){
                if (key !== "config" && value !== null && value !== undefined && value !== "") {
                    nodeCopy.data.config[key] = value;
                    console.log(`Added direct value from component state for node ${node.id}: ${key}=${value}`);
                }
            }
            // Log the node with state
            console.log(`Added component state to node ${node.id}:`, nodeState);
            console.log(`Updated node config:`, nodeCopy.data.config);
        } else {
            // If there's no state for this node, check if there are any values in the inputs
            if (nodeCopy.data.definition && nodeCopy.data.definition.inputs) {
                const inputs = nodeCopy.data.definition.inputs;
                for (const input of inputs){
                    if (input.value !== null && input.value !== undefined && input.value !== "" && (typeof input.value !== "object" || Object.keys(input.value).length > 0)) {
                        // Add the input value to the node config
                        nodeCopy.data.config[input.name] = input.value;
                    }
                }
            }
        }
        return nodeCopy;
    });
    // Create a new payload with the nodes with state
    const payloadWithState = {
        nodes: nodesWithState,
        edges: payload.edges
    };
    // Transform the payload for the external orchestration service
    // This will properly format MCP Marketplace components
    const { nodes, edges } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mcp_marketplace_transform$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformWorkflowForOrchestration"])(payloadWithState.nodes, payloadWithState.edges);
    // Post-process the transformed nodes to ensure API request method is preserved
    const processedNodes = nodes.map((node)=>{
        // Check if this is an API request node
        if (node.data.originalType === "ApiRequestNode" || node.data.type === "ApiRequestNode") {
            console.log(`Processing API request node ${node.id}`);
            // Ensure the method is preserved from the original node
            const originalNode = payloadWithState.nodes.find((n)=>n.id === node.id);
            if (originalNode && originalNode.data.config && originalNode.data.config.method) {
                console.log(`Preserving method ${originalNode.data.config.method} for API request node ${node.id}`);
                if (node.data.config) {
                    node.data.config.method = originalNode.data.config.method;
                }
            }
        }
        return node;
    });
    // Create the transformed payload without mcp_configs
    const transformedPayload = {
        nodes: processedNodes,
        edges,
        workflow_id: payload.workflow_id
    };
    // Log the original payload
    console.log(`Original execution payload:`, payload);
    // Log the payload with state
    console.log(`Payload with state:`, payloadWithState);
    // Log the transformed payload
    console.log(`Transformed payload:`, transformedPayload);
    // Save the payloads to localStorage for debugging
    try {
        localStorage.setItem("original_payload", JSON.stringify(payload));
        localStorage.setItem("payload_with_state", JSON.stringify(payloadWithState));
        localStorage.setItem("transformed_payload", JSON.stringify(transformedPayload));
        console.log("Saved payloads to localStorage for debugging");
    } catch (error) {
        console.error("Error saving payloads to localStorage:", error);
    }
    // Use the backend API for execution, which will forward to the external service
    const endpoint = `${BACKEND_API_URL}/execute`; // Backend API endpoint
    console.log(`Sending execution request to ${endpoint} with payload:`, transformedPayload);
    try {
        // Get the access token based on environment
        let accessToken;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        // Prepare headers with authentication
        const headers = {
            "Content-Type": "application/json"
        };
        // Add Authorization header if we have a token
        if (accessToken) {
            headers["Authorization"] = `Bearer ${accessToken}`;
        }
        const response = await fetch(endpoint, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(transformedPayload)
        });
        // Check if the request was successful (status code 2xx)
        if (!response.ok) {
            let errorDetail = "Unknown execution error";
            try {
                // Try to parse more detailed error from backend response body
                const errorData = await response.json();
                errorDetail = errorData.detail || JSON.stringify(errorData);
            } catch (parseError) {
                // If parsing fails, use the status text
                errorDetail = response.statusText;
            }
            // Throw an error to be caught by the catch block below
            throw new Error(`Execution failed: ${response.status} ${errorDetail}`);
        }
        // If successful, parse the JSON response from the backend
        const resultData = await response.json();
        // Ensure the result matches the expected structure (basic check)
        if (typeof resultData.success !== "boolean") {
            throw new Error("Invalid response format received from backend.");
        }
        console.log("Received execution result:", resultData);
        return resultData; // Type assertion
    } catch (error) {
        console.error("Error executing workflow via API:", error);
        // Return a standardized error format consistent with ExecutionResult
        return {
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
async function saveWorkflowToServer(payload) {
    // Determine if this is a new workflow or an existing one
    const isExistingWorkflow = !!payload.workflow_id;
    // Use the appropriate endpoint based on whether it's a new or existing workflow
    let endpoint = '';
    if (isExistingWorkflow && payload.workflow_id) {
        // For existing workflows, use the UPDATE endpoint with the workflow ID in the URL
        endpoint = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOWS.UPDATE(payload.workflow_id)}`;
    }
    // Use appropriate HTTP method based on whether it's a new or existing workflow
    const method = isExistingWorkflow ? "PATCH" : "POST";
    console.log(`Sending save workflow request to ${endpoint} with method ${method} and payload:`, payload);
    // Find the StartNode to update its configuration
    const startNode = payload.nodes.find((node)=>node.data.originalType === "StartNode" || node.id === "start-node" || node.data.label === "Start");
    // Check if we have a StartNode
    if (startNode) {
        console.log(`[WORKFLOW SAVE] Found StartNode with ID: ${startNode.id}`);
        // Ensure the StartNode has a config object
        if (!startNode.data.config) {
            console.log(`[WORKFLOW SAVE] StartNode has no config object, creating one`);
            startNode.data.config = {};
        }
        // Ensure the config has a collected_parameters object
        if (!startNode.data.config.collected_parameters) {
            console.log(`[WORKFLOW SAVE] StartNode has no collected_parameters object, creating one`);
            startNode.data.config.collected_parameters = {};
        }
        // Create a fresh collected_parameters object
        if (startNode.data.config) {
            startNode.data.config.collected_parameters = {};
        }
        console.log(`[WORKFLOW SAVE] Using Run button's graph traversal logic to find all connected nodes`);
        // Use the same getConnectedNodes function that the Run button uses
        // This performs a breadth-first search to find all nodes reachable from the StartNode
        const connectedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getConnectedNodes"])(payload.nodes, payload.edges, startNode.id);
        console.log(`[WORKFLOW SAVE] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);
        // Use the same collectAllFields function that the Run button uses
        // This collects all fields from connected nodes, including required and optional fields
        const allFields = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$fieldValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectAllFields"])(payload.nodes, connectedNodes, payload.edges);
        console.log(`[WORKFLOW SAVE] Collected ${allFields.length} fields from connected nodes`);
        // Log detailed field information
        if (allFields.length > 0) {
            console.log(`[WORKFLOW SAVE] Field details:`);
            allFields.forEach((field, index)=>{
                console.log(`[WORKFLOW SAVE]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}, Required: ${field.required ? "YES" : "NO"}`);
            });
        }
        // Add only fields that should be included to the StartNode's collected_parameters
        if (allFields.length > 0) {
            console.log(`[WORKFLOW SAVE] Adding fields to StartNode collected_parameters (excluding fields with incoming connections)`);
            allFields.forEach((field)=>{
                const fieldId = `${field.nodeId}_${field.name}`;
                // Check if this field has an incoming connection from another node
                const hasIncomingConnection = field.is_handle === true && field.is_connected === true;
                // Use the shared utility function to determine if the field should be included
                const isRequired = field.required !== false;
                const isDirectlyConnected = field.directly_connected_to_start === true;
                const hasConfiguredValue = field.currentValue !== undefined;
                // Log field status for debugging
                console.log(`[WORKFLOW SAVE] Field ${fieldId} status:
          - required: ${isRequired ? "YES" : "NO"}
          - directly connected to Start: ${isDirectlyConnected ? "YES" : "NO"}
          - has configured value: ${hasConfiguredValue ? "YES" : "NO"}
          - has incoming connection: ${hasIncomingConnection ? "YES" : "NO"}`);
                // Only include fields that should be included based on our filtering logic
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$field$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shouldIncludeField"])(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection)) {
                    if (startNode.data.config && startNode.data.config.collected_parameters) {
                        startNode.data.config.collected_parameters[fieldId] = {
                            node_id: field.nodeId,
                            node_name: field.nodeName,
                            input_name: field.name,
                            value: field.currentValue,
                            connected_to_start: true,
                            required: field.required,
                            input_type: field.inputType,
                            options: field.options
                        };
                        console.log(`[WORKFLOW SAVE] Added field ${fieldId} to StartNode collected_parameters`);
                    }
                } else {
                    console.log(`[WORKFLOW SAVE] Skipping field ${fieldId} - excluded by filtering logic`);
                }
            });
            console.log(`[WORKFLOW SAVE] Finished adding fields to StartNode collected_parameters`);
        } else {
            console.log(`[WORKFLOW SAVE] No fields found to add to StartNode collected_parameters`);
        }
        // Log the final StartNode configuration
        console.log(`[WORKFLOW SAVE] StartNode complete data:`, startNode.data);
        console.log(`[WORKFLOW SAVE] StartNode config:`, startNode.data.config);
        console.log(`[WORKFLOW SAVE] StartNode collected_parameters:`, startNode.data.config.collected_parameters);
    } else {
        console.log(`[WORKFLOW SAVE] No StartNode found in the workflow`);
    }
    // Extract the start node data
    const start_node_data = extractStartNodeData(payload.nodes, payload.edges);
    console.log(`[WORKFLOW SAVE] Extracted start_node_data:`, start_node_data);
    // Initialize variables for filtered nodes and edges
    let filteredNodes = payload.nodes;
    let filteredEdges = payload.edges;
    // Filter out nodes that are not connected to the StartNode
    if (startNode) {
        console.log(`[WORKFLOW SAVE] Using Run button's graph traversal logic to find all connected nodes`);
        // Use the getConnectedNodes function to find all nodes reachable from the StartNode
        const connectedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getConnectedNodes"])(payload.nodes, payload.edges, startNode.id);
        console.log(`[WORKFLOW SAVE] Found ${connectedNodes.size} nodes connected to StartNode: ${Array.from(connectedNodes).join(', ')}`);
        // Check if there are any disconnected nodes
        const disconnectedNodesCount = payload.nodes.length - connectedNodes.size;
        if (disconnectedNodesCount > 0) {
            console.log(`[WORKFLOW SAVE] Found ${disconnectedNodesCount} disconnected nodes that will be excluded from the saved workflow`);
            // Filter nodes to include only those connected to the StartNode
            filteredNodes = payload.nodes.filter((node)=>connectedNodes.has(node.id));
            // Filter edges to include only those connecting filtered nodes
            filteredEdges = payload.edges.filter((edge)=>connectedNodes.has(edge.source) && connectedNodes.has(edge.target));
            console.log(`[WORKFLOW SAVE] Filtered workflow contains ${filteredNodes.length} nodes and ${filteredEdges.length} edges`);
            // Import toast dynamically to avoid server-side rendering issues
            try {
                // Use dynamic import for toast
                const { toast } = await __turbopack_context__.r("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                // Show a warning toast notification
                toast.warning(`${disconnectedNodesCount} unconnected ${disconnectedNodesCount === 1 ? 'node has' : 'nodes have'} been excluded from the saved workflow.`, {
                    description: "Only nodes connected to the Start node are included in the workflow.",
                    duration: 5000
                });
            } catch (error) {
                console.error("Failed to show toast notification:", error);
            }
        }
    } else {
        // If no StartNode is found, use the original payload
        console.warn(`[WORKFLOW SAVE] No StartNode found, using original payload`);
    }
    // Create the request payload with filtered nodes and edges
    const requestPayload = {
        name: payload.filename,
        description: payload.filename,
        workflow_data: {
            nodes: filteredNodes,
            edges: filteredEdges
        },
        start_node_data: start_node_data
    };
    try {
        // Get the access token based on environment
        let accessToken;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        // Prepare headers with authentication
        const headers = {
            "Content-Type": "application/json"
        };
        // Add Authorization header if we have a token
        if (accessToken) {
            headers["Authorization"] = `Bearer ${accessToken}`;
        }
        // Log the final request payload with StartNode config
        console.log(`[WORKFLOW SAVE] Final request payload:`, requestPayload);
        console.log(`[WORKFLOW SAVE] Final request payload JSON:`, JSON.stringify(requestPayload, null, 2));
        const response = await fetch(endpoint, {
            method: method,
            headers: headers,
            body: JSON.stringify(requestPayload)
        });
        // Check if the request was successful (status code 2xx)
        if (!response.ok) {
            let errorDetail = "Unknown save error";
            try {
                // Try to parse more detailed error from backend response body
                const errorData = await response.json();
                errorDetail = errorData.detail || JSON.stringify(errorData);
            } catch (parseError) {
                // If parsing fails, use the status text
                errorDetail = response.statusText;
            }
            // Handle specific error codes
            if (response.status === 400) {
                errorDetail = "Invalid workflow data. Please check your workflow configuration.";
            } else if (response.status === 403) {
                errorDetail = "You don't have permission to save this workflow.";
            } else if (response.status === 404) {
                errorDetail = "Workflow not found. It may have been deleted.";
            } else if (response.status === 500) {
                errorDetail = "Server error occurred while saving the workflow. Please try again later.";
            }
            // Throw an error to be caught by the catch block below
            throw new Error(`Save failed: ${response.status} ${errorDetail}`);
        }
        // If successful, parse the JSON response from the backend
        const resultData = await response.json();
        // Handle different response formats from the new endpoint
        let saveResult;
        // The new endpoint returns a workflow object directly
        if (resultData.workflow) {
            // New endpoint format
            saveResult = {
                success: true,
                workflow_id: resultData.workflow.id,
                message: "Workflow saved successfully",
                error: undefined
            };
        } else if (typeof resultData.success === "boolean") {
            // Old format with success property
            saveResult = {
                success: resultData.success,
                workflow_id: resultData.workflow_id,
                message: resultData.message || "Workflow saved successfully",
                error: resultData.error
            };
        } else {
            throw new Error("Invalid response format received from backend.");
        }
        console.log("Received save result:", saveResult);
        return saveResult;
    } catch (error) {
        console.error("Error saving workflow via API:", error);
        // Return a standardized error format consistent with SaveResult
        return {
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
function processJsonObject(jsonData, parentKey = '', parentTransitionId = '') {
    // Handle null or undefined
    if (jsonData === null || jsonData === undefined) {
        return {
            field: parentKey,
            type: 'string',
            transition_id: parentTransitionId
        };
    }
    // If it's an object (but not an array), process it as an object with properties
    if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {
        const properties = [];
        // Process each property of the object
        Object.entries(jsonData).forEach(([key, value])=>{
            if (value === null || value === undefined) {
                // Handle null values as simple string properties
                properties.push({
                    field: key,
                    type: 'string',
                    transition_id: parentTransitionId
                });
            } else if (typeof value === 'object' && !Array.isArray(value)) {
                // Recursively process nested objects
                const nestedObject = processJsonObject(value, key, parentTransitionId);
                properties.push(nestedObject);
            } else if (Array.isArray(value)) {
                // Handle arrays
                if (value.length > 0 && typeof value[0] === 'object') {
                    // If array contains objects, process the first one as an example
                    const nestedObject = processJsonObject(value[0], key, parentTransitionId);
                    properties.push({
                        field: key,
                        type: 'array',
                        transition_id: parentTransitionId,
                        properties: [
                            nestedObject
                        ]
                    });
                } else {
                    // Simple array
                    properties.push({
                        field: key,
                        type: 'array',
                        transition_id: parentTransitionId
                    });
                }
            } else {
                // Handle primitive types
                let type = typeof value;
                if (type === 'number') {
                    // Check if it's an integer or float
                    type = Number.isInteger(value) ? 'integer' : 'number';
                }
                properties.push({
                    field: key,
                    type: type,
                    transition_id: parentTransitionId
                });
            }
        });
        // Return the object with its properties
        return {
            field: parentKey,
            type: 'object',
            transition_id: parentTransitionId,
            properties: properties
        };
    } else if (Array.isArray(jsonData)) {
        // It's an array
        if (jsonData.length > 0 && typeof jsonData[0] === 'object') {
            // If array contains objects, process the first one as an example
            const nestedObject = processJsonObject(jsonData[0], `${parentKey}Item`, parentTransitionId);
            return {
                field: parentKey,
                type: 'array',
                transition_id: parentTransitionId,
                properties: [
                    nestedObject
                ]
            };
        } else {
            // Simple array
            return {
                field: parentKey,
                type: 'array',
                transition_id: parentTransitionId
            };
        }
    } else {
        // It's a primitive type
        let type = typeof jsonData;
        if (type === 'number') {
            // Check if it's an integer or float
            type = Number.isInteger(jsonData) ? 'integer' : 'number';
        }
        return {
            field: parentKey,
            type: type,
            transition_id: parentTransitionId
        };
    }
}
// The flattenJson function has been replaced by processJsonObject
/**
 * Checks if a handle input is connected to another node
 *
 * @param nodeId The ID of the node containing the handle
 * @param inputName The name of the handle input
 * @param edges The array of edges in the workflow
 * @returns True if the handle is connected to another node
 */ function isConnected(nodeId, inputName, edges) {
    // For target handles, the connection would be to a target handle with the input name
    const isConnected = edges.some((edge)=>edge.target === nodeId && edge.targetHandle === inputName);
    // Log all edges for debugging
    console.log(`[EXTRACT_START_NODE] All edges (${edges.length}):`);
    edges.forEach((edge, index)=>{
        console.log(`[EXTRACT_START_NODE] Edge ${index + 1}: source=${edge.source}, sourceHandle=${edge.sourceHandle}, target=${edge.target}, targetHandle=${edge.targetHandle}`);
    });
    // Log the specific check we're making
    console.log(`[EXTRACT_START_NODE] Checking if handle ${nodeId}.${inputName} is connected: ${isConnected ? "YES" : "NO"}`);
    // If connected, log which edge(s) connect to this handle
    if (isConnected) {
        const connectingEdges = edges.filter((edge)=>edge.target === nodeId && edge.targetHandle === inputName);
        connectingEdges.forEach((edge, index)=>{
            console.log(`[EXTRACT_START_NODE] Found connecting edge ${index + 1}: source=${edge.source}, sourceHandle=${edge.sourceHandle}, target=${edge.target}, targetHandle=${edge.targetHandle}`);
        });
    }
    return isConnected;
}
/**
 * Extracts required input fields from the start node
 * @param nodes The workflow nodes
 * @param edges The array of edges in the workflow
 * @returns Array of start node data objects
 */ function extractStartNodeData(nodes, edges = []) {
    console.log(`[EXTRACT_START_NODE] Starting extraction from ${nodes.length} nodes`);
    // Find the start node
    const startNode = nodes.find((node)=>node.data.originalType === "StartNode" || node.id === "start-node" || node.data.label === "Start");
    if (!startNode) {
        console.log(`[EXTRACT_START_NODE] No StartNode found in the workflow`);
        return [];
    }
    console.log(`[EXTRACT_START_NODE] Found StartNode with ID: ${startNode.id}`);
    if (!startNode.data.config) {
        console.log(`[EXTRACT_START_NODE] StartNode has no config object`);
        return [];
    }
    if (!startNode.data.config.collected_parameters) {
        console.log(`[EXTRACT_START_NODE] StartNode has no collected_parameters object`);
        return [];
    }
    console.log(`[EXTRACT_START_NODE] StartNode config:`, startNode.data.config);
    console.log(`[EXTRACT_START_NODE] StartNode collected_parameters:`, startNode.data.config.collected_parameters);
    const result = [];
    // Extract parameters from the start node
    const params = startNode.data.config.collected_parameters;
    Object.entries(params).forEach(([fieldId, paramData])=>{
        console.log(`[EXTRACT_START_NODE] Processing field: ${fieldId}`, paramData);
        // Extract node_id and field_name from the fieldId
        const nodeId = paramData.node_id;
        const fieldName = paramData.input_name;
        console.log(`[EXTRACT_START_NODE] Extracted nodeId: ${nodeId}, fieldName: ${fieldName}`);
        // Check if this field is required (consider it required unless explicitly marked as optional)
        const isRequired = paramData.required !== false;
        // Check if this field is directly connected to the Start node
        const isDirectlyConnected = paramData.connected_to_start === true;
        // Find the node in the current workflow
        const node = nodes.find((node)=>node.id === nodeId);
        // Check if the node has a config with a value for this field
        const hasConfiguredValue = node?.data?.config && node.data.config[fieldName] !== undefined;
        // Check if this field has an incoming connection from another node
        const hasIncomingConnection = isConnected(nodeId, fieldName, edges);
        // Use the shared utility function to log field status
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$field$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logFieldStatus"])("ExtractStartNodeData", fieldId, node?.data?.label || "Unknown Node", fieldName, isRequired, paramData.required, isDirectlyConnected || false, hasConfiguredValue || false, hasConfiguredValue && node?.data?.config ? node.data.config[fieldName] : undefined, hasIncomingConnection);
        // Use the shared utility function to determine if the field should be included
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$field$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shouldIncludeField"])(isRequired, isDirectlyConnected || false, hasConfiguredValue || false, hasIncomingConnection)) {
            // Determine the data type
            let dataType = paramData.input_type || "string";
            let enumValues = undefined;
            if (paramData.input_type) {
                dataType = paramData.input_type;
                console.log(`[EXTRACT_START_NODE] Found dataType in paramData.input_type: ${dataType}`);
            } else {
                console.log(`[EXTRACT_START_NODE] No input_type found, using default: ${dataType}`);
            }
            if ((dataType === "json" || dataType === "object") && paramData.value) {
                try {
                    // If value is already an object, use it directly
                    const jsonData = typeof paramData.value === 'object' ? paramData.value : JSON.parse(paramData.value);
                    console.log(`[EXTRACT_START_NODE] Processing ${dataType} type data`);
                    // Process the JSON object with proper structure
                    const processedObject = processJsonObject(jsonData, fieldName, nodeId);
                    console.log(`[EXTRACT_START_NODE] Processed object structure:`, processedObject);
                    // Add the processed object to the result
                    result.push(processedObject);
                    // Skip adding the original JSON field since we've added the processed version
                    return;
                } catch (error) {
                    console.log(`[EXTRACT_START_NODE] Error processing ${dataType}: ${error}. Using as regular string.`);
                    // Fall back to treating it as a string
                    dataType = "string";
                }
            }
            // Check for enum values if dataType is enum
            if (dataType === "enum") {
                if (Array.isArray(paramData.options)) {
                    enumValues = paramData.options;
                    console.log(`[EXTRACT_START_NODE] Found enum values in paramData.options`);
                }
            }
            console.log(`[EXTRACT_START_NODE] Determined dataType: ${dataType}`);
            // For regular fields, create a simple field object
            result.push({
                field: fieldName,
                type: dataType,
                ...enumValues && {
                    enum: enumValues
                },
                transition_id: nodeId
            });
        } else {
            console.log(`[EXTRACT_START_NODE] Skipping field ${fieldId} (not required, has configured value, or not directly connected)`);
        }
    });
    console.log(`[EXTRACT_START_NODE] Final extracted result:`, result);
    return result;
}
async function validateWorkflow(payload) {
    const endpoint = `${BACKEND_API_URL}/validate_workflow`; // Endpoint for validation
    console.log(`Sending validation request to ${endpoint} with payload:`, payload);
    try {
        // Get the access token based on environment
        let accessToken;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        // Prepare headers with authentication
        const headers = {
            "Content-Type": "application/json"
        };
        // Add Authorization header if we have a token
        if (accessToken) {
            headers["Authorization"] = `Bearer ${accessToken}`;
        }
        const response = await fetch(endpoint, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(payload)
        });
        // Check if the request was successful (status code 2xx)
        if (!response.ok) {
            let errorDetail = "Unknown validation error";
            try {
                // Try to parse more detailed error from backend response body
                const errorData = await response.json();
                errorDetail = errorData.detail || JSON.stringify(errorData);
            } catch (parseError) {
                // If parsing fails, use the status text
                errorDetail = response.statusText;
            }
            // Throw an error to be caught by the catch block below
            throw new Error(`Validation failed: ${response.status} ${errorDetail}`);
        }
        // If successful, parse the JSON response from the backend
        const resultData = await response.json();
        // Ensure the result matches the expected structure (basic check)
        if (typeof resultData.is_valid !== "boolean") {
            throw new Error("Invalid response format received from backend.");
        }
        console.log("Received validation result:", resultData);
        return resultData; // Type assertion
    } catch (error) {
        console.error("Error validating workflow via API:", error);
        // Return a standardized error format consistent with ValidationResult
        return {
            is_valid: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
;
async function fetchCredentials() {
    // Use mock implementation for now
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$credential$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockFetchCredentials"])();
// The code below is kept for future implementation
/*
  const endpoint = `${API_BASE_URL}/credentials`;
  console.log(`Fetching credentials from ${endpoint}`);

  try {
    const response = await fetch(endpoint);

    if (!response.ok) {
      let errorDetail = 'Unknown error';
      try {
        const errorData = await response.json();
        errorDetail = errorData.detail || JSON.stringify(errorData);
      } catch (parseError) {
        errorDetail = response.statusText;
      }
      throw new Error(`Failed to fetch credentials: ${errorDetail}`);
    }

    const data = await response.json();
    console.log("Received credentials:", data);
    return data;
  } catch (error) {
    console.error("Error fetching credentials:", error);
    throw error; // Re-throw to let the component handle the error
  }
  */ }
async function createCredential(credential) {
    // Use mock implementation for now
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$credential$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockCreateCredential"])(credential);
// The code below is kept for future implementation
/*
  const endpoint = `${API_BASE_URL}/credentials`;
  console.log(`Creating credential at ${endpoint}`);

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credential),
    });

    if (!response.ok) {
      let errorDetail = 'Unknown error';
      try {
        const errorData = await response.json();
        errorDetail = errorData.detail || JSON.stringify(errorData);
      } catch (parseError) {
        errorDetail = response.statusText;
      }
      throw new Error(`Failed to create credential: ${errorDetail}`);
    }

    const data = await response.json();
    console.log("Created credential:", data);
    return data;
  } catch (error) {
    console.error("Error creating credential:", error);
    throw error; // Re-throw to let the component handle the error
  }
  */ }
async function deleteCredential(credentialId) {
    // Use mock implementation for now
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$credential$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockDeleteCredential"])(credentialId);
// The code below is kept for future implementation
/*
  const endpoint = `${API_BASE_URL}/credentials/${credentialId}`;
  console.log(`Deleting credential at ${endpoint}`);

  try {
    const response = await fetch(endpoint, {
      method: 'DELETE',
    });

    if (!response.ok) {
      let errorDetail = 'Unknown error';
      try {
        const errorData = await response.json();
        errorDetail = errorData.detail || JSON.stringify(errorData);
      } catch (parseError) {
        errorDetail = response.statusText;
      }
      throw new Error(`Failed to delete credential: ${errorDetail}`);
    }

    const data = await response.json();
    console.log("Deleted credential:", data);
    return data;
  } catch (error) {
    console.error("Error deleting credential:", error);
    throw error; // Re-throw to let the component handle the error
  }
  */ }
async function executeWorkflowWithUserInputs(payload) {
    try {
        console.log(`Executing workflow with user inputs:`, payload);
        // Get the access token based on environment
        let accessToken;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Server-side
            accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
        }
        // Prepare headers with authentication
        const headers = {
            "Content-Type": "application/json"
        };
        // Add Authorization header if we have a token
        if (accessToken) {
            headers["Authorization"] = `Bearer ${accessToken}`;
        }
        // Send the request to the execution endpoint
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOW_EXECUTION.EXECUTE, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(payload)
        });
        if (!response.ok) {
            let errorDetail = "Unknown execution error";
            try {
                // Try to parse more detailed error from response body
                const errorData = await response.json();
                errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
            } catch (parseError) {
                // If parsing fails, use the status text
                errorDetail = response.statusText;
            }
            throw new Error(`Execution failed: ${response.status} ${errorDetail}`);
        }
        const resultData = await response.json();
        console.log("Received execution result:", resultData);
        return {
            success: true,
            correlationId: resultData.correlationId,
            message: resultData.message || "Workflow execution started successfully"
        };
    } catch (error) {
        console.error("Error executing workflow with user inputs:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
async function sendApprovalDecision(correlationId, decision) {
    try {
        console.log(`Sending ${decision} decision for correlation ID: ${correlationId}`);
        // Direct call to the external API, not going through our backend
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOW_EXECUTION.APPROVE, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getClientAccessToken"])()}`
            },
            body: JSON.stringify({
                correlationId,
                decision
            })
        });
        if (!response.ok) {
            let errorDetail = "Unknown approval error";
            try {
                // Try to parse more detailed error from response body
                const errorData = await response.json();
                errorDetail = errorData.detail || errorData.message || JSON.stringify(errorData);
            } catch (parseError) {
                // If parsing fails, use the status text
                errorDetail = response.statusText;
            }
            throw new Error(`Approval failed: ${response.status} ${errorDetail}`);
        }
        const resultData = await response.json();
        console.log("Received approval result:", resultData);
        return {
            success: true,
            message: resultData.message || `Workflow ${decision}d successfully`
        };
    } catch (error) {
        console.error("Error sending approval decision:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
}}),
"[project]/src/lib/sseClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// No import needed for standard EventSource - it's built into modern browsers
// Removed: import { EventSourcePolyfill } from 'event-source-polyfill';
__turbopack_context__.s({
    "SSEClient": (()=>SSEClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiConfig.ts [app-ssr] (ecmascript)");
;
class SSEClient {
    eventSource = null;
    correlationId;
    options;
    config;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    reconnectDelay = 2000;
    url;
    // --- State Flags ---
    // Flag prevents reconnect if user explicitly called close()
    explicitlyClosed = false;
    // Flag prevents reconnect if a designated server error *event* was received (e.g., event: error)
    receivedServerErrorEvent = false;
    // Flag prevents reconnect if a message indicated a terminal workflow status
    receivedTerminalStatus = false;
    constructor(correlationId, options = {}, config = {}){
        this.correlationId = correlationId;
        this.options = options;
        // Construct the URL - ensure backend handles auth via cookies or secure URL params if needed
        const baseUrl = config.baseUrl || process.env.NEXT_PUBLIC_SSE_URL || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].WORKFLOW_EXECUTION.STREAM;
        this.url = `${baseUrl}/${this.correlationId}`;
        this.config = {
            ...config,
            baseUrl: baseUrl
        };
        if (!this.correlationId) {
            console.error("SSEClient: Correlation ID is required.");
            // Consider throwing an error or handling more robustly
            if (this.options.onError) {
                // Use request time for the error if available, otherwise fallback
                const errorTime = new Date().toISOString(); // Example timestamp
                this.options.onError(new Error(`[${errorTime}] Correlation ID is required`));
            }
        }
    }
    connect() {
        const connectTime = new Date().toISOString(); // Example timestamp
        if (!this.correlationId) {
            console.error(`[${connectTime}] SSEClient: Cannot connect without Correlation ID.`);
            if (this.options.onError) {
                this.options.onError(new Error(`[${connectTime}] Cannot connect without Correlation ID`));
            }
            return;
        }
        if (this.isConnected()) {
            console.log(`[${connectTime}] SSEClient: Connection attempt skipped. Already connected.`);
            return;
        }
        // --- Reset flags for the new connection attempt ---
        this.reset();
        // Close any existing connection cleanly before creating a new one
        this.closeInternal(false); // Don't trigger onClose callback here
        try {
            console.log(`[${connectTime}] SSEClient: Connecting to ${this.url} for correlation ID: ${this.correlationId}`);
            // --- Use standard EventSource ---
            // Removed polyfill-specific options like headers
            this.eventSource = new EventSource(this.url);
            // Note: If you require authentication and can't use cookies,
            // you might need to include tokens in the URL (less secure)
            // e.g., new EventSource(`${this.url}?token=YOUR_TOKEN`)
            // Ensure your server supports and secures this method.
            console.log(`[${connectTime}] SSEClient: EventSource instance created. Waiting for connection to open...`);
            // --- Event Handlers ---
            this.eventSource.onopen = ()=>{
                const openTime = new Date().toISOString();
                console.log(`[${openTime}] SSEClient: Connection opened successfully for ${this.correlationId}.`);
                this.reconnectAttempts = 0; // Reset reconnect attempts on successful open
                if (this.options.onOpen) {
                    this.options.onOpen();
                }
            };
            // --- Generic Message Handler ---
            this.eventSource.onmessage = (event)=>{
                const messageTime = new Date().toISOString();
                // console.log(`[${messageTime}] SSEClient: Received generic message:`, event.data);
                try {
                    const data = JSON.parse(event.data);
                    // console.log(`[${messageTime}] SSEClient: Parsed generic message data:`, data);
                    // --- Check for Terminal Workflow Status ---
                    this.checkAndHandleTerminalStatus(data, "generic message");
                    if (this.options.onMessage) {
                        this.options.onMessage(event); // Pass the raw event
                    }
                } catch (error) {
                    console.error(`[${messageTime}] SSEClient: Error parsing generic message data:`, error, event.data);
                // Optionally trigger onError for parsing failures
                // if (this.options.onError) this.options.onError(new Error(`[${messageTime}] Failed to parse message: ${error}`));
                }
            };
            // --- Connection Error Handler ---
            this.eventSource.onerror = (errorEvent)=>{
                const errorTime = new Date().toISOString();
                console.error(`[${errorTime}] SSEClient: Connection error occurred.`, errorEvent);
                const currentReadyState = this.eventSource?.readyState;
                const readyStateText = this.getReadyStateText(currentReadyState);
                console.log(`[${errorTime}] SSEClient: EventSource readyState on error: ${readyStateText}`);
                // Store flags before closing, as closeInternal might reset them indirectly
                const wasExplicitlyClosed = this.explicitlyClosed;
                const hadServerErrorEvent = this.receivedServerErrorEvent;
                const hadTerminalStatus = this.receivedTerminalStatus;
                // Close the faulty EventSource instance
                this.closeInternal(true); // Mark as closed due to error
                // --- Reconnection Logic ---
                if (wasExplicitlyClosed) {
                    console.log(`[${errorTime}] SSEClient: Connection error occurred after explicit close. No reconnection attempt.`);
                    return;
                }
                if (hadServerErrorEvent) {
                    console.log(`[${errorTime}] SSEClient: Connection error occurred after receiving a server error event. No reconnection attempt.`);
                    if (this.options.onClose) this.options.onClose(true); // Indicate closed due to error
                    return;
                }
                if (hadTerminalStatus) {
                    console.log(`[${errorTime}] SSEClient: Connection error occurred after receiving a terminal workflow status. No reconnection attempt.`);
                    if (this.options.onClose) this.options.onClose(true); // Indicate closed due to terminal status
                    return;
                }
                // --- Attempt Reconnection ---
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`[${errorTime}] SSEClient: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay / 1000}s...`);
                    // Use arrow function to maintain 'this' context
                    setTimeout(()=>this.connect(), this.reconnectDelay);
                } else {
                    console.error(`[${errorTime}] SSEClient: Max reconnect attempts (${this.maxReconnectAttempts}) reached for ${this.correlationId}. Giving up.`);
                    if (this.options.onError) {
                        this.options.onError(new Error(`[${errorTime}] Failed to reconnect after ${this.maxReconnectAttempts} attempts.`));
                    }
                    if (this.options.onClose) this.options.onClose(true); // Indicate closed due to failure
                }
                // Call user's onError *after* handling reconnection logic attempt or decision
                if (this.options.onError) {
                    this.options.onError(new Error(`[${errorTime}] SSE connection error (readyState: ${readyStateText})`));
                }
            };
            // --- Custom Event Listeners ---
            const terminalErrorEventTypes = [
                "error",
                "fatal_error"
            ]; // Events indicating server errors that should stop reconnects
            const listenedEventTypes = [
                // Add all event types your server might send
                "connected",
                "log",
                "warning",
                "error",
                "info",
                "success",
                "complete",
                "fatal_error"
            ];
            listenedEventTypes.forEach((eventType)=>{
                if (!this.eventSource) return; // Type guard
                this.eventSource.addEventListener(eventType, (event)=>{
                    const eventTime = new Date().toISOString();
                    const messageEvent = event; // Assume MessageEvent
                    // console.log(`[${eventTime}] SSEClient: Received event [${eventType}]:`, messageEvent.data);
                    try {
                        const data = messageEvent.data ? JSON.parse(messageEvent.data) : null;
                        // console.log(`[${eventTime}] SSEClient: Parsed event [${eventType}] data:`, data);
                        // Check if this event type itself signals a terminal server error
                        if (terminalErrorEventTypes.includes(eventType)) {
                            console.warn(`[${eventTime}] SSEClient: Received terminal server error event [${eventType}]. Flagging to prevent reconnect.`);
                            this.receivedServerErrorEvent = true;
                        // Optionally close immediately
                        // setTimeout(() => this.close(), 50);
                        }
                        // --- Check for Terminal Workflow Status within the event data ---
                        this.checkAndHandleTerminalStatus(data, `event [${eventType}]`);
                        // Call specific custom event handler
                        if (this.options.onCustomEvent) {
                            this.options.onCustomEvent(eventType, data);
                        }
                    } catch (error) {
                        console.error(`[${eventTime}] SSEClient: Error parsing event [${eventType}] data:`, error, messageEvent.data);
                    // Optionally trigger onError
                    // if (this.options.onError) this.options.onError(new Error(`[${eventTime}] Failed to parse ${eventType} event: ${error}`));
                    }
                });
            });
        } catch (error) {
            // Catches errors during `new EventSource()` instantiation (e.g., network error, invalid URL immediately)
            const initErrorTime = new Date().toISOString();
            console.error(`[${initErrorTime}] SSEClient: Failed to create EventSource instance:`, error);
            this.eventSource = null; // Ensure it's null
            if (this.options.onError) {
                this.options.onError(error instanceof Error ? error : new Error(`[${initErrorTime}] Failed to initialize SSE connection`));
            }
        // Decide if instantiation errors should trigger reconnect attempts
        // Usually they indicate config issues, but you could add retry logic here too:
        // if (this.reconnectAttempts < this.maxReconnectAttempts) { ... } else { onClose(true); }
        }
    }
    // --- Helper to check for terminal status ---
    checkAndHandleTerminalStatus(data, context) {
        const checkTime = new Date().toISOString();
        // Check if data is an object and has the workflow_status property
        if (data && typeof data === "object" && data.hasOwnProperty("workflow_status")) {
            const status = data.workflow_status;
            const terminalStatuses = [
                "completed",
                "cancelled",
                "failed"
            ]; // Define terminal statuses
            // Check for approval status and dispatch a custom event
            // Only trigger approval UI when we have the specific criteria:
            // 1. workflow_status is "waiting_for_approval"
            // 2. approval_required is true
            // 3. status is "paused"
            if (status?.toLowerCase() === "waiting_for_approval" && data.approval_required === true && data.status === "paused") {
                console.log(`[${checkTime}] SSEClient: Received valid approval request in ${context}.`, data);
                // Only proceed if we have a valid node ID
                if (data.node_id && data.node_id !== "unknown") {
                    // Import dynamically to avoid circular dependencies
                    __turbopack_context__.r("[project]/src/lib/approvalUtils.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i).then(({ dispatchApprovalNeededEvent })=>{
                        // Use the centralized function to dispatch the event
                        dispatchApprovalNeededEvent(this.correlationId, data.node_id, data.node_name || data.node_id);
                        // Also set a direct flag on the window for immediate access
                        // This helps with race conditions where the event might not be processed
                        window._pendingApproval = {
                            correlationId: this.correlationId,
                            nodeId: data.node_id,
                            nodeName: data.node_name || data.node_id,
                            timestamp: Date.now()
                        };
                        // Force a UI update by dispatching a direct event
                        setTimeout(()=>{
                            console.log(`[${checkTime}] SSEClient: Dispatching direct approval-ui-update event`);
                            window.dispatchEvent(new CustomEvent("approval-ui-update"));
                        }, 500);
                    }).catch((err)=>{
                        console.error(`[${checkTime}] SSEClient: Error importing approvalUtils:`, err);
                        // Fallback to direct event dispatch if import fails
                        const timestamp = Date.now();
                        const event = new CustomEvent("workflow-approval-needed", {
                            detail: {
                                correlationId: this.correlationId,
                                nodeId: data.node_id,
                                nodeName: data.node_name || data.node_id,
                                timestamp: timestamp,
                                // Add a force flag to ensure this event is processed
                                force: true
                            }
                        });
                        window.dispatchEvent(event);
                        // Also set the window flag
                        window._pendingApproval = {
                            correlationId: this.correlationId,
                            nodeId: data.node_id,
                            nodeName: data.node_name || data.node_id,
                            timestamp: timestamp
                        };
                    });
                } else {
                    console.log(`[${checkTime}] SSEClient: Skipping approval event due to missing node ID`);
                }
                // Do NOT close the connection - keep streaming
                return;
            } else if (status?.toLowerCase() === "waiting_for_approval") {
                // Log that we received a waiting_for_approval status but it didn't meet our criteria
                console.log(`[${checkTime}] SSEClient: Received waiting_for_approval status but it didn't meet criteria for approval UI:`, data);
            }
            if (terminalStatuses.includes(status?.toLowerCase())) {
                console.log(`[${checkTime}] SSEClient: Received terminal workflow status "${status}" in ${context}. Flagging to prevent reconnect.`);
                this.receivedTerminalStatus = true;
                // Log the status for debugging
                console.log(`[${checkTime}] SSEClient: Workflow status: ${status}, Node ID: ${data.node_id || "unknown"}, Result: ${data.result || "none"}`);
                // Close the connection proactively after receiving a terminal status
                console.log(`[${checkTime}] SSEClient: Closing connection shortly due to terminal status "${status}".`);
                // Dispatch a custom event to notify listeners that a terminal status was received
                // This can be used to update UI elements
                const event = new CustomEvent("workflow-terminal-status", {
                    detail: {
                        status,
                        nodeId: data.node_id,
                        result: data.result
                    }
                });
                window.dispatchEvent(event);
                // Force close the connection immediately for complete status
                if (status?.toLowerCase() === "complete") {
                    console.log(`[${checkTime}] SSEClient: Immediately closing connection for "complete" status.`);
                    this.close();
                } else {
                    // For other terminal statuses, close after a short delay to allow any final messages to be received
                    setTimeout(()=>this.close(), 500);
                }
            }
        }
    }
    // --- Public close method ---
    close() {
        const closeTime = new Date().toISOString();
        // Only log if we actually have something to close
        if (this.eventSource) {
            console.log(`[${closeTime}] SSEClient: Explicitly closing connection for ${this.correlationId}...`);
            this.explicitlyClosed = true; // Set flag to prevent reconnects by onerror
            // Stop any scheduled reconnection attempts
            this.reconnectAttempts = this.maxReconnectAttempts;
            this.closeInternal(false); // Close the connection, not marked as an error closure
        } else {
            console.log(`[${closeTime}] SSEClient: Close called, but no active connection.`);
        }
    }
    // --- Internal close method ---
    closeInternal(isDueToError) {
        if (this.eventSource) {
            // Close the EventSource
            this.eventSource.close();
            this.eventSource = null;
            // Call onClose only if:
            // 1. It was an explicit close (isDueToError=false AND explicitlyClosed=true)
            // 2. It was due to an error that won't be retried (handled in onerror logic)
            // Avoid calling onClose here if an error occurred and a retry might happen.
            // The onerror handler will call onClose if max retries are reached or retry is skipped.
            if (!isDueToError && this.explicitlyClosed) {
                if (this.options.onClose) {
                    // Pass 'false' because it wasn't an *unexpected* error closure
                    this.options.onClose(false);
                }
            }
        }
    }
    isConnected() {
        // Also check explicitlyClosed flag, connection might be closing but state not yet CLOSED
        return this.eventSource !== null && this.eventSource.readyState === EventSource.OPEN && !this.explicitlyClosed;
    }
    getReadyState() {
        return this.eventSource?.readyState ?? null;
    }
    getReadyStateText(readyState) {
        switch(readyState){
            case EventSource.CONNECTING:
                return "CONNECTING (0)";
            case EventSource.OPEN:
                return "OPEN (1)";
            case EventSource.CLOSED:
                return "CLOSED (2)";
            case null:
                return "NULL (Not Initialized)";
            case undefined:
                return "UNDEFINED (Error State)"; // Should ideally not happen
            default:
                return `UNKNOWN (${readyState})`;
        }
    }
    // Reset all internal state flags to allow reconnection
    reset() {
        console.log(`SSEClient: Resetting internal state flags for ${this.correlationId}`);
        this.explicitlyClosed = false;
        this.receivedServerErrorEvent = false;
        this.receivedTerminalStatus = false;
        this.reconnectAttempts = 0;
    }
}
}}),
"[project]/src/lib/approvalMigration.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions for migrating approval flags from config to definition
 */ __turbopack_context__.s({
    "migrateApprovalFlagToDefinition": (()=>migrateApprovalFlagToDefinition)
});
function migrateApprovalFlagToDefinition(nodes) {
    return nodes.map((node)=>{
        // If the node has requires_approval in config, move it to definition
        if (node.data?.config?.requires_approval !== undefined) {
            console.log(`Migrating requires_approval flag for node ${node.id} from config to definition`);
            const newNode = {
                ...node,
                data: {
                    ...node.data,
                    definition: node.data.definition ? {
                        ...node.data.definition,
                        requires_approval: node.data.config.requires_approval,
                        // Ensure required properties are present with non-undefined values
                        name: node.data.definition.name || "",
                        display_name: node.data.definition.display_name || "",
                        description: node.data.definition.description || "",
                        category: node.data.definition.category || "",
                        icon: node.data.definition.icon || "",
                        beta: node.data.definition.beta || false,
                        inputs: node.data.definition.inputs || [],
                        outputs: node.data.definition.outputs || [],
                        is_valid: node.data.definition.is_valid || false,
                        path: node.data.definition.path || ""
                    } : undefined
                }
            };
            // Remove from config
            const newConfig = {
                ...node.data.config
            };
            delete newConfig.requires_approval;
            newNode.data.config = newConfig;
            return newNode;
        }
        return node;
    });
}
}}),
"[project]/src/lib/workflowUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/workflowUtils.ts
__turbopack_context__.s({
    "convertWorkflowJsonToReactFlow": (()=>convertWorkflowJsonToReactFlow),
    "loadWorkflowFromJson": (()=>loadWorkflowFromJson),
    "saveWorkflowToFile": (()=>saveWorkflowToFile),
    "validateWorkflowJson": (()=>validateWorkflowJson)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$approvalMigration$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/approvalMigration.ts [app-ssr] (ecmascript)");
;
function saveWorkflowToFile(nodes, edges, filename = "workflow.json", workflowName = "Untitled Workflow") {
    // Map nodes to desired structure - preserve all node data for proper workflow restoration
    const mappedNodes = nodes.map((node)=>{
        // Create a clean copy of the node with all its properties
        return {
            id: node.id,
            type: node.type,
            position: node.position,
            data: node.data
        };
    });
    // Map edges - preserve all edge data for proper workflow restoration
    const mappedEdges = edges.map((edge)=>{
        return {
            id: edge.id,
            source: edge.source,
            sourceHandle: edge.sourceHandle,
            target: edge.target,
            targetHandle: edge.targetHandle
        };
    });
    // Create a simple JSON structure with workflow name, nodes and edges as the main keys
    const workflowToSave = {
        workflow_name: workflowName,
        nodes: mappedNodes,
        edges: mappedEdges
    };
    try {
        const jsonString = JSON.stringify(workflowToSave, null, 2); // Pretty print
        const blob = new Blob([
            jsonString
        ], {
            type: "application/json"
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename; // Use the provided filename
        document.body.appendChild(link); // Required for Firefox
        link.click();
        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        console.log(`Workflow saved successfully as ${filename}.`);
    } catch (error) {
        console.error("Failed to save workflow:", error);
    }
}
function validateWorkflowJson(workflowData) {
    // Check if the data is an object
    if (!workflowData || typeof workflowData !== "object") {
        return {
            isValid: false,
            error: "Workflow data must be a valid JSON object"
        };
    }
    // Check workflow_name if present (not required)
    if (workflowData.workflow_name && typeof workflowData.workflow_name !== "string") {
        return {
            isValid: false,
            error: "Workflow name must be a string"
        };
    }
    // Check for required top-level properties
    if (!Array.isArray(workflowData.nodes)) {
        return {
            isValid: false,
            error: 'Workflow must contain a "nodes" array'
        };
    }
    // Check for edges array (previously called connections)
    if (!Array.isArray(workflowData.edges) && !Array.isArray(workflowData.connections)) {
        return {
            isValid: false,
            error: 'Workflow must contain an "edges" array'
        };
    }
    // For backward compatibility, use edges if available, otherwise use connections
    const edgesArray = workflowData.edges || workflowData.connections;
    // Validate each node
    for(let i = 0; i < workflowData.nodes.length; i++){
        const node = workflowData.nodes[i];
        // Check for required node properties
        if (!node.id) {
            return {
                isValid: false,
                error: `Node at index ${i} is missing an "id" property`
            };
        }
        if (!node.type) {
            return {
                isValid: false,
                error: `Node at index ${i} is missing a "type" property`
            };
        }
        if (!node.position || typeof node.position !== "object" || typeof node.position.x !== "number" || typeof node.position.y !== "number") {
            return {
                isValid: false,
                error: `Node at index ${i} has an invalid "position" property`
            };
        }
        if (!node.data || typeof node.data !== "object") {
            return {
                isValid: false,
                error: `Node at index ${i} is missing a valid "data" property`
            };
        }
        // Check for required data properties
        if (!node.data.type) {
            return {
                isValid: false,
                error: `Node at index ${i} is missing a "data.type" property`
            };
        }
        if (!node.data.label) {
            return {
                isValid: false,
                error: `Node at index ${i} is missing a "data.label" property`
            };
        }
        if (!node.data.definition || typeof node.data.definition !== "object") {
            return {
                isValid: false,
                error: `Node at index ${i} is missing a valid "data.definition" property`
            };
        }
    }
    // Validate each edge (previously called connection)
    for(let i = 0; i < edgesArray.length; i++){
        const edge = edgesArray[i];
        // Check for required edge properties
        if (!edge.id) {
            return {
                isValid: false,
                error: `Edge at index ${i} is missing an "id" property`
            };
        }
        if (!edge.source) {
            return {
                isValid: false,
                error: `Edge at index ${i} is missing a "source" property`
            };
        }
        if (!edge.target) {
            return {
                isValid: false,
                error: `Edge at index ${i} is missing a "target" property`
            };
        }
        // Check that source and target nodes exist
        const sourceExists = workflowData.nodes.some((node)=>node.id === edge.source);
        if (!sourceExists) {
            return {
                isValid: false,
                error: `Edge at index ${i} references a non-existent source node: ${edge.source}`
            };
        }
        const targetExists = workflowData.nodes.some((node)=>node.id === edge.target);
        if (!targetExists) {
            return {
                isValid: false,
                error: `Edge at index ${i} references a non-existent target node: ${edge.target}`
            };
        }
    }
    // If we've made it this far, the workflow is valid
    return {
        isValid: true,
        data: workflowData
    };
}
function convertWorkflowJsonToReactFlow(workflowData) {
    console.log("Converting workflow data to ReactFlow format:", workflowData);
    // Check if we're dealing with a wrapped workflow structure
    // This happens when loading from the builder URL where the data is in workflow_data
    if (workflowData.workflow_data && workflowData.workflow_data.nodes) {
        console.log("Detected wrapped workflow structure, unwrapping...");
        workflowData = workflowData.workflow_data;
    }
    // Map nodes directly - they should already be in the correct format
    const nodes = workflowData.nodes.map((node)=>{
        console.log("Processing node:", node);
        // Special handling for StartNode to ensure its configuration is preserved
        if (node.data && node.data.originalType === "StartNode") {
            console.log("Found StartNode, ensuring config is preserved:", node);
            // Ensure the node has a config object
            if (!node.data.config) {
                node.data.config = {};
            }
            // Ensure the config has a collected_parameters object
            if (!node.data.config.collected_parameters) {
                node.data.config.collected_parameters = {};
            }
            // Ensure all parameters in collected_parameters have the required property set
            // This is critical for pre-built workflows where the required property might not be set
            if (node.data.config.collected_parameters) {
                Object.keys(node.data.config.collected_parameters).forEach((paramId)=>{
                    const param = node.data.config.collected_parameters[paramId];
                    // If required is undefined, set it to true (consider required unless explicitly false)
                    if (param.required === undefined) {
                        console.log(`Setting required=true for parameter ${paramId} in StartNode`);
                        param.required = true;
                    }
                });
            }
            console.log("StartNode config after ensuring structure:", node.data.config);
        }
        return {
            id: node.id,
            type: node.type || "WorkflowNode",
            position: node.position,
            data: node.data,
            // Include any other properties that might be needed
            width: node.width,
            height: node.height,
            selected: false,
            dragging: false
        };
    });
    // Map edges (previously called connections)
    // For backward compatibility, use edges if available, otherwise use connections
    const edgesArray = workflowData.edges || workflowData.connections;
    const edges = edgesArray.map((edge)=>{
        console.log("Processing edge:", edge);
        return {
            id: edge.id,
            source: edge.source,
            sourceHandle: edge.sourceHandle,
            target: edge.target,
            targetHandle: edge.targetHandle,
            type: edge.type || "default",
            // Include any other properties that might be needed
            animated: true
        };
    });
    console.log("Converted nodes:", nodes);
    console.log("Converted edges:", edges);
    // Migrate approval flags from config to definition
    const migratedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$approvalMigration$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["migrateApprovalFlagToDefinition"])(nodes);
    console.log("Migrated nodes:", migratedNodes);
    return {
        nodes: migratedNodes,
        edges
    };
}
function loadWorkflowFromJson(workflowData) {
    // First validate the workflow data
    const validationResult = validateWorkflowJson(workflowData);
    if (!validationResult.isValid) {
        return validationResult;
    }
    try {
        // Convert the validated data to ReactFlow format
        const reactFlowData = convertWorkflowJsonToReactFlow(workflowData);
        return {
            isValid: true,
            data: reactFlowData
        };
    } catch (error) {
        return {
            isValid: false,
            error: error instanceof Error ? error.message : "Failed to convert workflow data to ReactFlow format"
        };
    }
}
}}),
"[project]/src/lib/workflowApi.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Workflow API Module
 *
 * This module re-exports functions from the feature-specific API module
 * to maintain backward compatibility.
 *
 * IMPORTANT: This file is deprecated. Please import directly from:
 * @/app/(features)/workflows/api
 */ // Re-export everything from the feature-specific API module
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "fetchWorkflowFromBuilderUrl": (()=>fetchWorkflowFromBuilderUrl)
});
// Also export a default object for easier imports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(features)/workflows/api.ts [app-ssr] (ecmascript)");
// Add the missing fetchWorkflowFromBuilderUrl function that's not in the feature API
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/axios.ts [app-ssr] (ecmascript)");
;
;
;
async function fetchWorkflowFromBuilderUrl(url) {
    try {
        // For external URLs, we need to use the external API instance (no auth)
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["externalApi"].get(url, {
            headers: {
                "Content-Type": "application/json"
            }
        });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`);
        }
        throw error;
    }
}
const __TURBOPACK__default__export__ = {
    fetchWorkflowsByUser: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchWorkflowsByUser"],
    createEmptyWorkflow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createEmptyWorkflow"],
    fetchWorkflowFromBuilderUrl,
    fetchWorkflowById: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchWorkflowById"]
};
}}),
"[project]/src/lib/workflowApi.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$features$292f$workflows$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(features)/workflows/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/axios.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$workflowApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/workflowApi.ts [app-ssr] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=src_lib_abeaf831._.js.map